
/**
  ******************************************************************************
  * @file    mc_parameters.h
  * <AUTHOR> Control SDK Team, ST Microelectronics
  * @brief   This file provides declarations of HW parameters specific to the
  *          configuration of the subsystem.
  *
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; Copyright (c) 2019 STMicroelectronics.
  * All rights reserved.</center></h2>
  *
  * This software component is licensed by ST under Ultimate Liberty license
  * SLA0044, the "License"; You may not use this file except in compliance with
  * the License. You may obtain a copy of the License at:
  *                             www.st.com/SLA0044
  *
  ******************************************************************************
  */
#ifndef MC_PARAMETERS_H
#define MC_PARAMETERS_H

#include "r3_2_g4xx_pwm_curr_fdbk.h"

/* USER CODE BEGIN Additional include */

/* USER CODE END Additional include */

extern R3_2_Params_t R3_2_ParamsM1;

/* USER CODE BEGIN Additional extern */

/* USER CODE END Additional extern */

#endif /* MC_PARAMETERS_H */
/******************* (C) COPYRIGHT 2019 STMicroelectronics *****END OF FILE****/
