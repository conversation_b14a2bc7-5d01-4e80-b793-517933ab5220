/*
 * Copyright (c) 2006-2018, RT-Thread Development Team
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Change Logs:
 * Date           Author       Notes
 * 2019-06-21     flybreak     first version
 */

#include <rtthread.h>

#include "mb.h"
#include "user_mb_app.h"
#include "uApp.h"

#ifdef PKG_MODBUS_SLAVE_SAMPLE
#define SLAVE_ADDR      (0x04 + pvpb.io.inv_addr1 + (pvpb.io.inv_addr2<<1))
#define PORT_NUM        MB_SLAVE_USING_PORT_NUM
#define PORT_BAUDRATE   MB_SLAVE_USING_PORT_BAUDRATE
#else
#define SLAVE_ADDR      0x01
#define PORT_NUM        2
#define PORT_BAUDRATE   115200
#endif

#define PORT_PARITY     MB_PAR_EVEN

#define MB_POLL_THREAD_PRIORITY  10
#define MB_SEND_THREAD_PRIORITY  RT_THREAD_PRIORITY_MAX - 1

#define MB_POLL_CYCLE_MS 200

extern USHORT usSRegHoldBuf[S_REG_HOLDING_NREGS];
extern USHORT usSRegWriteHoldBuf[S_REG_HOLDING_NREGS];

void mb_reg_mrsw_write( uint8_t *data,          // user data
                            uint8_t reg_index,  // reg index to write  ,from 0
                            uint16_t size       // write reg nums
)
{
    
    rt_memcpy(&usSRegHoldBuf[reg_index],data,size*2); 
}

void mb_reg_mwsr_read( uint8_t *data,           // user data
                            uint8_t reg_index,  // reg index to read,from 0
                            uint16_t size       // read reg nums
)
{
    rt_memcpy(data,&usSRegWriteHoldBuf[reg_index],size*2); 
}

static void mb_slave_poll(void *parameter)
{
    while(pvpb.bit.io_init == 0)
    {
        rt_thread_delay(RT_TICK_PER_SECOND / 5);
    }
    
    if (rt_strstr(parameter, "RTU"))
    {
#ifdef PKG_MODBUS_SLAVE_RTU
        eMBInit(MB_RTU, SLAVE_ADDR, PORT_NUM, PORT_BAUDRATE, PORT_PARITY);
#else
        rt_kprintf("Error: Please open RTU mode first");
#endif
    }
    else if (rt_strstr(parameter, "ASCII"))
    {
#ifdef PKG_MODBUS_SLAVE_ASCII
        eMBInit(MB_ASCII, SLAVE_ADDR, PORT_NUM, PORT_BAUDRATE, PORT_PARITY);
#else
        rt_kprintf("Error: Please open ASCII mode first");
#endif
    }
    else if (rt_strstr(parameter, "TCP"))
    {
#ifdef PKG_MODBUS_SLAVE_TCP
        eMBTCPInit(0);
#else
        rt_kprintf("Error: Please open TCP mode first");
#endif
    }
    else
    {
        rt_kprintf("Error: unknown parameter");
    }
    eMBEnable();
    
    while (1)
    {
        eMBPoll();
    }
}

int mb_slave_poll_create(void)
{
    rt_thread_t tid1 = RT_NULL;
    
    tid1 = rt_thread_create("mb_s_poll", mb_slave_poll, "RTU", 1024, MB_POLL_THREAD_PRIORITY, 10);
    if (tid1 != RT_NULL)
    {
        rt_thread_startup(tid1);
    }
}
INIT_APP_EXPORT(mb_slave_poll_create);

static int mb_slave_samlpe(int argc, char **argv)
{
    static rt_uint8_t is_init = 0;
    rt_thread_t tid1 = RT_NULL;

    if (is_init > 0)
    {
        rt_kprintf("sample is running\n");
        return -RT_ERROR;
    }
    if (argc < 2)
    {
        rt_kprintf("Usage: mb_slave_samlpe RTU/ASCII/TCP\n");
        return -1;
    }

    tid1 = rt_thread_create("md_s_poll", mb_slave_poll, argv[1], 1024, MB_POLL_THREAD_PRIORITY, 10);
    if (tid1 != RT_NULL)
    {
        rt_thread_startup(tid1);
    }
    else
    {
        goto __exit;
    }

    is_init = 1;
    return RT_EOK;

__exit:
    if (tid1)
        rt_thread_delete(tid1);

    return -RT_ERROR;
}
MSH_CMD_EXPORT(mb_slave_samlpe, run a modbus slave sample);
