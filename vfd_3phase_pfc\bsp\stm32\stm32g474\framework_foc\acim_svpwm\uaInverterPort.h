
/******************** (C) COPYRIGHT 2022 BSM ********************
* File Name          : uaInverter.h
* Author             : 
* Version            : V1.0
* Date               : 
* Description        : 
********************************************************************************/

#ifndef __UA_INVERTER_PORT_H__
#define __UA_INVERTER_PORT_H__



float Inv_GetFreqRef(void);
float Inv_GetFreqNow(void);
float Inv_GetFreqAllow(void);
void Inv_SetFreqRef(float ref);
void Inv_PushSetFreqRef(float ref);
void Inv_PopSetFreqRef(void);
float Inv_GetPushFreqRef(void);
void Inv_FreqRampStop(uint8_t bit);
void Inv_FreqRampStart(uint8_t bit);
    
#endif

