
/******************** (C) COPYRIGHT 2022 bus-lan ********************
* File Name          : Bsp_fullbridge_pwm.c
* Author             :
* Version            : V1.0
* Date               :
* Description        :
********************************************************************************/
#include <rtthread.h>
#include <stm32g4xx.h>
#include "uaFullbridge.h"
#include "udPwm.h"
#include "uApp.h"

static uint8_t PWM_STATE = 0;
extern TIM_HandleTypeDef htim20;
TIM_HandleTypeDef *phtimx = &htim20;
void setpwm(uint16_t pwmper, uint8_t enble);

#define TIMxCCER_MASK_CH12        ((uint16_t)  ( LL_TIM_CHANNEL_CH1|LL_TIM_CHANNEL_CH1N|\
                                                 LL_TIM_CHANNEL_CH2|LL_TIM_CHANNEL_CH2N))
                                                 
/*******************************************************************************
* Function Name  : void DcdcPwmSetCompare(int pwm)
* Description    :
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void DcdcPwmSetCompare(int modulation)
{
    rt_base_t level;

//    if(vfd.ctrl.tim8_mode != TIM8_MODE_BOOST)
//        return;

    if (modulation < 0)    modulation = 0;
    if (modulation > BOOST_PWM_MAX_DUTY) modulation = BOOST_PWM_MAX_DUTY;

    level = rt_hw_interrupt_disable();

    if(modulation == 0)
    {
        __HAL_TIM_SET_COMPARE(phtimx, TIM_CHANNEL_1, 0);
        __HAL_TIM_SET_COMPARE(phtimx, TIM_CHANNEL_2, PWM_PERIOD - 1);
    }
    else
    {
        __HAL_TIM_SET_COMPARE(phtimx, TIM_CHANNEL_1, PWM_PERIOD * modulation / 1000);
        __HAL_TIM_SET_COMPARE(phtimx, TIM_CHANNEL_2, PWM_PERIOD - PWM_PERIOD * modulation / 1000);
    }

    rt_hw_interrupt_enable(level);

    boost.modulation_out = modulation;
     
}

/*******************************************************************************
* Function Name  : void DcdcPwmStart(void)
* Description    :
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
uint16_t tim2_fix_cnt = 12;
uint16_t tim3_fix_cnt = 19;
void DcdcPwmStart(void)
{
//    if(vfd.ctrl.tim8_mode != TIM8_MODE_BOOST)
//    {
//        PWM_STATE = 0;
//        return;
//    }
    
    if (PWM_STATE == 0)
    {
        rt_base_t level;
        PWM_STATE = 1;
        
        HAL_TIM_Base_Start(phtimx);
        LL_TIM_EnableIT_BRK(phtimx->Instance);
        /** @note 
        Bit 15 MOE: Main output enable
        This bit is cleared asynchronously by hardware as soon as one of the break inputs is active 
        (tim_brk or tim_brk2). It is set by software or automatically depending on the AOE bit. It is 
        acting only on the channels which are configured in output. 
        0:In response to a break 2 event. OC and OCN outputs are disabled
        In response to a break event or if MOE is written to 0: OC and OCN outputs are disabled or 
        forced to idle state depending on the OSSI bit.
        1:OC and OCN outputs are enabled if their respective enable bits are set (CCxE, CCxNE in 
        TIMx_CCER register).
        See OC/OCN enable description for more details (Section 28.6.11: TIMx capture/compare 
        enable register (TIMx_CCER)(x = 1, 8, 20)).
        */
        __HAL_TIM_MOE_ENABLE(phtimx);
        /* Enable PWM channel */
        LL_TIM_CC_EnableChannel(phtimx->Instance, TIMxCCER_MASK_CH12);
        
        boost.start_pwm = 1;
    }
}

void DcdcTimClearCounterPwm(void)
{
    DcdcPwmSetCompare(0);
}

/*******************************************************************************
* Function Name  : void DcdcPwmStop(uint16_t _val)
* Description    :
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
uint8_t boost_timer_init_flag;
void DcdcPwmStop(void)
{
    if (boost_timer_init_flag &&
            PWM_STATE == 1)
    {
        PWM_STATE = 0;
        LL_TIM_DisableIT_BRK(phtimx->Instance);
        HAL_TIM_Base_Stop(phtimx);
        /* It forces inactive level on TIMx CHy and CHyN */
        LL_TIM_CC_DisableChannel(phtimx->Instance, TIMxCCER_MASK_CH12);

        DcdcPwmSetCompare(0);

        __HAL_TIM_SET_COUNTER(phtimx, 0);
        BOOST_FS_OUT(0);
        boost.start_pwm = 0;
    }
}

static void tim20_break_isr(void)
{
    static uint32_t tick = 0;
    /* over current .*/
    LL_TIM_DisableIT_BRK(TIM20);

    hardware_irq_pin.f_ipm_boost_cnt++;
    hardware_irq_pin.f_tim20_breakin_flag = RT_TRUE;
    
    dio_table[dio_pin_find(F_IPM_DC_PIN)].irq_delay = IRQ_DELAY_DEFAULT;
    
    if(rt_tick_get() - tick > 100)
    {
//        rt_kprintf("[Tick %d] tim20 breakin. boost[%d][%d][%d]\r\n", rt_tick_get() / 2,
// 
//                    vfd.ctrl.start_boost,vfd.ctrl.kmon1,vfd.ctrl.fs_boost);

    }
    
    DcdcPwmStop();
    
    tick = rt_tick_get();
}


/**
  * @brief 
  * @param 
  * @retval 
  */
void TIM20_BRK_IRQHandler(void)
{
  /* USER CODE BEGIN TIM1_BRK_TIM15_IRQHandler 0 */
  if (LL_TIM_IsActiveFlag_BRK(phtimx->Instance))
  {
      LL_TIM_ClearFlag_BRK(phtimx->Instance);
      tim20_break_isr();
  }
  
  if (LL_TIM_IsActiveFlag_BRK2(phtimx->Instance))
  {
      LL_TIM_ClearFlag_BRK2(phtimx->Instance);
      tim20_break_isr();
  }
  /* USER CODE END TIM1_BRK_TIM15_IRQHandler 0 */
}


#define TEST_NULL 0
#define TEST_PWM_SET 1
#define TEST_PWM_STOP 2
#define TEST_PWM_CLEAR 3

uint8_t gcTestPwmFlag = 0;
uint8_t gcTestPwmCtr = TEST_NULL;
uint16_t gcTestPwmDuty = 0;
int DcdcPwmOpenLoopDebug(void)
{
    static uint8_t _ctrl = 0;
    static uint16_t _pwm = 0;
    uint8_t result = 0;

    switch (gcTestPwmCtr)
    {
    case TEST_NULL:
        if (gcTestPwmFlag == 1)
        {
            gcTestPwmDuty = 0;
            setpwm(0, 0);
        }
        gcTestPwmFlag = 0;

        break;
    case TEST_PWM_SET:
        result = 1;
        gcTestPwmFlag = 1;
        setpwm(gcTestPwmDuty, 1);
        break;
    case TEST_PWM_STOP:
        gcTestPwmFlag = 1;
        setpwm(0, 0);
        break;
    case TEST_PWM_CLEAR:
        gcTestPwmFlag = 0;
        gcTestPwmDuty = 0;
        setpwm(0, 0);
        break;
    default:
        gcTestPwmFlag = 0;
        gcTestPwmCtr = TEST_NULL;
        break;
    }

    return result;
}

void setpwm(uint16_t pwmper, uint8_t enble)
{
    if (enble == 0)
    {
        DcdcPwmStop();
        return;
    }

    DcdcPwmSetCompare(pwmper * 10);
    DcdcPwmStart();
}
static void pwm(uint8_t argc, char **argv)
{ 
    if (argc == 3)
    {
        rt_uint16_t _pwm = atoi(argv[2]);
        rt_uint16_t enable = atoi(argv[1]);

        gcTestPwmCtr  = enable;
        gcTestPwmDuty = _pwm;
    }
    else
    {
        gcTestPwmCtr = 0;
        gcTestPwmDuty = 0;
    }
}

static void ipm1(uint8_t argc, char **argv)
{
    rt_kprintf(" IPM1 isr cnt %d \r\n", hardware_irq_pin.f_ipm_boost_cnt);
}

#include "finsh.h"
FINSH_FUNCTION_EXPORT(setpwm, set the fullbridge pwm)
MSH_CMD_EXPORT(pwm, clear log information);
MSH_CMD_EXPORT(ipm1, clear log information);
/******************* (C) COPYRIGHT 2021 *****END OF FILE****/


