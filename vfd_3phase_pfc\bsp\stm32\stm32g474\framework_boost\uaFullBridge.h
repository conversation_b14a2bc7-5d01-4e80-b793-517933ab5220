
/******************** (C) COPYRIGHT 2022 bus-lan ********************
* File Name          : uaSysCharge.h
* Author             :
* Version            : V1.0
* Date               :
* Description        :
********************************************************************************/
#ifndef __FULL_BRIDGE_H__
#define __FULL_BRIDGE_H__
#include "stm32g4xx.h"
#include "stdint.h"
#include "uaPID.h"
#include "udPwm.h"

#ifdef VFD_TEST_DEBUG
#define BOOSTC_ERR_RESET_TICK      (5000) // 1s = 1000
#else
#define BOOSTC_ERR_RESET_TICK      (1000) // 1s = 1000
#endif

#define USE_DCDC

#define BOOST_FS_OUT(x)           vfd_fs_set(FS_CHNN_BOOST,x)
#define BOOST_KMON_OUT(x)         vfd_dcdc_kmon1_set(x)
#define GET_BOOST_IPM_BRKIN()     rt_pin_read(GET_PIN(F, 7))  

#define BOOST_CMD_SET_VOLTREF       1
#define BOOST_CMD_SET_CURREF        2
#define BOOST_CMD_GET_STATE         3
#define BOOST_CMD_UPDATE_VOLT_FB    4
#define BOOST_CMD_INIT_TIM8         5
#define BOOST_CMD_UPDATE_VIN        6

#define BOOST_STOP              0
#define BOOST_START             1
#define BOOST_PRECHARGE         2
#define BOOST_SOFT_STARTUP      3
#define BOOST_RUN               4
#define BOOST_ERR               5

#define BOOST_PWM_MAX_DUTY              490     // 49%
#define BOOST_PWM_MIN_DUTY              0       // 0.0%
#define BOOST_PWM_SOFTSTART_DUTY        75      // 7.5%
#define BOOST_PWM_SOFTSTART_MAX_DUTY    200 // 20%

#define BOOST_VIN_PRECHARGE             (500) //50v
#define BOOST_VIN_MIN                   (671) //67v

#define BOOST_WORK_TIMEOUT_TICK         (45 * 60 * 1000)


void uaCharge_PID_Init(void);
int32_t mUapDcdcPwmExecute(uint8_t state);

struct rt_boost_ops;
struct rt_boost_device
{
    const struct rt_boost_ops *ops;

    uint32_t    machine_cnt;
    uint16_t    Vbus_ref;
    uint16_t    Vin_ads;
    uint16_t    Iin_ref;
    int32_t     modulation;         /* 1= 0.001 duty */
    int32_t     modulation_out;     /* 1= 0.001 duty */
    uint8_t     state;
    uint8_t     init_flag               : 1;
    uint8_t     DcdcHardFaultStopFlag   : 1;
    uint8_t     DcdcTimeoutStopFlag     : 1;
    uint8_t     kmon_precharge          : 1;
    uint8_t     start_pwm               : 1;
    uint8_t     softStartErrFlag        : 1;

    PID_CB      _OutVPidCB;

    uint8_t     gcSoftStartStep;
    uint8_t     gcPwmSoftHold;
};

typedef struct rt_boost_device rt_boost_t;

struct rt_boost_ops
{
    rt_err_t (*init)(void);
    rt_err_t (*start)(void);
    rt_err_t (*stop)(void);
    rt_err_t (*reset)(void);
    rt_err_t (*control)(int cmd, void *arg);
    rt_err_t (*machine_task)(void);
};

extern rt_boost_t  boost;
#endif

/******************* (C) COPYRIGHT 2012 Shenzhen TY *****END OF FILE****/

