
/***************************************************************************

Copyright (C), 2022-2022, BSM Tech. Co., Ltd.

* @file           pfc_sdpll.c
* <AUTHOR> @version        V0.0.1
* @date           2022-09-06
* @brief          this is pfc sdpll

History:          // Revision Records

       <Author>             <time>         <version >               <desc>



***************************************************************************/
#include "pfc_sdpll.h"
#include "app_pfcctrl.h"
#define LED_COMM_PIN    GET_PIN(C, 10)

Trig_Components ThetaSC;
tstDPLL3P DPLL3P;

extern uint16_t pfc_adc_value[8];
extern TIM_HandleTypeDef htim1;
extern ADC_HandleTypeDef hadc1;

void SetSdpll3P_Vref(uint16_t ref)
{
    DPLL3P.SysPar.UserVdcref = (ref > 700) ? 700.0f : ref;
    DPLL3P.SysPar.UserVdcref = (ref < 100) ? 100.0f : ref;
    
    DPLL3P.Vbus.PP = DPLL3P.SysPar.UserVdcref * 1.2f;
}
/*******************************************************************************
* Function Name  : void sDPLL3P(void)
* Description    : PFC soft ac3p dpll ctrl init
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void InitSdpll3P(void)
{
    #define SDP_VOLT_REF     650.0f
    
    memset(&DPLL3P, 0, sizeof(DPLL3P));
    
    DPLL3P.SysPar.UserVdcref = SDP_VOLT_REF;
    DPLL3P.SysPar.Vdcref = 0.0f;
    DPLL3P.SysPar.L = (float)5000 / (float)1000000; // 
    DPLL3P.SysPar.C = (float)135  / (float)1000000; // 
    DPLL3P.SysPar.wL = (float)2 * PICONST * DPLL3P.SysPar.L;

    DPLL3P.SysPar.Fs = (float)USER_PFC_PWM_FREQ_Hz; //10000;
    DPLL3P.SysPar.Ts = (float)1 / DPLL3P.SysPar.Fs;
    DPLL3P.SysPar.Tpwm = (float)USER_PFC_PWM_ARR; //DPLL3P.SysPar.Ts*50000000+0.5;

    DPLL3P.Vac.PP = (float)540;
    DPLL3P.Vac.PN = -DPLL3P.Vac.PP;

    DPLL3P.DPLLDDSRF.fn = (float)0; //55;

    DPLL3P.DPLLDDSRF.k1 = (float) 0.000941590368062;
    DPLL3P.DPLLDDSRF.k2 = (float) -0.998116819263876;

    DPLL3P.DPLLDDSRF.delta_T = DPLL3P.SysPar.Ts;

    DPLL3P.DPLLDDSRF.fn = (float)0; //55;

    DPLL3P.DPLLDDSRF.lpf_coeff.B0_lf = (float)1.669743f; // 4.48097790394205;//1.669743;//222.28620946915;//1.669743;// 6.664922416;//9+0.002;//6.664922416;//(222.2862095);//(166.9743);
    DPLL3P.DPLLDDSRF.lpf_coeff.B1_lf = (float)-1.662660f; //-4.40543422988695;//-1.662660;//-222.03439722230;//-1.662660;//-6.664695785;//-9;//-6.664695785;//(-222.0343972);//(-166.266);
    DPLL3P.DPLLDDSRF.lpf_coeff.A1_lf = (float) -1.0;

    //Avoiding Perfect Cosine Wave
    DPLL3P.DPLLDDSRF.theta = -0.1f;
    DPLL3P.DPLLDDSRF.vqpre = -0.1f;

    DPLL3P.Vac.PP = 425.0f;
    DPLL3P.Iac.PP = 3.8f;
    
    DPLL3P.Vbus.PP = DPLL3P.SysPar.UserVdcref * 1.2f;
    if (DPLL3P.Vbus.PP > 800)
    {
        DPLL3P.Vbus.PP = 800;
    }
}

uint16_t dac_data1,dac_data2;
/*******************************************************************************
* Function Name  : void sDPLL3P(void)
* Description    : PFC soft ac3p dpll
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
float line_volt[3];
void sDPLL3P(void)
{
    static uint32_t freq_lpf_cnt = 0;
    uint16_t theta_uh;
    int16_t theta_h = 0;
    float va=0,vb=0,vc=0;
    
    if(DPLL3P.FlagCalibEnd)
    {
        DPLL3P.VLac.In.a= ((float)pfc_adc_value[0] * USER_ACIN_VOLTAGE_SF - DPLL3P.VLac.Offset.a);
        DPLL3P.VLac.In.b= ((float)pfc_adc_value[1] * USER_ACIN_VOLTAGE_SF - DPLL3P.VLac.Offset.b);
        DPLL3P.VLac.In.c= ((float)pfc_adc_value[2] * USER_ACIN_VOLTAGE_SF - DPLL3P.VLac.Offset.c);
    }
    else
    {
        DPLL3P.VLac.In.a= ((float)pfc_adc_value[0] * USER_ACIN_VOLTAGE_SF - VA_OFFSET_V);
        DPLL3P.VLac.In.b= ((float)pfc_adc_value[1] * USER_ACIN_VOLTAGE_SF - VB_OFFSET_V);
        DPLL3P.VLac.In.c= ((float)pfc_adc_value[2] * USER_ACIN_VOLTAGE_SF - VC_OFFSET_V);
    }
    //DPLL3P.VLac.In.a= -(DPLL3P.VLac.In.c+DPLL3P.VLac.In.b);
    
    DPLL3P.Vac.In.a=-(DPLL3P.VLac.In.a-DPLL3P.VLac.In.c)*0.33333333333f;
    DPLL3P.Vac.In.b=-(DPLL3P.VLac.In.b-DPLL3P.VLac.In.a)*0.33333333333f;
    DPLL3P.Vac.In.c=-(DPLL3P.VLac.In.c-DPLL3P.VLac.In.b)*0.33333333333f;
    
    
    DPLL3P.VabcVdq0PN.alpha = (0.6666666667f) * (DPLL3P.Vac.In.a - 0.5f * (DPLL3P.Vac.In.b + DPLL3P.Vac.In.c));
    DPLL3P.VabcVdq0PN.beta = (0.5773502691f) * (DPLL3P.Vac.In.b - DPLL3P.Vac.In.c);
    
//  DPLL3P.VabcVdq0PN.alpha = DPLL3P.Vac.In.a;
//  DPLL3P.VabcVdq0PN.beta  = (0.5773502691f )*(2*DPLL3P.Vac.In.b + DPLL3P.Vac.In.a); //(2 * p_abc->b + p_abc->a)

    DPLL3P.VabcVdq0PN.dp =  DPLL3P.VabcVdq0PN.alpha * DPLL3P.AngleCal.cos_theta + DPLL3P.VabcVdq0PN.beta * DPLL3P.AngleCal.sin_theta;
    DPLL3P.VabcVdq0PN.qp = -DPLL3P.VabcVdq0PN.alpha * DPLL3P.AngleCal.sin_theta + DPLL3P.VabcVdq0PN.beta * DPLL3P.AngleCal.cos_theta;

    DPLL3P.VabcVdq0PN.dn =  DPLL3P.VabcVdq0PN.alpha * DPLL3P.AngleCal.cos_theta - DPLL3P.VabcVdq0PN.beta * DPLL3P.AngleCal.sin_theta;
    DPLL3P.VabcVdq0PN.qn =  DPLL3P.VabcVdq0PN.alpha * DPLL3P.AngleCal.sin_theta + DPLL3P.VabcVdq0PN.beta * DPLL3P.AngleCal.cos_theta;

    DPLL3P.DPLLDDSRF.dpde = DPLL3P.VabcVdq0PN.dp
                            - (DPLL3P.DPLLDDSRF.dndelpf * DPLL3P.AngleCal.cos_2theta)
                            - (DPLL3P.DPLLDDSRF.qndelpf * DPLL3P.AngleCal.sin_2theta);
                            
    DPLL3P.DPLLDDSRF.qpde = DPLL3P.VabcVdq0PN.qp
                            + (DPLL3P.DPLLDDSRF.dndelpf * DPLL3P.AngleCal.sin_2theta)
                            - (DPLL3P.DPLLDDSRF.qndelpf * DPLL3P.AngleCal.cos_2theta);

    DPLL3P.DPLLDDSRF.dnde = DPLL3P.VabcVdq0PN.dn
                            - (DPLL3P.DPLLDDSRF.dpdelpf * DPLL3P.AngleCal.cos_2theta)
                            + (DPLL3P.DPLLDDSRF.qpdelpf * DPLL3P.AngleCal.sin_2theta);
    DPLL3P.DPLLDDSRF.qnde = DPLL3P.VabcVdq0PN.qn
                            - (DPLL3P.DPLLDDSRF.dpdelpf * DPLL3P.AngleCal.sin_2theta)
                            - (DPLL3P.DPLLDDSRF.qpdelpf * DPLL3P.AngleCal.cos_2theta);

    DPLL3P.DPLLDDSRF.dpdelpf = 0.00188140921932478000f * DPLL3P.DPLLDDSRF.dpde + DPLL3P.DPLLDDSRF.dpdelpf * 0.99811859078067500000f ;
    DPLL3P.DPLLDDSRF.qpdelpf = 0.00188140921932478000f * DPLL3P.DPLLDDSRF.qpde + DPLL3P.DPLLDDSRF.qpdelpf * 0.99811859078067500000f ;
    DPLL3P.DPLLDDSRF.dndelpf = 0.00188140921932478000f * DPLL3P.DPLLDDSRF.dnde + DPLL3P.DPLLDDSRF.dndelpf * 0.99811859078067500000f ;
    DPLL3P.DPLLDDSRF.qndelpf = 0.00188140921932478000f * DPLL3P.DPLLDDSRF.qnde + DPLL3P.DPLLDDSRF.qndelpf * 0.99811859078067500000f ;

    #ifndef DEBUG_PFC_INVERT_MODE
    
    if( ((vfd.ad.ac_vin_u*0.1f) < 50.0f) &&
        ((vfd.ad.ac_vin_v*0.1f) < 50.0f) &&
        ((vfd.ad.ac_vin_w*0.1f) < 50.0f) )
    {
        DPLL3P.DPLLDDSRF.lfo = 0;
        DPLL3P.DPLLDDSRF.freq_stableflag = 0;
        freq_lpf_cnt = 0;
    }
    else if(AC380_FreqIs_Ok && freq_lpf_cnt++ >= 10)
    {
        DPLL3P.DPLLDDSRF.freq_stableflag = 1;
    }
    
    if(DPLL3P.DPLLDDSRF.freq_stableflag == 0)
    {
        DPLL3P.DPLLDDSRF.lfo =    DPLL3P.DPLLDDSRF.lfo
                               + (4.48097790394205f * DPLL3P.DPLLDDSRF.qpde)
                               + (-4.40543422988695f * DPLL3P.DPLLDDSRF.vqpre);
    }
    else
        DPLL3P.DPLLDDSRF.lfo =    DPLL3P.DPLLDDSRF.lfo
                               + (DPLL3P.DPLLDDSRF.lpf_coeff.B0_lf * DPLL3P.DPLLDDSRF.qpde)
                               + (DPLL3P.DPLLDDSRF.lpf_coeff.B1_lf * DPLL3P.DPLLDDSRF.vqpre);

    #else
//    if(pfc.PFC_Runing)
//    {
//        if(DPLL3P.DPLLDDSRF.lfo < 50.0f)
//            DPLL3P.DPLLDDSRF.lfo += 0.005f;
//    }
//    else
    DPLL3P.DPLLDDSRF.freq_stableflag = 1;
    DPLL3P.DPLLDDSRF.lfo = 50.0f;
    
    #endif
    
    DPLL3P.DPLLDDSRF.vqpre  = DPLL3P.DPLLDDSRF.qpde;
    DPLL3P.DPLLDDSRF.fo_lpf = DPLL3P.DPLLDDSRF.fo_lpf * 0.99f + DPLL3P.DPLLDDSRF.lfo * 0.01f;
    DPLL3P.DPLLDDSRF.theta  = DPLL3P.DPLLDDSRF.theta + (DPLL3P.DPLLDDSRF.lfo * DPLL3P.DPLLDDSRF.delta_T);
    
    theta_h = DPLL3P.DPLLDDSRF.theta;
    DPLL3P.DPLLDDSRF.theta -= theta_h;

    theta_uh = DPLL3P.DPLLDDSRF.theta * 65536.0f;
    
    
    if (theta_uh < 0x7fff)
    {
        //LED_COM_OUT(1);
    }
    else
    {
        //LED_COM_OUT(0);
    }

    ThetaSC = Trig_Functions(theta_uh);
    DPLL3P.AngleCal.sin_theta = (float) ThetaSC.hSin / 32768.0f;
    DPLL3P.AngleCal.cos_theta = (float) ThetaSC.hCos / 32768.0f;

    theta_uh *= 2;
    ThetaSC = Trig_Functions(theta_uh);

    DPLL3P.AngleCal.sin_2theta = (float) ThetaSC.hSin / 32768.0f;
    DPLL3P.AngleCal.cos_2theta = (float) ThetaSC.hCos / 32768.0f;

}


