
/*
 * File      : uLed.c
 *
 *
 *
 *
 *
 *
 *
 * Change Logs:
 * Date           Author       Notes
 * 2019-07-22     Group
 */


/* Includes ------------------------------------------------------------------*/
#include "uLed.h"
#include "uapp.h"
#include "./dcdc/uaFullbridge.h"

/* Private typedef -----------------------------------------------------------*/

/* Private define ------------------------------------------------------------*/
void System_Led_StatusChange(void);

//-----------------------------------------------------------------------------
//
//-----------------------------------------------------------------------------

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

const uint8_t *p_led_flt;
const uint8_t *p_led_run;
const uint8_t *p_led_com;

const uint8_t _LED_NOFLT[10] = {0};                    //û�й��ϣ�Ϩ��
const uint8_t _LED_LFLT[10]  = {0, 0, 0, 0, 1, 1, 0, 0, 0, 0}; //һ����ϣ�����?
const uint8_t _LED_MFLT[10]  = {0, 0, 1, 1, 1, 1, 1, 1, 0, 0}; //һ����ϣ�����?
const uint8_t _LED_HFLT[10]  = {1, 1, 1, 1, 1, 1, 1, 1, 1, 1}; //���ع��ϣ�����

const uint8_t _LED_IDLE[10]   = {1, 0, 0, 0, 0, 0, 0, 0, 0, 0}; //run ����ֹͣ״̬
const uint8_t _LED_EMERG_RUN [10] = {1, 1, 1, 0, 0, 1, 1, 1, 0, 0}; //run ���﹤��״̬
const uint8_t _LED_NORMAL_RUN [10] = {1, 0, 1, 0, 1, 0, 1, 0, 1, 0}; //run ���﹤��״̬

const uint8_t _LED_COMERR[10] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0}; //ͨ��ʧЧ��Ϩ��
const uint8_t _LED_COMOK[10]  = {1, 1, 0, 0, 0, 1, 1, 0, 0, 0}; //ͨ������������

/* Private function prototypes -----------------------------------------------*/

#ifdef USE_PMSM01
    #define LEDFLT GPIO_PIN_1
    #define LEDRUN GPIO_PIN_2
    #define LEDCOM GPIO_PIN_0
#else
    #define LEDFLT GPIO_PIN_1
    #define LEDRUN GPIO_PIN_2
    #define LEDCOM GPIO_PIN_0
#endif


#define LEDGPIO GPIOG

#define LEDTEST GPIOA
#define LEDTRUN GPIO_PIN_5

/*******************************************************************************
* Function Name  :  Led_Init
* Description    : ����LED�Ƴ�ʼ��
*
* Input          :  .
* Output         : None
* Return         :
*******************************************************************************/
void Led_init(void)
{
    uint32_t delaytime = 100000 * 50;

    GPIO_InitTypeDef GPIO_InitStructure = {0};

    /* Enable GPIOC clocks */
    __HAL_RCC_GPIOG_CLK_ENABLE();

    //------------------------------------------------------------------------
    //Ledָʾ�ƹܽų�ʼ��
    //------------------------------------------------------------------------
    GPIO_InitStructure.Pin   =  LEDFLT | LEDRUN | LEDCOM; //FLT,RUN,COM
    GPIO_InitStructure.Mode  =  GPIO_MODE_OUTPUT_PP;
    GPIO_InitStructure.Speed =  GPIO_SPEED_FREQ_HIGH;

    HAL_GPIO_Init(LEDGPIO, &GPIO_InitStructure);

    HAL_GPIO_WritePin(LEDGPIO, LEDFLT, GPIO_PIN_SET);
    HAL_GPIO_WritePin(LEDGPIO, LEDRUN, GPIO_PIN_SET);
    HAL_GPIO_WritePin(LEDGPIO, LEDCOM, GPIO_PIN_SET);

    p_led_flt = _LED_LFLT;
    p_led_run = _LED_IDLE;
    p_led_com = _LED_COMERR;

    while (--delaytime);
}

/*******************************************************************************
* Function Name  :  void Led_StatusChange(void)
* Description    :
*
* Input          : - none.
* Output         : None
* Return         : None
*******************************************************************************/
char testled = 0;
void Led_StatusChange(void)
{

//FLT RUN COM

    //FLT LED
    if (PMSMST.DcdcFLT_FucnA)
    {
        p_led_flt = _LED_HFLT;
    }
    else if (PMSMST.DcdcHardFaultStopFlag)
    {
        if (gsHardFaultStopSum < 10)
            p_led_flt = _LED_LFLT;
        else
            p_led_flt = _LED_MFLT;
    }
    else
    {
        p_led_flt = _LED_NOFLT;
    }

    //RUN LED
    if (PMSMST.ControlState == Mc_Run)
    {
        switch (PMSMST.SysState)
        {
        case SYS_NORMAL:
            p_led_run = _LED_NORMAL_RUN;
            break;
        case SYS_EMERG:
            p_led_run = _LED_EMERG_RUN;
            break;
        default:
            p_led_run = _LED_IDLE;
            break;
        }
    }
    else
        p_led_run = _LED_IDLE;
    if (PMSMST.COMM_OK)
    {
        p_led_com = _LED_COMOK;
    }
    else
    {
        p_led_com = _LED_COMERR;
    }

}

int PowerOnLedPinCheck(uint32_t _10ms_cnt)
{
    /* 1Hz light up/down */
    if (_10ms_cnt % 50 == 0)
    {
        HAL_GPIO_TogglePin(LEDGPIO, LEDRUN);
        HAL_GPIO_TogglePin(LEDGPIO, LEDFLT);
        HAL_GPIO_TogglePin(LEDGPIO, LEDCOM);
    }

    if (_10ms_cnt <= 300)
    {
        return -1;
    }
    else
    {
        HAL_GPIO_WritePin(LEDGPIO, LEDRUN, GPIO_PIN_RESET);
        HAL_GPIO_WritePin(LEDGPIO, LEDFLT, GPIO_PIN_RESET);
        HAL_GPIO_WritePin(LEDGPIO, LEDCOM, GPIO_PIN_RESET);
        return 0;
    }
}


/*******************************************************************************
* Function Name  :  void Led_Logic(void)
* Description    :
*
* Input          : - none.
* Output         : None
* Return         : None
*******************************************************************************/
void Led_Logic(void)
{
    static uint8_t cnt = 0;
    static uint8_t _first = 1;
    static uint32_t mscnt = 0;

    mscnt++;

    if (_first)
    {
        /* when power on ,led light up and down in 1Hz */
        if (PowerOnLedPinCheck(mscnt) == 0)
            _first = 0;
        else
            return;
    }

    if ((mscnt % 10) == 0)
    {
        Led_StatusChange();

        if (p_led_flt[cnt])
        {
            HAL_GPIO_WritePin(LEDGPIO, LEDFLT, GPIO_PIN_RESET);
        }
        else
        {
            HAL_GPIO_WritePin(LEDGPIO, LEDFLT, GPIO_PIN_SET);
        }


        if (p_led_run[cnt])
        {
            HAL_GPIO_WritePin(LEDGPIO, LEDRUN, GPIO_PIN_RESET);
        }
        else
        {
            HAL_GPIO_WritePin(LEDGPIO, LEDRUN, GPIO_PIN_SET);
        }

        if (p_led_com[cnt])
        {
            HAL_GPIO_WritePin(LEDGPIO, LEDCOM, GPIO_PIN_RESET);
        }
        else
        {
            HAL_GPIO_WritePin(LEDGPIO, LEDCOM, GPIO_PIN_SET);
        }

        cnt++;

        cnt = cnt % 10;
    }
}

/******************* (C) COPYRIGHT 2019 Group *****END OF FILE****/


