import rtconfig
import os
from building import *

cwd     = GetCurrentDir()

#CPPPATH = [cwd, str(Dir('#'))]

CPPPATH = [
    cwd + '/MotorControl/MCSDK/MCLib/Any/Inc',
	cwd + '/MotorControl/MCSDK/MCLib/G4xx/Inc',
	cwd + '/MotorControl/MCSDK/UILibrary/Inc']



src		= [cwd + '/MotorControl/MCSDK/MCLib/G4xx/Src/r3_2_g4xx_pwm_curr_fdbk.c']
src     += [cwd + '/MotorControl/MCSDK/MCLib/Any/Src/bus_voltage_sensor.c']
src     += [cwd + '/MotorControl/MCSDK/MCLib/Any/Src/circle_limitation.c']
src     += [cwd + '/MotorControl/MCSDK/MCLib/Any/Src/digital_output.c']
src     += [cwd + '/MotorControl/MCSDK/MCLib/Any/Src/flux_weakening_ctrl.c']
src     += [cwd + '/MotorControl/MCSDK/MCLib/Any/Src/motor_power_measurement.c']
src     += [cwd + '/MotorControl/MCSDK/MCLib/Any/Src/ntc_temperature_sensor.c']
src     += [cwd + '/MotorControl/MCSDK/MCLib/Any/Src/open_loop.c']
src     += [cwd + '/MotorControl/MCSDK/MCLib/Any/Src/pid_regulator.c']
src     += [cwd + '/MotorControl/MCSDK/MCLib/Any/Src/pqd_motor_power_measurement.c']
src     += [cwd + '/MotorControl/MCSDK/MCLib/Any/Src/pwm_common.c']
src     += [cwd + '/MotorControl/MCSDK/MCLib/Any/Src/pwm_curr_fdbk.c']

src     += [cwd + '/MotorControl/MCSDK/MCLib/Any/Src/ramp_ext_mngr.c']
src     += [cwd + '/MotorControl/MCSDK/MCLib/Any/Src/revup_ctrl.c']
src     += [cwd + '/MotorControl/MCSDK/MCLib/Any/Src/speed_pos_fdbk.c']
src     += [cwd + '/MotorControl/MCSDK/MCLib/Any/Src/speed_torq_ctrl.c']
src     += [cwd + '/MotorControl/MCSDK/MCLib/Any/Src/state_machine.c']
src     += [cwd + '/MotorControl/MCSDK/MCLib/Any/Src/sto_pll_speed_pos_fdbk.c']
src     += [cwd + '/MotorControl/MCSDK/MCLib/Any/Src/virtual_speed_sensor.c']
src     += [cwd + '/MotorControl/MCSDK/MCLib/Any/Src/r_divider_bus_voltage_sensor.c']

src     += [cwd + '/MotorControl/MCSDK/UILibrary/Src/dac_common_ui.c']
src     += [cwd + '/MotorControl/MCSDK/UILibrary/Src/frame_communication_protocol.c']
src     += [cwd + '/MotorControl/MCSDK/UILibrary/Src/ui_irq_handler.c']
src     += [cwd + '/MotorControl/MCSDK/UILibrary/Src/usart_frame_communication_protocol.c']


group = DefineGroup('Middlewares/MotorControl', src, depend = [''], CPPPATH = CPPPATH)

Return('group')
