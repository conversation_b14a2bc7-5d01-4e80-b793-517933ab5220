
/*
 * File      : usctrl.c
 *
 *
 *
 *
 *
 *
 *
 * Change Logs:
 * Date           Author       Notes
 * 2021-12-15     Xiou
 */


/* Includes ------------------------------------------------------------------*/
#include "uApp.h"
#include "usctrl.h"
#include "regular_conversion_manager.h"
#include "api_rs485.h"
#include "uaFullbridge.h"
/* Private typedef -----------------------------------------------------------*/
char invtemp_limit_flag, dctemp_limit_flag, vin_volt_limit_flag, vin_power_limit_flag;

/* Private define ------------------------------------------------------------*/

/**
  * @brief ref speed increase/decrease by step.
  * @param dest speed.
  * @retval step speed
  */
int speed_run_step(int ref_speed_set, int feedback)
{
    static int ref_speed = 0;
    int result ;
    ref_speed_set = ref_speed_set * 10;

    if (ref_speed_set == 0)
    {
        ref_speed = 0;
    }
    else if ((ref_speed / 10 > 600) &&
             (ref_speed / 10 > feedback) &&
             ((ref_speed / 10 - feedback) > (fan_cur_fix.ref_step * 10)))
    {
        ref_speed = ref_speed;
    }
    else if (ref_speed < (ref_speed_set - fan_cur_fix.ref_step))
    {
        ref_speed += fan_cur_fix.ref_step;
    }
    else if (ref_speed > (ref_speed_set + 4 * fan_cur_fix.ref_step))
    {
        ref_speed -= 4 * fan_cur_fix.ref_step;
    }
    else
    {
        ref_speed = ref_speed_set;
    }

    result = ref_speed / 10;

    return result;
}

/**
  * @brief active flag to hmi ptu.
  * @param str which flag.
  * @retval none
  */
static void flag_to_hmi(const char *name)
{
    fan_cur_fix.dcdc_dc110_low_flag = !rt_strcmp("volt", name) ? 1 : 0;
    fan_cur_fix.dcdc_oc_flag = !rt_strcmp("curr", name) ? 1 : 0;
    fan_cur_fix.dcdc_ot_flag = !rt_strcmp("dctemp", name) ? 1 : 0;
    fan_cur_fix.dcdc_op_flag = !rt_strcmp("power", name) ? 1 : 0;
    fan_cur_fix.invter_ot_flag = !rt_strcmp("invtemp", name) ? 1 : 0;
    fan_cur_fix.fix_active_flag = fan_cur_fix.dcdc_dc110_low_flag   ||
                                  fan_cur_fix.dcdc_op_flag        ||
                                  fan_cur_fix.dcdc_ot_flag        ||
                                  fan_cur_fix.dcdc_oc_flag        ||
                                  fan_cur_fix.invter_ot_flag;

}

/**
  * @brief is wait over tick since last set ?
  * @param wait tick.
  * @retval result
  */
static int fix_after_time(int wait_tick)
{
    static rt_size_t  tick = 0;

    if (rt_tick_get() - tick > wait_tick)
        return 1;
    else
        return 0;
}

/**
  * @brief check fan speed need to fix by curve ?
  * @param control set speed
  * @retval fix set speed
  */
int fan_speed_fix(int ref_speed_set)
{
    int      result = 0;
    uint16_t temperature;
    static float cur_top;
    float temp_cur_top;
    static int stop_delay;
    static uint8_t fan_state = 0;
    float volt_curve_top    = fan_cur_fix.cur_top;
    float invtemp_curve_top    = fan_cur_fix.cur_top;
    float dctemp_curve_top    = fan_cur_fix.cur_top;
    float power_curve_top    = fan_cur_fix.cur_top;
    static uint8_t  over_cur_flag = 0;
    static int16_t  over_cur_fit_speed = 0;

    /* check need stop 10s because ptu ? */
    if (fan_cur_fix.ptu_stop_flag && !stop_delay)
    {
        stop_delay = 1000;
    }

    if (stop_delay > 0)
    {
        stop_delay--;
        if (stop_delay == 0)
            fan_cur_fix.ptu_stop_flag = 0;
    }

    /* control speed effcvtive */
    if (!fan_cur_fix.ptu_stop_flag &&
            ref_speed_set > 0)
    {
        /* check if active curve limit current */
        dctemp_limit_flag = ((PMSMST.SysState == SYS_EMERG)                   &&
                             (PMSMST.Temperature3 > fan_cur_fix.temp_low));

        invtemp_limit_flag  = (PMSMST.Temperature > fan_cur_fix.temp_low);

        vin_volt_limit_flag = ((PMSMST.SysState == SYS_EMERG)               &&
                               (PMSMST.VinbusVoltage <= fan_cur_fix.volt_top)  &&
                               (PMSMST.VinbusVoltage >= 500));

        vin_power_limit_flag = ((PMSMST.SysState == SYS_EMERG) &&
                                (PMSMST.IinCurrent * PMSMST_PARA_DISPLAY.DCvin) >= (fan_cur_fix.pw_top * 8 / 10));

        /* default dcdc system current top limit is 13A */
        temp_cur_top = fan_cur_fix.cur_top;

        /* curve limit cal */
        volt_curve_top = (vin_volt_limit_flag) ? (-1.67 * PMSMST.VinbusVoltage + 2883.33) : /*95v->13A,80v->15.5A*/
                         fan_cur_fix.cur_top;
        volt_curve_top = volt_curve_top < 1000 ? 1000 : volt_curve_top;

        invtemp_curve_top = (invtemp_limit_flag) ? (-110 * PMSMST.Temperature + 10650) : /*85du->13A,90du->7.5A*/
                            fan_cur_fix.cur_top;

        dctemp_curve_top = (dctemp_limit_flag) ? (-110 * PMSMST.Temperature3 + 10650) : /*85du->13A,90du->7.5A*/
                           fan_cur_fix.cur_top;

        power_curve_top = (vin_power_limit_flag) ? (fan_cur_fix.pw_top / PMSMST_PARA_DISPLAY.DCvin) : /*110V*13A*/
                          fan_cur_fix.cur_top;

        /* get the minimum  one */
        temp_cur_top = (temp_cur_top > volt_curve_top) ? volt_curve_top : temp_cur_top;
        temp_cur_top = (temp_cur_top > invtemp_curve_top) ? invtemp_curve_top : temp_cur_top;
        temp_cur_top = (temp_cur_top > dctemp_curve_top) ? dctemp_curve_top : temp_cur_top;
        temp_cur_top = (temp_cur_top > power_curve_top) ? power_curve_top : temp_cur_top;

        /* curve limit minimum is 7.5A */
        cur_top = temp_cur_top < fan_cur_fix.cur_low ? fan_cur_fix.cur_low : temp_cur_top;

        /* curve limit active by Iin  */
        if ((PMSMST.IinCurrent >= (uint16_t)cur_top) &&
                fix_after_time(fan_cur_fix.fix_tick))
        {
            uint16_t fix_step = fan_cur_fix.fix_step * (PMSMST.IinCurrent - (uint16_t)cur_top + fan_cur_fix.back_lash - 1) / fan_cur_fix.back_lash;
            /* set back x step to limit current */
            over_cur_fit_speed = PMSMST.MotorSpeed;
            over_cur_fit_speed -=  fix_step;
        }
        else if (over_cur_fit_speed)
        {
            static uint16_t cnt = 0;

            /* Iin back lash for 1A last for 2s clear curve limit */
            if ((PMSMST.IinCurrent + fan_cur_fix.back_lash) < (uint16_t)cur_top)
            {
                if (cnt < fan_cur_fix.resume_tick)
                    cnt++;
                else
                {
                    over_cur_fit_speed += fan_cur_fix.resume_step;
                    cnt = 0;
                }
            }
            else
                cnt = 0;
        }

        /* control speed bigger than fix speed ,set to fix speed */
        if (over_cur_fit_speed > 0 &&
                (ref_speed_set > over_cur_fit_speed))
        {
            ref_speed_set = over_cur_fit_speed ;

            /* dcdc system limit 13A */
            if (cur_top == fan_cur_fix.cur_top) flag_to_hmi("curr");
            else if (temp_cur_top == volt_curve_top) flag_to_hmi("volt");
            else if (temp_cur_top == invtemp_curve_top) flag_to_hmi("invtemp");
            else if (temp_cur_top == dctemp_curve_top) flag_to_hmi("dctemp");
            else if (temp_cur_top == power_curve_top) flag_to_hmi("power");

        }
        /* control speed smaller than fix speed,clear fix speed */
        else
        {
            flag_to_hmi("clear");
            over_cur_fit_speed = 0;
        }

        ref_speed_set = ref_speed_set > MAXSPEED ? MAXSPEED : ref_speed_set;
        ref_speed_set = ref_speed_set < (MINSPEED * 8 / 10) ? (MINSPEED * 8 / 10) : ref_speed_set;
    }
    else
        ref_speed_set = 0;

    result = ref_speed_set;

    return result;
}

int16_t ControlSpeed = DEAFULT_SPEED; /*< default speed set */
/*******************************************************************************
* Function Name  :  void Led_Logic(void)
* Description    :
*
* Input          : - none.
* Output         : None
* Return         : None
*******************************************************************************/
int16_t get_ctrl_speed(uint8_t stop_cmd)
{
    static uint16_t stopcnt = 0;
    static uint8_t ControlDirec = 0;

    int16_t result = 0;

    /* com to ctrl */
    //if (Rs485GetLinkflag() == 0) //
    //if(!PMSMST.COMM_OK)
    if (0)
    {
        if (RIDS.ctl_runtype == 0)
        {
            ControlSpeed = 0;
            PMSMST.ControlDirection = MOTOR_STOP;
        }
        else
        {
            ControlSpeed = RIDS.ctl_speed;
        }
    }
    else
    {
        if (0 == RIDS.ctl_runtype)
        {
            ControlSpeed = 0;
            PMSMST.ControlDirection = MOTOR_STOP;
        }
        else
        {
            if (!PMSMST.speed_is_set)
            {
                if (SYS_NORMAL == PMSMST.SysState)
                {
                    ControlSpeed = 1500;        // ����Ĭ��ת��Ϊ1500
                    RIDS.ctl_speed = ControlSpeed;
                }
                else if (SYS_EMERG  == PMSMST.SysState)
                {
                    ControlSpeed = 850;
                    RIDS.ctl_speed = ControlSpeed;
                }
            }
            else
            {
                ControlSpeed = RIDS.ctl_speed;
            }

            if (ControlSpeed > MAXSPEED) ControlSpeed = MAXSPEED;
            if (ControlSpeed < -MAXSPEED) ControlSpeed = -MAXSPEED;

            PMSMST.ControlDirection = (ControlSpeed > 0) ? MOTOR_POSITIVE : MOTOR_NEGATIVE;
            PMSMST.ControlDirection = (ControlSpeed == 0) ? MOTOR_STOP : PMSMST.ControlDirection;
        }
    }

    if (PMSMST.ControlDirection != ControlDirec)
    {
        if (MOTOR_STOP != ControlDirec)
        {
            stopcnt = 5;//50ms
        }

        ControlDirec = PMSMST.ControlDirection;
    }

    if (stopcnt > 0)
    {
        stopcnt--;
        ControlSpeed = 0;
        User_MotorControl("stop", NULL);
    }

    if ((stop_cmd) || (ControlSpeed < (MINSPEED * 8 / 10)))
    {
        ControlSpeed = 0;
    }

    result = fan_speed_fix(ControlSpeed);

    return speed_run_step(result, (int)PMSMST.MotorSpeed);

}




/******************* (C) COPYRIGHT 2019 Group *****END OF FILE****/


