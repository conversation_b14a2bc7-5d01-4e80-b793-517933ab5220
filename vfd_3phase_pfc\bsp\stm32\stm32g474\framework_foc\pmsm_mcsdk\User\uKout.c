
/*
 * File      : uLed.c
 *
 *
 *
 *
 *
 *
 *
 * Change Logs:
 * Date           Author       Notes
 * 2019-07-22     Group
 */


/* Includes ------------------------------------------------------------------*/
#include "ukout.h"
#include "uApp.h"

#define DBG_TAG "kout"
#define DBG_LVL DBG_LOG
#include <rtdbg.h>
/* Private typedef -----------------------------------------------------------*/

/* Private define ------------------------------------------------------------*/

//-----------------------------------------------------------------------------
//
//-----------------------------------------------------------------------------

/* Private macro -------------------------------------------------------------*/
//����·Ԥ��� PD0
#define KIN  GPIO_PIN_0
#define KINGPIO GPIOD

//AC380���� PB6
#define KACIN  GPIO_PIN_6
#define KACINGPIO GPIOB

//�������
#define KFLT GPIO_PIN_4
#define KFLTGPIO GPIOD

//DCDC��ѹ���� PC8
#define KDCIN  GPIO_PIN_8
#define KDCINGPIO GPIOC

/* Private variables ---------------------------------------------------------*/


/* Private function prototypes -----------------------------------------------*/



/*******************************************************************************
* Function Name  :  KOut_init
* Description    : �̵��������ʼ��
*
* Input          :  .
* Output         : None
* Return         :
*******************************************************************************/
void KOut_init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure = {0};

    /* Enable GPIOD clocks */
    __HAL_RCC_GPIOD_CLK_ENABLE();
    __HAL_RCC_GPIOB_CLK_ENABLE();

    //------------------------------------------------------------------------
    //�̵�������ų�ʼ��
    //------------------------------------------------------------------------
    GPIO_InitStructure.Pin   =  KIN ;  //KMON
    GPIO_InitStructure.Mode  =  GPIO_MODE_OUTPUT_PP;
    GPIO_InitStructure.Speed =  GPIO_SPEED_FREQ_HIGH;

    HAL_GPIO_Init(KINGPIO, &GPIO_InitStructure);

    GPIO_InitStructure.Pin   =  KFLT ;  //KFLT
    GPIO_InitStructure.Mode  =  GPIO_MODE_OUTPUT_PP;
    GPIO_InitStructure.Speed =  GPIO_SPEED_FREQ_HIGH;

    HAL_GPIO_Init(KFLTGPIO, &GPIO_InitStructure);

    GPIO_InitStructure.Pin   =  KACIN ;  //KMON2
    GPIO_InitStructure.Mode  =  GPIO_MODE_OUTPUT_PP;
    GPIO_InitStructure.Speed =  GPIO_SPEED_FREQ_HIGH;

    HAL_GPIO_Init(KACINGPIO, &GPIO_InitStructure);

    GPIO_InitStructure.Pin   =  KDCIN ;  //KMON1
    GPIO_InitStructure.Mode  =  GPIO_MODE_OUTPUT_PP;
    GPIO_InitStructure.Speed =  GPIO_SPEED_FREQ_HIGH;

    HAL_GPIO_Init(KDCINGPIO, &GPIO_InitStructure);

    HAL_GPIO_WritePin(KINGPIO, KIN, GPIO_PIN_RESET);

    HAL_GPIO_WritePin(KFLTGPIO, KFLT, GPIO_PIN_RESET);

    HAL_GPIO_WritePin(KACINGPIO, KACIN, GPIO_PIN_RESET);

    HAL_GPIO_WritePin(KDCINGPIO, KDCIN, GPIO_PIN_RESET);
//    rt_kprintf("\r\n + LED and HEX GPIO configure...... OK\r\n");
//    rt_kprintf(" |");
}

/*******************************************************************************
* Function Name  :  void Kin_On(void)
* Description    :
*
* Input          : - none.
* Output         : None
* Return         : None
*******************************************************************************/
void KACin_On(void)
{
    bKMON2_Precharge = 1;
    //HAL_GPIO_WritePin(KACINGPIO, KACIN, GPIO_PIN_SET);
}

/*******************************************************************************
* Function Name  :  void Kin_Off(void)
* Description    :
*
* Input          : - none.
* Output         : None
* Return         : None
*******************************************************************************/
void KACin_Off(void)
{
    bKMON2_Precharge = 0;
    //HAL_GPIO_WritePin(KACINGPIO, KACIN, GPIO_PIN_RESET);
}

/*******************************************************************************
* Function Name  :  void Kin_On(void)
* Description    :
*
* Input          : - none.
* Output         : None
* Return         : None
*******************************************************************************/
void Kin_On(void)
{
    bKMON_Precharge = 1;
    //HAL_GPIO_WritePin(KINGPIO, KIN, GPIO_PIN_SET);
}

/*******************************************************************************
* Function Name  :  void Kin_Off(void)
* Description    :
*
* Input          : - none.
* Output         : None
* Return         : None
*******************************************************************************/
void Kin_Off(void)
{
    bKMON_Precharge = 0;
    //HAL_GPIO_WritePin(KINGPIO, KIN, GPIO_PIN_RESET);
}

/*******************************************************************************
* Function Name  :  void Kflt_On(void)
* Description    :
*
* Input          : - none.
* Output         : None
* Return         : None
*******************************************************************************/
void Kflt_On(void)
{
    if(!bO1_Fault)
       LOG_I("bO1_Fault set 1");
    bO1_Fault = 1;
}

/*******************************************************************************
* Function Name  :  void Kflt_Off(void)
* Description    :
*
* Input          : - none.
* Output         : None
* Return         : None
*******************************************************************************/
void Kflt_Off(void)
{
    if(bO1_Fault)
       LOG_I("bO1_Fault set 0");
    bO1_Fault = 0;
}

/*******************************************************************************
* Function Name  :  void KDCin_On(void)
* Description    :
*
* Input          : - none.
* Output         : None
* Return         : None
*******************************************************************************/
void KDCin_On(void)
{
    bKMON1_Precharge = 1;
    //HAL_GPIO_WritePin(KDCINGPIO, KDCIN, GPIO_PIN_SET);
}

/*******************************************************************************
* Function Name  :  void KDCin_Off(void)
* Description    :
*
* Input          : - none.
* Output         : None
* Return         : None
*******************************************************************************/
void KDCin_Off(void)
{
    bKMON1_Precharge = 0;
    //HAL_GPIO_WritePin(KDCINGPIO, KDCIN, GPIO_PIN_RESET);
}
/******************* (C) COPYRIGHT 2019 Group *****END OF FILE****/


