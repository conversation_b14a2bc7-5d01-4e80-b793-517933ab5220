import rtconfig
import os
from building import *

cwd     = GetCurrentDir()

CPPPATH = [cwd, str(Dir('#'))]
src     = Split("""
main_init.c
mc_config.c
dac_ui.c
mc_api.c
mc_interface.c
mc_math.c
mc_parameters.c
mc_tasks.c
motor_control_protocol.c
motorcontrol.c
regular_conversion_manager.c
stm32g4xx_hal_msp.c
stm32g4xx_it.c
stm32g4xx_mc_it.c
ui_task.c
user_interface.c
""")

group = DefineGroup('Application/User', src, depend = [''], CPPPATH = CPPPATH)

Return('group')
