/******************** (C) COPYRIGHT 2022     ***********************************
* File Name          : app_bsm_com_outernet.c
* Author             : dfy
* Version            : V1.0
* Date               : 2022.07.07
* Description        : 外界通讯
********************************************************************************/
#include "app_bsm_com_outernet.h"
#include "crc.h"
#include <dfs_posix.h>
#include "uapp.h"
#include "uaInverter.h"
#include "uaInverterPort.h"

//#define BSM_COM_DEBUG

#ifdef BSM_COM_DEBUG
#define BSM_COM_PRINT(...)  rt_kprintf(__VA_ARGS__)
#else
#define BSM_COM_PRINT(...) 
#endif


#define FW_BUFF_LEN  1024
typedef struct{
	uint8_t 	*rx_buff;
	uint8_t 	*tx_buff;
	uint8_t 	*ack_buff;
	
	uint32_t 	rx_len;
	uint32_t    max_data_len;
	uint8_t 	com_prot;
}BSM_OPT_T;

typedef struct{
    uint32_t 	funcid_0x0A;
	uint32_t 	funcid_0x81;
	uint32_t 	funcid_0x82; 	
	uint32_t 	funcid_0x83; 	
	uint32_t 	funcid_0x84;
    uint32_t 	funcid_0x85;
    uint32_t 	funcid_0x86;
    uint32_t 	funcid_0x87;
    uint32_t 	funcid_0x88;

}rxptu_cnter_t;

static rxptu_cnter_t rxcnter;
extern UART_HandleTypeDef huart2;


//操作系统变量
static struct rt_semaphore bsm_sem_rev = {0};

static BsmFrame06 Bsm_SetParam				= {0};  	//设置参数帧
static BsmFrame0A Bsm_FwInfo 		    	= {0};      //升级固件信息帧
static BsmFrame0B Bsm_FwUpdate 				= {0};   	//固件帧
static BsmFrame82 Bsm_SetInvBoostParam   	= {0};		//设置逆变和升压参事

uint8_t bsm_com_port = 0;

//缓冲区变量
static uint32_t Bsm_Rx_Lenth = 0; 				     //记录接收的长度
static uint8_t  APP_BSM_Buff_Tx [ BSM_DATA_TX_LENTH ];  //发送缓存区数组
static uint8_t  APP_BSM_Buff_Rx [ BSM_DATA_RX_LENTH ];  //接收缓存区数组
static uint8_t  APP_BSM_Ack_Buff   [ BSM_DATA_TX_LENTH ];  //应用层缓冲器


static char  APP_FW_DATA_Buff [ FW_BUFF_LEN ];  		 //接收缓存区数组

//标志位
static uint8_t bin_upgrade_step = 0;  //固件接收完标志位


BSM_OPT_T bsm_opt = {
	.rx_buff		= APP_BSM_Buff_Rx,     
	.tx_buff		= APP_BSM_Buff_Tx,
	.ack_buff		= APP_BSM_Ack_Buff,
	.com_prot		= 0,
	.rx_len			= 0,
	.max_data_len 	= BSM_DATA_RX_LENTH,
};

//函数声明
static int app_bsm_com_rx(uint8_t Dev_Addr,const uint8_t *pRxBuff,uint16_t Size);

extern void esp01_send_data(const uint8_t *pData, uint16_t len);
extern void rs485_send_frame(const void *buff, int size);
extern void fdcan_ptu_send_data(const uint8_t *pSendData, uint16_t Size);
/**
  * @brief 从接口复制数据流到应用层
  * @param 
  * @retval 
  */
void app_bsm_com_rev_byte(const uint8_t *pData,rt_size_t size)
{
    uint8_t result = -1;
    
	if (size > 0 && size < bsm_opt.max_data_len)
	{				
        result = api_bsm_check_frame((uint8_t*)pData);
        
		if (0 == result) 		// 检查帧头
		{	
            result = app_bsm_com_rx(DEV_ADDR, pData, size);
            
			if (0 == result) // 判断地址和校验
			{			
                if(DEV_ADDR == pData[3])
                    vfd.modbus_id_lock = 1;
                
				rt_memset(bsm_opt.rx_buff, 0, bsm_opt.max_data_len);   
				
				rt_memcpy(bsm_opt.rx_buff, pData, size);
				
				bsm_opt.rx_len = size;			// - 记录接收的长度
				bsm_opt.rx_buff[size] = 0;		// - 缓冲区最后一位清0
				
				rt_sem_release(&bsm_sem_rev);
			}          
		}			
	}
    
    
}
extern rt_uint8_t rt_kprintf_lock;
/**
  * @brief 发送数据流处理
  * @param 
  * @retval 
  */
static void app_bsm_com_tx(const uint8_t *pData,uint16_t Size)
{
	if(NULL == pData || 0 == Size)	return ;
	
	switch(bsm_com_port)
	{
		case dev_485:
			rs485_send_frame(pData, Size);
			break;
		case dev_esp:
			esp01_send_data(pData, Size);
			break;
		case dev_can:
			fdcan_ptu_send_data(pData, Size);
			break;
        case dev_console:
            toolbox_dma_printf_binary(pData,Size);
        
            break;
	
	}
	//rt_memset(bsm_opt.ack_buff, 0, sizeof(APP_BSM_Buff_Tx));
}

/**
  * @brief 接收数据流处理
  * @param 
  * @retval 
  */
static int app_bsm_com_rx(uint8_t Dev_Addr,const uint8_t *pRxBuff,uint16_t Size)
{
	int ret = 0; //返回值
	
	ret = api_bsm_check_addr_crc(Dev_Addr, pRxBuff, Size);
	
	if (-1 == ret)
	{
//		if (2 == bsm_com_port)
//			app_bsm_com_tx(pRxBuff, Size);  
//				
		return ret;
	}		
	else if (-2 == ret )
	{
		return -2;
	}
	
	return ret;		
}

//----------------------------------------------------------------------------
// 其他函数功能区
//----------------------------------------------------------------------------

/**
  * @brief  check crc 校验bin文件完整性
  * @param  filename
  * @retval result
  */
static int app_bin_crc_check(const char *f_name)
{
    int fd, len;
    uint16_t crc = 0xFFFF;
    char *bufs = &APP_FW_DATA_Buff[0];

    if ((fd = open(f_name, O_RDONLY | O_BINARY, 0666)) < 0)
    {
        rt_kprintf("open %s er.\r\n", f_name);
        return -1;
    }
    do
    {
        memset(bufs, 0, FW_BUFF_LEN);
        if ((len = read(fd, bufs, FW_BUFF_LEN)) > 0)
        {
            crc = CRC16_A001((void *)bufs, len, crc);
        }
    }
    while (len > 0);

    close(fd);
    return crc == 0 ? 0 : -1;
}

/**
  * @brief  app_send_data 
  * @param  
  * @retval 
  */
static uint16_t app_bsm_send_data(	uint8_t dev_addr,
									uint8_t func_frame,
									uint16_t data_lenth,
									uint16_t data_offset,
									void *pSendData,
									uint8_t * sendBuff)
{
    
    com_cnter_send(&com_ptu);
	// 1.封装协议数据
	uint16_t sendSize = api_bsm_com_package(	dev_addr,       	//设备地址
												func_frame,			//帧功能号
												data_lenth,  	    //数据大小
												data_offset,        //数据偏移量
												pSendData,          //数据源
												sendBuff);	  		//发送缓冲区
	// 2.发送数据
	app_bsm_com_tx(sendBuff, sendSize);
	
	return sendSize;

}
//----------------------------------------------------------------------------
// - 响应帧函数区
//----------------------------------------------------------------------------
/**
  * @brief F1.获取版本帧
  */
void bsm_unpack_get_version(void *frame)
{
	if(NULL == frame)
		return;
	
	BsmFramePackage *pBsmFP = (BsmFramePackage*)frame;
	BsmFrame01  *pSendStream = (BsmFrame01*)bsm_opt.ack_buff;
	{
	}	
	uint16_t sendSize = app_bsm_send_data(		pBsmFP->Dev_Addr,  	 //设备地址
												pBsmFP->Frame_Fun, 	 //帧功能号
												sizeof(BsmFrame01),  //数据大小
												0,                   //数据偏移量
												pSendStream,         //数据源
												bsm_opt.tx_buff);	 //发送缓冲区
}


/**
  * @brief F2.获取日期帧
  */
void bsm_unpack_get_date(void *frame)
{
	if(NULL == frame)
		return;

	BsmFramePackage *pBsmFP = (BsmFramePackage*)frame;
	BsmFrame02  *pSendStream = (BsmFrame02*)bsm_opt.ack_buff;
	{			
		pSendStream->Year  = vfd.date.year; 
		pSendStream->Month = vfd.date.month;
		pSendStream->Day   = vfd.date.day;  
		pSendStream->Hour  = vfd.date.hour; 
		pSendStream->Min   = vfd.date.min;  
		pSendStream->Sec   = vfd.date.sec; 
	}
	uint16_t sendSize = app_bsm_send_data(		pBsmFP->Dev_Addr,    //设备地址
												pBsmFP->Frame_Fun,   //帧功能号
												sizeof(BsmFrame02),  //数据大小
												0,                   //数据偏移量
												pSendStream,         //数据源
												bsm_opt.tx_buff);	 //发送缓冲区
}

/**
  * @brief F3.获取数字量输入帧
  */
void bsm_unpack_get_digital_input(void *frame)
{
	if(NULL == frame)	return;		
	BsmFramePackage *pBsmFP 		= (BsmFramePackage*)frame;;
}

/**
  * @brief F4.获取输出数字量帧
  */
void bsm_unpack_get_digital_out(void *frame)
{
	if(NULL == frame)	return;
	BsmFramePackage *pBsmFP = (BsmFramePackage*)frame;
}

/**
  * @brief F5.获取模拟通道值帧
  */
void bsm_unpack_get_adc_value(void *frame)
{
	if(NULL == frame)	return;	
	BsmFramePackage *pBsmFP = (BsmFramePackage*)frame;
}

/**
  * @brief F6.
  */
void bsm_unpack_reset_mcu(void *frame)
{
    if(NULL == frame)	return;
	BsmFramePackage *pBsmFP = (BsmFramePackage*)frame;
    uint8_t  *pSendStream 	= (uint8_t *)bsm_opt.ack_buff;
    int ack_size = 0;

    
    
	uint16_t sendSize = app_bsm_send_data(		pBsmFP->Dev_Addr,	//设备地址
												pBsmFP->Frame_Fun, 	//帧功能号
												ack_size, 			//数据大小
												0,                    				//数据偏移量
												bsm_opt.ack_buff,        				//数据源
												bsm_opt.tx_buff);	  				//发送缓冲区
    
    vfd.bit.soft_stop = 1;
    BSM_COM_PRINT("\r\n get reset cmd,delay 1s to reset mcu ");
            
    rt_thread_mdelay(1 * RT_TICK_PER_SECOND); 
    
    rt_hw_cpu_reset(); 	
}

/**
  * @brief F0A.保存固件信息帧
  */
void bsm_unpack_fw_info(void *frame)
{
	BsmFramePackage *pBsmFP = (BsmFramePackage*)frame;
	BsmFrame0A *pF0A = (BsmFrame0A *)pBsmFP->pData;
	
	rt_memset(&Bsm_FwInfo ,			0, sizeof(Bsm_FwInfo));
	rt_memset(&Bsm_FwUpdate.Ack, 	0, sizeof(Bsm_FwUpdate.Ack));
	rt_memcpy(&Bsm_FwInfo, 	  	 pF0A, sizeof(Bsm_FwInfo.Rev));
	int  fd;
	switch (Bsm_FwInfo.Rev.FWSW)
	{
		// 2.1 逆变固件升级	
		case FW_Inverter:
			vfd.is_update_fw = 1;
        
			unlink(BIN_FILE_NAME);				// - 删除旧的BIN文件
							
			Bsm_FwInfo.Ack.FWSW = FW_Inverter;  // - 逆变固件升级
			Bsm_FwInfo.Ack.EraseFlashFlag = 1; 	// - 擦除FLASH区成功
			
			if ((fd = open(BIN_FILE_NAME, O_CREAT)) < 0)
			{					
				rt_kprintf("open %s err.1\r\n", BIN_FILE_NAME);
			}
			else
			{
				Bsm_FwInfo.Ack.SaveBinFileInfoFlag = 1;  // - 保存固件信息成功					
				bin_upgrade_step = FW_FILE_DATA; 		 // - 标记开始接收文件
			}
			record_logdata_push(LOG_Inv_FW_Info,Bsm_FwInfo.Rev.BinVersionNum%100);
			close(fd);									
			break;
				
	}
	uint16_t sendSize = app_bsm_send_data(		DEV_ADDR,       	  		//设备地址
												UNPACK_FW_INFO_F0A,  		//帧功能号
												sizeof(Bsm_FwInfo.Ack),  //数据大小
												0,                    		//数据偏移量
												&Bsm_FwInfo.Ack,         //数据源
												bsm_opt.tx_buff);	  		//发送缓冲区
}

uint32_t bin_framecnt = 0;
/**
  * @brief F0B.固件更新帧
  */
void bsm_unpack_fw_update(void *frame)
{	
	BsmFramePackage *pBsmFP = (BsmFramePackage*)frame;
    static uint16_t offset_writed = 0;
	rt_uint16_t offset = pBsmFP->Data_offset;
    rt_uint16_t lenth  = pBsmFP->Data_Lenth;
	rt_memcpy(&Bsm_FwUpdate.Rev.BinFileFrameData, pBsmFP->pData, pBsmFP->Data_Lenth);	
	
	int  fd;
	switch (Bsm_FwInfo.Rev.FWSW)
	{
		// 1. 逆变固件升级	
		case FW_Inverter:	
		{	
            vfd.is_update_fw = 1;
            
			do
			{
				switch(bin_upgrade_step)
				{
					// - 1.1 接收固件帧	
					case FW_FILE_DATA:
						{		
                            Bsm_FwUpdate.Ack.Is_OneFrameRev_OK = 1;	// - 当前帧接收0K														
							Bsm_FwUpdate.Ack.BinFileFrameNum = offset; // -  帧序号
										
                            // - 最后1帧接收OK
							if (Bsm_FwInfo.Rev.BinSubSize != ((offset)+1))
                            {
                                uint16_t sendSize = app_bsm_send_data(		DEV_ADDR,       	  			//设备地址
																		UNPACK_FW_UPDATE_F0B,  			//帧功能号
																		sizeof(Bsm_FwUpdate.Ack),  	//数据大小
																		0,                    			//数据偏移量
																		&Bsm_FwUpdate.Ack,         	//数据源
																		bsm_opt.tx_buff);	  			//发送缓冲区
                            }
                                                                                
							// 过滤重复接收帧
							if( offset_writed && 
								offset_writed == offset)
							{
								rt_kprintf("frame repetition:%d\n",offset);
							}
							else
							{
								if ((fd = open(BIN_FILE_NAME, O_WRONLY | O_APPEND)) < 0)
								{
									rt_kprintf("open %s er.2\r\n", BIN_FILE_NAME);
								}
								int size = write(fd, Bsm_FwUpdate.Rev.BinFileFrameData, lenth);							
								close(fd);
                                
                                if(size > 0)
                                    bin_framecnt++;
                                
                                offset_writed = offset;
							}
                            
                            // - 最后1帧接收OK
							if (Bsm_FwInfo.Rev.BinSubSize == ((offset)+1))
							{	
                                
								Bsm_FwUpdate.Ack.Is_AllFrameRev_OK = 1; // - 所有帧接收OK														
								//----------------------------------------------------------------------------
								// 校验BIN文件
								//----------------------------------------------------------------------------	
								uint8_t crc_ret = app_bin_crc_check(BIN_FILE_NAME);
								if (crc_ret == 0)
								{
									bin_upgrade_step = FW_FILE_END;	
									Bsm_FwUpdate.Ack.Is_AllFrameCRC_OK = 1;		
                                    record_logdata_push(LOG_Inv_FW_CrcOk,0);                                    
								}
                                
                                uint16_t sendSize = app_bsm_send_data(		DEV_ADDR,       	  			//设备地址
																		UNPACK_FW_UPDATE_F0B,  			//帧功能号
																		sizeof(Bsm_FwUpdate.Ack),  	//数据大小
																		0,                    			//数据偏移量
																		&Bsm_FwUpdate.Ack,         	//数据源
																		bsm_opt.tx_buff);	  			//发送缓冲区
							}			
																														
							if(FW_FILE_END == bin_upgrade_step)
								//！ 先把消息反馈给上位机 - 再复位											
								rt_thread_mdelay(1 * RT_TICK_PER_SECOND); 
						}
					break;
								
					// - 2.2 复位 
					case FW_FILE_END:
						{					
							rt_hw_cpu_reset(); 					
						}
					break;
						
				}
			}while(FW_FILE_END ==  bin_upgrade_step);
		}
		break;
		
	}
}

/**
  * @brief F15.设置RTC时间
  */
void bsm_unpack_set_rtc(void *frame)
{
    BsmFramePackage *pBsmFP = (BsmFramePackage*)frame;
    
    if(pBsmFP->Data_Lenth == 4)
    {
        time_t now;
        rt_device_t device;
        
        now = *(uint32_t *)&pBsmFP->pData[0];
        
        timestamp_sync_rtc(now,5);
        
    }
    else if(pBsmFP->Data_Lenth == 6)
    {
        if((pBsmFP->pData[0] >= 22) && (pBsmFP->pData[1] >= 1) && (pBsmFP->pData[1] <= 12)
            && (pBsmFP->pData[2] >= 1) && (pBsmFP->pData[2] <= 31))
        {
            set_date(2000+pBsmFP->pData[0],pBsmFP->pData[1],pBsmFP->pData[2]);
            set_time(pBsmFP->pData[3],pBsmFP->pData[4],pBsmFP->pData[5]);
        }
    }
}

/**
  * @brief F16.设置baud
  */
void bsm_unpack_set_baud(void *frame)
{
    BsmFramePackage *pBsmFP = (BsmFramePackage*)frame;
    uint32_t baud = *(uint32_t*)&pBsmFP->pData[0];
    
    if((baud >= 9600) && (baud <= 921600))
    {
        extern int MX_USART2_UART_Init(uint32_t BaudRate);
        extern uint8_t rs485_ptu_set_baud;
        if(baud != huart2.Init.BaudRate)
        {
            rs485_ptu_set_baud = 1;
            __HAL_UART_DISABLE(&huart2);
            MX_USART2_UART_Init(baud);
        }
        
        *(uint32_t*)&bsm_opt.ack_buff[0] = (uint32_t)huart2.Init.BaudRate;
        uint16_t sendSize = app_bsm_send_data(	pBsmFP->Dev_Addr,   //设备地址
												pBsmFP->Frame_Fun,  //帧功能号
												4, //数据大小
												0,                  //数据偏移量
												bsm_opt.ack_buff,   //数据源
												bsm_opt.tx_buff);	//发送缓冲区	
    }
}

//#define DEBUG_PTU_81

#ifdef DEBUG_PTU_81
BsmFrame81 debug_81 = {0};
#endif
extern uint32_t  HardfaultNormalDelay;
extern int power_on_cnt;
/**
  * @brief F81.获取变频器状态 
  */
void bsm_unpack_get_state(void *frame)
{	
    static uint8_t prev_set = 0;
    time_t now;
	BsmFramePackage *pBsmFP 	= (BsmFramePackage*)frame;
    BsmFrame81_RPDO  *p81_RPDO  = (BsmFrame81_RPDO*)&pBsmFP->pData[0];
    
    rt_memset(bsm_opt.ack_buff,0,BSM_DATA_TX_LENTH);
    
    /* get current time */
    now = time(RT_NULL);
    
    {
        if(pBsmFP->Data_Lenth >= 4)
        {
            timestamp_sync_rtc(p81_RPDO->time_stamp,5);
        }
        
        if( p81_RPDO->bit0_valid)
        {
            vfd.is_update_fw = 0;
            
            if(prev_set == 0)
            {
                vfd.diag_stop_times = 0;
                HardfaultNormalDelay = 500;
            }
            
            if(p81_RPDO->bit1_dio_ctrl)
            {
                rt_memset(&vfd.manual,0,sizeof(vfd.manual));
            }
            else if(p81_RPDO->bit2_ptu_ctrl)
            {      
                vfd.manual.start_mask = p81_RPDO->mask.bit0;
                vfd.manual.start      = p81_RPDO->val.bit0;

            }
            
            //if(p81_RPDO->mask.bit1)
            {
                float freq = 0;
                freq = (float)p81_RPDO->inv_freq_set/100.0f;
                Inv_SetFreqRef(freq);
                
                if(vfd.bit.lock_stop) 
                    diag_lockstop_reset();
            }
        } 

        prev_set = p81_RPDO->bit0_valid;        
    }
    
    #ifdef DEBUG_PTU_81
    if(pSendStream != &debug_81)
    #endif
	{          
        toolbox_u32_set_data(&bsm_opt.ack_buff[0],1,vfd.ctrl.sys_mode);  /* system state */
        rt_memcpy(&bsm_opt.ack_buff[11] ,&vfd.diag,12);                  /* PFC fault code 1 */

        if((vfd.PIacin.Out < 0) && 
            ((motor.vout_ratio < 1.0f) || (motor.freq_ratio < 1.0f)))
            toolbox_u32_set_data(&bsm_opt.ack_buff[23],1,0x7);        /* pfc state */
        else 
            toolbox_u32_set_data(&bsm_opt.ack_buff[23],1,vfd.ctrl.pfc_st);        /* pfc state */
        
        if((vfd.PIdcin.Out < 0) && 
            ((motor.vout_ratio < 1.0f) || (motor.freq_ratio < 1.0f)))
            toolbox_u32_set_data(&bsm_opt.ack_buff[24],1,0x7);      /* boost state */
        else
            toolbox_u32_set_data(&bsm_opt.ack_buff[24],1,vfd.ctrl.boost_st);      /* boost state */
        
        if((vfd.PIacout.Out < 0) && 
            ((motor.vout_ratio < 1.0f) || (motor.freq_ratio < 1.0f)))
        {
            if(vfd.ctrl.start_boost)
                toolbox_u32_set_data(&bsm_opt.ack_buff[25],1,0x8);        /* inv state */
            else
                toolbox_u32_set_data(&bsm_opt.ack_buff[25],1,0x7);        /* inv state */
        }
        else
            toolbox_u32_set_data(&bsm_opt.ack_buff[25],1,vfd.ctrl.inv_st);        /* inv state */
        
        toolbox_u32_set_data(&bsm_opt.ack_buff[26] ,4,nvs_datas.accumulator.total_running_time/360);   /* power-on time */
        toolbox_u32_set_data(&bsm_opt.ack_buff[30] ,4,rt_tick_get()/RT_TICK_PER_SECOND);   /* power-on time */
        toolbox_u32_set_data(&bsm_opt.ack_buff[34] ,4,nvs_datas.accumulator.inv_run_time);     /*  inv runtime */
        
        toolbox_u32_set_data(&bsm_opt.ack_buff[38] ,2,vfd.ad.temp_cpu/10);     /*  cpu temp  */
        toolbox_u32_set_data(&bsm_opt.ack_buff[40] ,2,vfd.ad.dc_5V);        /*  cpu temp  */
        
        toolbox_u32_set_data(&bsm_opt.ack_buff[42] ,2,VFD_SOFT_VERSION);  

        uint32_t data = nvs_datas.accumulator.power_consumption;
        
        toolbox_u32_set_data(&bsm_opt.ack_buff[44] ,4,data);        /*  cpu temp  */
        
        data = vfd.powerconsumption_now;
        toolbox_u32_set_data(&bsm_opt.ack_buff[48] ,4,data);        /*  cpu temp  */
        
        toolbox_u32_set_data(&bsm_opt.ack_buff[52] ,1,1);     /*  motor type 1 acim  */
        toolbox_u32_set_data(&bsm_opt.ack_buff[53] ,1,vfd.can_addr);                    /*  can addr  */ 
        

        toolbox_u32_set_data(&bsm_opt.ack_buff[54] ,2,vfd.capacity_in);        /*  cpu temp  */                                
        toolbox_u32_set_data(&bsm_opt.ack_buff[56] ,2,vfd.capacity_out);        /*  cpu temp  */  
        
        uint16_t hard_ver = vfd.ctrl_hw_ver;
        toolbox_u32_set_data(&bsm_opt.ack_buff[58] ,2,hard_ver);        /*  cpu temp  */  
 
        toolbox_u32_set_data(&bsm_opt.ack_buff[60] ,1,vfd.serial_addr);      /*  cpu temp  */  
        
        toolbox_u32_set_data(&bsm_opt.ack_buff[61] ,1,vfd.SetMoInfo.Motor_NumberofPoles);        /*  cpu temp  */ 
        toolbox_u32_set_data(&bsm_opt.ack_buff[62] ,2,vfd.ad.vsr);        /*  cpu temp  */  
        toolbox_u32_set_data(&bsm_opt.ack_buff[64] ,2,vfd.ad.dc_15V);        /*  cpu temp  */  
        
        toolbox_u32_set_data(&bsm_opt.ack_buff[66] ,2,vfd.ad.ac_vin_u/10);        /*  cpu temp  */  
        toolbox_u32_set_data(&bsm_opt.ack_buff[68] ,2,vfd.ad.ac_vin_v/10);        /*  cpu temp  */  
        toolbox_u32_set_data(&bsm_opt.ack_buff[70] ,2,vfd.ad.ac_vin_w/10);        /*  cpu temp  */  

        toolbox_u32_set_data(&bsm_opt.ack_buff[72] ,2,vfd.ad.ac_iin_u);        /*  cpu temp  */  
        toolbox_u32_set_data(&bsm_opt.ack_buff[74] ,2,vfd.ad.ac_iin_v);        /*  cpu temp  */  
        toolbox_u32_set_data(&bsm_opt.ack_buff[76] ,2,vfd.ad.ac_iin_w);        /*  cpu temp  */ 
 
        toolbox_u32_set_data(&bsm_opt.ack_buff[78] ,2,(int16_t)(vfd.acin_freq*10));        /*  cpu temp  */ 
        toolbox_u32_set_data(&bsm_opt.ack_buff[80] ,2,vfd.ad.temp_pfc/10);        /*  cpu temp  */ 
        toolbox_u32_set_data(&bsm_opt.ack_buff[82] ,2,vfd.ad.dc_vinbus);        /*  cpu temp  */ 
        toolbox_u32_set_data(&bsm_opt.ack_buff[84] ,2,vfd.ad.temp_CAP/10);        /*  cpu temp  */ 
        toolbox_u32_set_data(&bsm_opt.ack_buff[86] ,2,vfd.ad.dc_Iin);        /*  cpu temp  */ 
        toolbox_u32_set_data(&bsm_opt.ack_buff[88] ,2,vfd.ad.vsr2);        /*  cpu temp  */ 
        toolbox_u32_set_data(&bsm_opt.ack_buff[90] ,2,vfd.ad.temp_Hb/10);        /*  cpu temp  */ 
        toolbox_u32_set_data(&bsm_opt.ack_buff[92] ,2,vfd.ad.vbus_pfc/10);        /*  cpu temp  */ 
        
        toolbox_u32_set_data(&bsm_opt.ack_buff[94] ,2,vfd.ad.ac_iout_u);        /*  cpu temp  */  
        toolbox_u32_set_data(&bsm_opt.ack_buff[96] ,2,vfd.ad.ac_iout_v);        /*  cpu temp  */  
        toolbox_u32_set_data(&bsm_opt.ack_buff[98] ,2,vfd.ad.ac_iout_w);        /*  cpu temp  */ 
        
        toolbox_u32_set_data(&bsm_opt.ack_buff[100] ,2,EPAD.MainVoltage/10);        /*  cpu temp  */ 
        toolbox_u32_set_data(&bsm_opt.ack_buff[102] ,2,(vfd.ctrl.inv_st == SYS_InvRun) ? (int)(Inv_GetFreqNow()*100) : 0);        /*  cpu temp  */ 
        
        
        toolbox_u32_set_data(&bsm_opt.ack_buff[104] ,4,nvs_datas.accumulator.pfc_startup_time);        /*  cpu temp  */ 
        toolbox_u32_set_data(&bsm_opt.ack_buff[108] ,2,vfd.ad.temp_IGBT_L1/10);        /*  cpu temp  */ 
        
        toolbox_u32_set_data(&bsm_opt.ack_buff[110] ,2,vfd.ctrl.motor_speed);        /*  cpu temp  */ 
        
        U32_SET_BIT(bsm_opt.ack_buff[112] ,0,vfd.bit.com_can);        
        U32_SET_BIT(bsm_opt.ack_buff[112] ,1,vfd.bit.com_485);
        U32_SET_BIT(bsm_opt.ack_buff[112] ,2,DIO_READ_BIT(FS_DR_PIN));
        U32_SET_BIT(bsm_opt.ack_buff[112] ,3,vfd.io.kmon1);
        U32_SET_BIT(bsm_opt.ack_buff[112] ,4,vfd.io.kmon2);
        U32_SET_BIT(bsm_opt.ack_buff[112] ,5,vfd.io.kmon3);
        U32_SET_BIT(bsm_opt.ack_buff[112] ,6,DIO_READ_BIT(ADDR1_PIN));       
        U32_SET_BIT(bsm_opt.ack_buff[112] ,7,DIO_READ_BIT(ADDR2_PIN));   
        
        U32_SET_BIT(bsm_opt.ack_buff[113] ,0,vfd.io.in1);     
        U32_SET_BIT(bsm_opt.ack_buff[113] ,1,vfd.io.in2);     
        U32_SET_BIT(bsm_opt.ack_buff[113] ,2,vfd.io.in3);     
        U32_SET_BIT(bsm_opt.ack_buff[113] ,3,vfd.io.in4);     
        U32_SET_BIT(bsm_opt.ack_buff[113] ,4,vfd.io.o1);   
        U32_SET_BIT(bsm_opt.ack_buff[113] ,5,vfd.io.o2);    
        U32_SET_BIT(bsm_opt.ack_buff[113] ,6,vfd.io.o3);    
        U32_SET_BIT(bsm_opt.ack_buff[113] ,7,(DIO_READ_BIT(FO1_PIN) || (DIO_IRQ_DELAY(FO1_PIN) > 0)));  
        
        U32_SET_BIT(bsm_opt.ack_buff[114] ,0,(DIO_READ_BIT(FO2_PIN) || (DIO_IRQ_DELAY(FO2_PIN) > 0)));     
        U32_SET_BIT(bsm_opt.ack_buff[114] ,1,(DIO_READ_BIT(FO3_PIN) || (DIO_IRQ_DELAY(FO3_PIN) > 0))); 
        U32_SET_BIT(bsm_opt.ack_buff[114] ,2,(DIO_READ_BIT(POW_PIN) && (DIO_IRQ_DELAY(POW_PIN) <= 0)));      
        U32_SET_BIT(bsm_opt.ack_buff[114] ,3,!DIO_READ_BIT(CLR_HD_PIN));   
        U32_SET_BIT(bsm_opt.ack_buff[114] ,4,!DIO_READ_BIT(FS_DR_B_PIN));        
        U32_SET_BIT(bsm_opt.ack_buff[114] ,5,(!DIO_READ_BIT(F_HD_PIN) || (DIO_IRQ_DELAY(F_HD_PIN) > 0)));        
        U32_SET_BIT(bsm_opt.ack_buff[114] ,6,(DIO_READ_BIT(FAN1_F_PIN) || (DIO_IRQ_DELAY(FAN1_F_PIN) > 0)));       
        U32_SET_BIT(bsm_opt.ack_buff[114] ,7,(DIO_READ_BIT(FAN2_F_PIN) || (DIO_IRQ_DELAY(FAN2_F_PIN) > 0)));  
        
        U32_SET_BIT(bsm_opt.ack_buff[115] ,0,(!DIO_READ_BIT(F_IPM_DC_PIN)  || (DIO_IRQ_DELAY(F_IPM_DC_PIN) > 0))); 
        U32_SET_BIT(bsm_opt.ack_buff[115] ,1,(!DIO_READ_BIT(F_IPM_PFC_PIN) || (DIO_IRQ_DELAY(F_IPM_PFC_PIN) > 0)));   
        U32_SET_BIT(bsm_opt.ack_buff[115] ,2,(!DIO_READ_BIT(F_IPM_INV_PIN) || (DIO_IRQ_DELAY(F_IPM_INV_PIN) > 0)));    
        U32_SET_BIT(bsm_opt.ack_buff[115] ,3,(!DIO_READ_BIT(OVP_P_BUS_PIN) || (DIO_IRQ_DELAY(OVP_P_BUS_PIN) > 0)));    
        U32_SET_BIT(bsm_opt.ack_buff[115] ,4,(!DIO_READ_BIT(OCP_N_PIN)     || (DIO_IRQ_DELAY(OCP_N_PIN) > 0))); 
        U32_SET_BIT(bsm_opt.ack_buff[115] ,5,(!DIO_READ_BIT(OCP_P_PIN)     || (DIO_IRQ_DELAY(OCP_P_PIN) > 0)));  
        U32_SET_BIT(bsm_opt.ack_buff[115] ,6,(!DIO_READ_BIT(ICP_N_PIN)     || (DIO_IRQ_DELAY(ICP_N_PIN) > 0)));  
        U32_SET_BIT(bsm_opt.ack_buff[115] ,7,(!DIO_READ_BIT(ICP_P_PIN)     || (DIO_IRQ_DELAY(ICP_P_PIN) > 0)));    
        
        toolbox_u32_set_data(&bsm_opt.ack_buff[116] ,2,EPST.InvPwmDuty);        /*  cpu temp  */ 
        
        toolbox_u32_set_data(&bsm_opt.ack_buff[118] ,2,vfd.ad.ac_vout/10);        /*  cpu temp  */ 
        
        data = nvs_datas.accumulator.dc_power_consumption;
        toolbox_u32_set_data(&bsm_opt.ack_buff[120] ,4,data);        /*  cpu temp  */ 
        data = vfd.dc_powerconsumption_now;
        toolbox_u32_set_data(&bsm_opt.ack_buff[124] ,4,data);        /*  cpu temp  */ 
        
        toolbox_u32_set_data(&bsm_opt.ack_buff[128] ,4,nvs_datas.accumulator.vfd_poweron_time);        /*  cpu temp  */ 
        
        
        toolbox_u32_set_data(&bsm_opt.ack_buff[132] ,2,TEMP_DCDC_TRANSF/10);    /*  */ 
        toolbox_u32_set_data(&bsm_opt.ack_buff[134] ,2,TEMP_PFC_L1/10);         /*  */ 
        toolbox_u32_set_data(&bsm_opt.ack_buff[136] ,2,(vfd.ad.ptvref_3_3V+50)/100);        /*  cpu temp  */ 
        
        if(vfd.boost->state == BOOST_RUN)
            toolbox_u32_set_data(&bsm_opt.ack_buff[138] ,4,boost.machine_cnt/1000);        /*  cpu temp  */ 
        else
            toolbox_u32_set_data(&bsm_opt.ack_buff[138] ,4,0);        /*  cpu temp  */ 
        
        //vfd.dcdc_power = motor.dcdc_power;
        toolbox_u32_set_data(&bsm_opt.ack_buff[142] ,2,vfd.dcdc_power*0.1f);
        
        toolbox_u32_set_data(&bsm_opt.ack_buff[144] ,2,vfd.ac_rated_power*0.1f);
        
        toolbox_u32_set_data(&bsm_opt.ack_buff[146] ,2,TEMP_CMC/10); /*  新增三相共膜电感温度 */ 
        
        toolbox_u32_set_data(&bsm_opt.ack_buff[188] ,2,VFD_SOFT_VERSION);        /*  cpu temp  */
        toolbox_u32_set_data(&bsm_opt.ack_buff[190] ,2,VFD_SOFT_VERSION);        /*  cpu temp  */
	}	
    
    toolbox_u32_set_data(&bsm_opt.ack_buff[204] ,4,now);               /* RTC Timestamp */
    
	uint16_t sendSize = app_bsm_send_data(		pBsmFP->Dev_Addr,   //设备地址
												pBsmFP->Frame_Fun,  //帧功能号
												208,                //数据大小
												0,                  //数据偏移量
												bsm_opt.ack_buff,   //数据源
												bsm_opt.tx_buff);	//发送缓冲区	
}

/**
  * @brief F82.设置逆变模块和PFC参数帧帧
  */
void bsm_unpack_set_invpfc_param(void *frame)
{
	if(NULL == frame)	return;
	
	BsmFramePackage *pBsmFP = (BsmFramePackage*)frame;
	BsmFrame82 *pRecvStream = (BsmFrame82*) pBsmFP->pData;
	BsmFrame82 *pSendStream = (BsmFrame82 *)bsm_opt.ack_buff;
	rt_memcpy(pSendStream, pRecvStream, sizeof(BsmFrame82));
    
	{			
        vfd.manual.o1_mask  = (pRecvStream->Ctrl_O1 == 0) ? 0 : 1;
        vfd.manual.o1       = (pRecvStream->Ctrl_O1 == 1) ? 1 : 0;
        vfd.manual.o2_mask  = (pRecvStream->Ctrl_O2 == 0) ? 0 : 1;
        vfd.manual.o2       = (pRecvStream->Ctrl_O2 == 1) ? 1 : 0;
        vfd.manual.o3_mask  = (pRecvStream->Ctrl_O3 == 0) ? 0 : 1;
        vfd.manual.o3       = (pRecvStream->Ctrl_O3 == 1) ? 1 : 0;
        vfd.manual.in1_mask = (pRecvStream->Ctrl_I1 == 0) ? 0 : 1;
        vfd.manual.in1      = (pRecvStream->Ctrl_I1 == 1) ? 1 : 0;
        vfd.manual.in2_mask = (pRecvStream->Ctrl_I2 == 0) ? 0 : 1;
        vfd.manual.in2      = (pRecvStream->Ctrl_I2 == 1) ? 1 : 0;
        vfd.manual.in3_mask = (pRecvStream->Ctrl_I3 == 0) ? 0 : 1;
        vfd.manual.in3      = (pRecvStream->Ctrl_I3 == 1) ? 1 : 0;
        vfd.manual.in4_mask = (pRecvStream->Ctrl_I4 == 0) ? 0 : 1;
        vfd.manual.in4      = (pRecvStream->Ctrl_I4 == 1) ? 1 : 0;
        
        vfd.manual.kmon1_mask = (pRecvStream->Ctrl_KMON1 == 0) ? 0 : 1;
        vfd.manual.kmon1      = (pRecvStream->Ctrl_KMON1 == 1) ? 1 : 0;
        vfd.manual.kmon2_mask = (pRecvStream->Ctrl_KMON2 == 0) ? 0 : 1;
        vfd.manual.kmon2      = (pRecvStream->Ctrl_KMON2 == 1) ? 1 : 0;
        vfd.manual.kmon3_mask = (pRecvStream->Ctrl_KMON3 == 0) ? 0 : 1;
        vfd.manual.kmon3      = (pRecvStream->Ctrl_KMON3 == 1) ? 1 : 0;
        
        nvs_datas.accumulator.kmon2_off_time         = pRecvStream->kmon2_off_time;
        nvs_datas.accumulator.kmon2_on_time          = pRecvStream->kmon2_on_time;
        nvs_datas.accumulator.pfc_startup_time       = pRecvStream->pfc_startup_time;
        nvs_datas.accumulator.total_running_time     = pRecvStream->sys_run_time;
        nvs_datas.accumulator.vfd_poweron_time       = pRecvStream->vfd_poweron_time;
        nvs_datas.accumulator.inv_run_time           = pRecvStream->inv_run_time;
        nvs_datas.accumulator.power_consumption      = pRecvStream->ac_power_consumption;
        nvs_datas.accumulator.dc_power_consumption   = pRecvStream->dc_power_consumption;
        
        vfd.manual.pfc_u_duty = (pRecvStream->Ctrl_PFC_U_Duty > 100) ? 100 : pRecvStream->Ctrl_PFC_U_Duty;
        vfd.manual.pfc_v_duty = (pRecvStream->Ctrl_PFC_V_Duty > 100) ? 100 : pRecvStream->Ctrl_PFC_V_Duty;
        vfd.manual.pfc_w_duty = (pRecvStream->Ctrl_PFC_W_Duty > 100) ? 100 : pRecvStream->Ctrl_PFC_W_Duty;
        vfd.manual.inv_u_duty = (pRecvStream->Ctrl_Inv_U_Duty > 100) ? 100 : pRecvStream->Ctrl_Inv_U_Duty;
        vfd.manual.inv_v_duty = (pRecvStream->Ctrl_Inv_V_Duty > 100) ? 100 : pRecvStream->Ctrl_Inv_V_Duty;
        vfd.manual.inv_w_duty = (pRecvStream->Ctrl_Inv_W_Duty > 100) ? 100 : pRecvStream->Ctrl_Inv_W_Duty;
        vfd.manual.dcdc_duty  = (pRecvStream->Ctrl_DCDC_Duty  > 100) ? 100 : pRecvStream->Ctrl_DCDC_Duty;
        
        if((pRecvStream->SetInvOutputFreq > 0) && (pRecvStream->SetInvOutputFreq <= 20000))
            nvsdata_write_one_mbreg("3009",pRecvStream->SetInvOutputFreq);
        
        if((pRecvStream->SetEmergInvOutputFreq > 0) && (pRecvStream->SetEmergInvOutputFreq <= 20000))
            nvsdata_write_one_mbreg("300D",pRecvStream->SetEmergInvOutputFreq);
        
        if((pRecvStream->SetPfcWorkVolt >= 100) && (pRecvStream->SetPfcWorkVolt <= 720))
            nvsdata_write_one_mbreg("300A",pRecvStream->SetPfcWorkVolt);
        
        if((pRecvStream->SetInvOutputRpm > 0) && (pRecvStream->SetInvOutputRpm <= 6000))
            nvsdata_write_one_mbreg("300B",pRecvStream->SetInvOutputRpm);
        
        if((pRecvStream->SetDcdcWorkMinute > 0) && (pRecvStream->SetDcdcWorkMinute <= 24*60))
            nvsdata_write_one_mbreg("300C",pRecvStream->SetDcdcWorkMinute);
        
        if( ((pRecvStream->VF_Linear_F1 > 0) && (pRecvStream->VF_Linear_F1 <= 20000))    &&
            ((pRecvStream->VF_Linear_V1 > 0) && (pRecvStream->VF_Linear_V1 <= 5000))     &&
            ((pRecvStream->VF_Linear_F2 > 0) && (pRecvStream->VF_Linear_F2 <= 20000))    &&
            ((pRecvStream->VF_Linear_V2 > 0) && (pRecvStream->VF_Linear_V2 <= 5000))     &&
            ((pRecvStream->VF_Linear_F3 > 0) && (pRecvStream->VF_Linear_F3 <= 20000))    &&
            ((pRecvStream->VF_Linear_V3 > 0) && (pRecvStream->VF_Linear_V3 <= 5000))    
        )
        {
            nvsdata_write_one_mbreg("3003",pRecvStream->VF_Linear_F1);
            nvsdata_write_one_mbreg("3004",pRecvStream->VF_Linear_V1);
            nvsdata_write_one_mbreg("3005",pRecvStream->VF_Linear_F2);
            nvsdata_write_one_mbreg("3006",pRecvStream->VF_Linear_V2);
            nvsdata_write_one_mbreg("3007",pRecvStream->VF_Linear_F3);
            nvsdata_write_one_mbreg("3008",pRecvStream->VF_Linear_V3);
        }
        
        if((pRecvStream->VF_Linear_StepUp >= 10) && (pRecvStream->VF_Linear_StepUp <= 10000))
        {
            nvsdata_write_one_mbreg("3001",pRecvStream->VF_Linear_StepUp);
        }

        if((pRecvStream->VF_Linear_StepDown >= 10) && (pRecvStream->VF_Linear_StepDown <= 10000))
        {
            nvsdata_write_one_mbreg("3002",pRecvStream->VF_Linear_StepDown);
        }
        
        vfreq_linear_config_update();

        rt_memcpy(&nvs_datas.accumulator.work_powerin_lvl1,&pRecvStream->Inv_BusVol_Cal,22*4);

        if(pBsmFP->Data_Lenth > 400)
            rt_memcpy(&nvs_datas.accumulator.work_temp1_lvl7,  &pRecvStream->statistics_data,50*4); 
        
		rt_memcpy(&vfd.SetMoInfo,   &pRecvStream->Inv_Motor_Manufacturer, sizeof(vfd.SetMoInfo));
		rt_memcpy(&vfd.SetHwVer,    &pRecvStream->DriveBoardHardware,     sizeof(vfd.SetHwVer));
		rt_memcpy(&vfd.SetCanAddr,  &pRecvStream->Can_X_Adrr,             sizeof(vfd.SetCanAddr));
        
        if(pRecvStream->Ctrl_ClearAll)
        {
            vfd.manual.o1 = 0;
            vfd.manual.o2 = 0;
            vfd.manual.o3 = 0;
            vfd.manual.in1 = 0;
            vfd.manual.in2 = 0;
            vfd.manual.in3 = 0;
            vfd.manual.in4 = 0;
            vfd.manual.o1_mask = 0;
            vfd.manual.o2_mask = 0;
            vfd.manual.o3_mask = 0;
            vfd.manual.in1_mask = 0;
            vfd.manual.in2_mask = 0;
            vfd.manual.in3_mask = 0;
            vfd.manual.in4_mask = 0;
            vfd.manual.kmon1 = 0;
            vfd.manual.kmon2 = 0;
            vfd.manual.kmon1_mask = 0;
            vfd.manual.kmon2_mask = 0;
            
            vfd.manual.inv_u_duty = 0;
            vfd.manual.inv_v_duty = 0;
            vfd.manual.inv_w_duty = 0;
            vfd.manual.pfc_u_duty = 0;
            vfd.manual.pfc_v_duty = 0;
            vfd.manual.pfc_w_duty = 0;
            vfd.manual.dcdc_duty  = 0;
        }
		
        if(pRecvStream->clear_all_timer)
        {
            nvs_datas.accumulator.kmon2_off_time         =  0;
            nvs_datas.accumulator.kmon2_on_time          =  0;
            nvs_datas.accumulator.pfc_startup_time       =  0;
            nvs_datas.accumulator.total_running_time     =  0;
            nvs_datas.accumulator.vfd_poweron_time       =  0;
            nvs_datas.accumulator.inv_run_time           =  0;
            nvs_datas.accumulator.power_consumption      =  0;
            nvs_datas.accumulator.dc_power_consumption   =  0;
 
            rt_memset(&nvs_datas.accumulator.work_powerin_lvl1, 0, 	 72*4);  
        }
        
        nvs_datas.config.serial_com_id = pRecvStream->Serial_Adrr;

        vfd.SetAdCa.PFC_R_InVol_Cal = (int32_t)pRecvStream->adca_vin_r;
        vfd.SetAdCa.PFC_S_InVol_Cal = (int32_t)pRecvStream->adca_vin_s;
        vfd.SetAdCa.PFC_T_InVol_Cal = (int32_t)pRecvStream->adca_vin_t;

        vfd.SetAdCa.PFC_R_InCur_Cal = (int32_t)pRecvStream->adca_iin_r;
        vfd.SetAdCa.PFC_S_InCur_Cal = (int32_t)pRecvStream->adca_iin_s;
        vfd.SetAdCa.PFC_T_ICur_Cal  = (int32_t)pRecvStream->adca_iin_t;

        vfd.SetAdCa.Inv_U_OutCur_Cal = (int32_t)pRecvStream->adca_iout_u;
        vfd.SetAdCa.Inv_V_OutCur_Cal = (int32_t)pRecvStream->adca_iout_v;
        vfd.SetAdCa.Inv_W_OutCur_Cal = (int32_t)pRecvStream->adca_iout_w;
 
        vfd.SetAdCa.Inv_BusVol_Cal = (int32_t)pRecvStream->adca_vbus_inv;
        vfd.SetAdCa.PFC_BusVol_Cal = (int32_t)pRecvStream->adca_vbus_pfc;
        
        nvs_datas.config.rs485_baudrate  = pRecvStream->rs485_baudrate;
        nvs_datas.config.rs485_check_bit = pRecvStream->rs485_check_bit;
        nvs_datas.config.can_baudrate    = pRecvStream->can_baudrate;
        
        vfd.nvs_write_config_flag = 1;
		WriteSetInfo();
        nvsdata_write_acc();
        // readback NVS data to ask
		ReadSetInfo();
        
        rt_thread_delay(RT_TICK_PER_SECOND/10);
        
        pSendStream->Ctrl_O1 = vfd.manual.o1_mask ? (vfd.manual.o1 ? 1 : 2) : 0;
        pSendStream->Ctrl_O2 = vfd.manual.o2_mask ? (vfd.manual.o2 ? 1 : 2) : 0;
        pSendStream->Ctrl_O3 = vfd.manual.o3_mask ? (vfd.manual.o3 ? 1 : 2) : 0;
        
        pSendStream->Ctrl_I1 = vfd.manual.in1_mask ? (vfd.manual.in1 ? 1 : 2) : 0;
        pSendStream->Ctrl_I2 = vfd.manual.in2_mask ? (vfd.manual.in2 ? 1 : 2) : 0;
        pSendStream->Ctrl_I3 = vfd.manual.in3_mask ? (vfd.manual.in3 ? 1 : 2) : 0;
        pSendStream->Ctrl_I3 = vfd.manual.in4_mask ? (vfd.manual.in4 ? 1 : 2) : 0;
        
        pSendStream->Ctrl_KMON1 = vfd.manual.kmon1_mask ? (vfd.manual.kmon1 ? 1 : 2) : 0;
        pSendStream->Ctrl_KMON2 = vfd.manual.kmon2_mask ? (vfd.manual.kmon2 ? 1 : 2) : 0;
        pSendStream->Ctrl_KMON3 = vfd.manual.kmon3_mask ? (vfd.manual.kmon3 ? 1 : 2) : 0;
        
        pSendStream->Ctrl_Inv_U_Duty = vfd.manual.inv_u_duty;
        pSendStream->Ctrl_Inv_V_Duty = vfd.manual.inv_v_duty;
        pSendStream->Ctrl_Inv_W_Duty = vfd.manual.inv_w_duty;
        pSendStream->Ctrl_PFC_U_Duty = vfd.manual.pfc_u_duty;
        pSendStream->Ctrl_PFC_V_Duty = vfd.manual.pfc_v_duty;
        pSendStream->Ctrl_PFC_W_Duty = vfd.manual.pfc_w_duty;
        pSendStream->Ctrl_DCDC_Duty  = vfd.manual.dcdc_duty;
        
        pSendStream->clear_all_timer = 0;
        pSendStream->Ctrl_ClearAll = 0;
        
        pSendStream->kmon2_off_time         =  nvs_datas.accumulator.kmon2_off_time        ;
        pSendStream->kmon2_on_time          =  nvs_datas.accumulator.kmon2_on_time         ;
        pSendStream->pfc_startup_time       =  nvs_datas.accumulator.pfc_startup_time      ;
        pSendStream->sys_run_time           =  nvs_datas.accumulator.total_running_time    ;
        pSendStream->vfd_poweron_time       =  nvs_datas.accumulator.vfd_poweron_time      ;
        pSendStream->inv_run_time           =  nvs_datas.accumulator.inv_run_time          ;
        pSendStream->ac_power_consumption   =  nvs_datas.accumulator.power_consumption     ;
        pSendStream->dc_power_consumption   =  nvs_datas.accumulator.dc_power_consumption  ;
        
		rt_memcpy(&pSendStream->Inv_BusVol_Cal, 		&nvs_datas.accumulator.work_powerin_lvl1, 	 22*4);
        rt_memcpy(&pSendStream->statistics_data, 		&nvs_datas.accumulator.work_temp1_lvl7, 	 50*4);
        
		rt_memcpy(&pSendStream->Inv_Motor_Manufacturer, &vfd.SetMoInfo, 	 sizeof(vfd.SetMoInfo));
		rt_memcpy(&pSendStream->DriveBoardHardware, 	&vfd.SetHwVer,  	 sizeof(vfd.SetHwVer));
		rt_memcpy(&pSendStream->Can_X_Adrr, 			&vfd.SetCanAddr,	 sizeof(vfd.SetCanAddr));		
        
        
        if((vfd.addr == 0) && (nvs_datas.config.serial_com_id != 0))
        {
            vfd.serial_addr = nvs_datas.config.serial_com_id;
            pSendStream->Serial_Adrr = nvs_datas.config.serial_com_id;
        }
        
        pSendStream->VF_Linear_StepUp   = nvs_datas.modbus.reg_3000[0x0001];
        pSendStream->VF_Linear_StepDown = nvs_datas.modbus.reg_3000[0x0002];
        pSendStream->VF_Linear_F1 = nvs_datas.modbus.reg_3000[0x0003];
        pSendStream->VF_Linear_V1 = nvs_datas.modbus.reg_3000[0x0004];
        pSendStream->VF_Linear_F2 = nvs_datas.modbus.reg_3000[0x0005];
        pSendStream->VF_Linear_V2 = nvs_datas.modbus.reg_3000[0x0006];
        pSendStream->VF_Linear_F3 = nvs_datas.modbus.reg_3000[0x0007];
        pSendStream->VF_Linear_V3 = nvs_datas.modbus.reg_3000[0x0008];
        
        pSendStream->SetInvOutputFreq   = nvs_datas.modbus.reg_3000[0x0009];
        pSendStream->SetEmergInvOutputFreq   = nvs_datas.modbus.reg_3000[0x000D];
        pSendStream->SetPfcWorkVolt     = nvs_datas.modbus.reg_3000[0x000A];
        
        SetSdpll3P_Vref(nvs_datas.modbus.reg_3000[0x000A]);
        
        pSendStream->SetInvOutputRpm    = nvs_datas.modbus.reg_3000[0x000B];
        pSendStream->SetDcdcWorkMinute  = nvs_datas.modbus.reg_3000[0x000C];
        
        pSendStream->adca_vin_r  = (int16_t)vfd.SetAdCa.PFC_R_InVol_Cal  ;
        pSendStream->adca_vin_s  = (int16_t)vfd.SetAdCa.PFC_S_InVol_Cal  ;
        pSendStream->adca_vin_t  = (int16_t)vfd.SetAdCa.PFC_T_InVol_Cal  ;
                                                                 
        pSendStream->adca_iin_r  = (int16_t)vfd.SetAdCa.PFC_R_InCur_Cal  ;
        pSendStream->adca_iin_s  = (int16_t)vfd.SetAdCa.PFC_S_InCur_Cal  ;
        pSendStream->adca_iin_t  = (int16_t)vfd.SetAdCa.PFC_T_ICur_Cal   ;
                                                                 
        pSendStream->adca_iout_u =  (int16_t)vfd.SetAdCa.Inv_U_OutCur_Cal;
        pSendStream->adca_iout_v =  (int16_t)vfd.SetAdCa.Inv_V_OutCur_Cal;
        pSendStream->adca_iout_w =  (int16_t)vfd.SetAdCa.Inv_W_OutCur_Cal;
                                                                 
        pSendStream->adca_vbus_inv = (int16_t)vfd.SetAdCa.Inv_BusVol_Cal ;
        pSendStream->adca_vbus_pfc = (int16_t)vfd.SetAdCa.PFC_BusVol_Cal ;
        
        pSendStream->rs485_baudrate  = nvs_datas.config.rs485_baudrate  ;
        pSendStream->rs485_check_bit = nvs_datas.config.rs485_check_bit ;
        pSendStream->can_baudrate    = nvs_datas.config.can_baudrate    ;
        
        pSendStream->ControlBoardHardware = vfd.ctrl_hw_ver;
        
        pSendStream->run_duty = nvs_datas.accumulator.inv_run_time*100/nvs_datas.accumulator.total_running_time;

	}
	uint16_t sendSize = app_bsm_send_data(		pBsmFP->Dev_Addr,	//设备地址
												pBsmFP->Frame_Fun,	//帧功能号
												sizeof(BsmFrame82), //数据大小
												0,                  //数据偏移量
												bsm_opt.ack_buff,   //数据源
												bsm_opt.tx_buff);	//发送缓冲区
}

/**
  * @brief F21.设置变频器
  */
void bsm_unpack_set_0x21(void *frame)
{	
	BsmFramePackage *pBsmFP 	= (BsmFramePackage*)frame;
    uint8_t  *pRecvStream   = (uint8_t *)pBsmFP->pData;
	uint8_t  *pSendStream 	= (uint8_t *)bsm_opt.ack_buff;
    
    if(rt_strlen(&pRecvStream[0]) <= 32)
        rt_strncpy(&nvs_datas.info.driverboard_barcode,  &pRecvStream[0],  32);
    
    if(rt_strlen(&pRecvStream[32]) <= 32)
        rt_strncpy(&nvs_datas.info.controlboard_barcode,  &pRecvStream[32],  32);
    
    if(rt_strlen(&pRecvStream[64]) <= 32)
        rt_strncpy(&nvs_datas.info.capacitanceboard_barcode,  &pRecvStream[64],  32);
    
    if(rt_strlen(&pRecvStream[96]) <= 32)
        rt_strncpy(&nvs_datas.info.inductanceboard_barcode,  &pRecvStream[96],  32);
    
    if(rt_strlen(&pRecvStream[128]) <= 32)
        rt_strncpy(&nvs_datas.info.product_barcode,  &pRecvStream[128],  32);
    
	nvsdata_write_info();
    
    // readback NVS data to ask
    nvsdata_read_info();
        
    rt_strncpy( &pSendStream[0],    &nvs_datas.info.driverboard_barcode,        32);
    rt_strncpy( &pSendStream[32],   &nvs_datas.info.controlboard_barcode,       32);
    rt_strncpy( &pSendStream[64],   &nvs_datas.info.capacitanceboard_barcode,   32);
    rt_strncpy( &pSendStream[96],   &nvs_datas.info.inductanceboard_barcode,    32);   
    rt_strncpy( &pSendStream[128],   &nvs_datas.info.product_barcode,           32);
    
    uint16_t sendSize = app_bsm_send_data(		pBsmFP->Dev_Addr,	//设备地址
												pBsmFP->Frame_Fun,	//帧功能号
												160, //数据大小
												0,                  //数据偏移量
												pSendStream,        //数据源
												bsm_opt.tx_buff);	//发送缓冲区
}

/**
  * @brief F22.获取变频器
  */
void bsm_unpack_get_0x22(void *frame)
{	
	BsmFramePackage *pBsmFP 	= (BsmFramePackage*)frame;
    uint8_t  *pRecvStream   = (uint8_t *)pBsmFP->pData;
	uint8_t  *pSendStream 	= (uint8_t *)bsm_opt.ack_buff;
    
     // readback NVS data to ask
    nvsdata_read_info();
        
    rt_strncpy( &pSendStream[0],    &nvs_datas.info.driverboard_barcode,        32);
    rt_strncpy( &pSendStream[32],   &nvs_datas.info.controlboard_barcode,       32);
    rt_strncpy( &pSendStream[64],   &nvs_datas.info.capacitanceboard_barcode,   32);
    rt_strncpy( &pSendStream[96],   &nvs_datas.info.inductanceboard_barcode,    32);   
    rt_strncpy( &pSendStream[128],  &nvs_datas.info.product_barcode,           32);
    
    uint16_t sendSize = app_bsm_send_data(		pBsmFP->Dev_Addr,	//设备地址
												pBsmFP->Frame_Fun,	//帧功能号
												160, //数据大小
												0,                  //数据偏移量
												pSendStream,        //数据源
												bsm_opt.tx_buff);	//发送缓冲区
}

/**
  * @brief F83.设置变频器
  */
void bsm_unpack_set_0x83(void *frame)
{	
	BsmFramePackage *pBsmFP 	= (BsmFramePackage*)frame;
    uint8_t  *pRecvStream   = (uint8_t *)pBsmFP->pData;
	uint8_t  *pSendStream 	= (uint8_t *)bsm_opt.ack_buff;
    
    
    rt_strncpy(&nvs_datas.config.serial_num,  &pRecvStream[0],  sizeof(nvs_datas.config.serial_num));
    rt_strncpy(&nvs_datas.config.bar_code,    &pRecvStream[16],  sizeof(nvs_datas.config.bar_code));
    rt_strncpy(&nvs_datas.config.SetWifi.local_ip,  &pRecvStream[48],  sizeof(nvs_datas.config.SetWifi.local_ip));
    rt_strncpy(&nvs_datas.config.SetWifi.remote_ip, &pRecvStream[64],  sizeof(nvs_datas.config.SetWifi.remote_ip));
    
    if(rt_strlen(&pRecvStream[96]) >= 8)
    {
        rt_strncpy(&nvs_datas.config.SetWifi.ssid,      &pRecvStream[80],  sizeof(nvs_datas.config.SetWifi.ssid));
        rt_strncpy(&nvs_datas.config.SetWifi.passwd,    &pRecvStream[96],  sizeof(nvs_datas.config.SetWifi.passwd));
    }
    
    vfd.nvs_write_config_flag = 1;
	WriteSetInfo();
    
    // readback NVS data to ask
    ReadSetInfo();
        
    rt_strncpy( &pSendStream[0],   &nvs_datas.config.serial_num,       sizeof(nvs_datas.config.serial_num));
    rt_strncpy( &pSendStream[16],  &nvs_datas.config.bar_code,         sizeof(nvs_datas.config.bar_code));
    rt_strncpy( &pSendStream[48],  &nvs_datas.config.SetWifi.local_ip, sizeof(nvs_datas.config.SetWifi.local_ip));
    rt_strncpy( &pSendStream[64],  &nvs_datas.config.SetWifi.remote_ip,sizeof(nvs_datas.config.SetWifi.remote_ip));
    rt_strncpy( &pSendStream[80],  &nvs_datas.config.SetWifi.ssid,     sizeof(nvs_datas.config.SetWifi.ssid));
    rt_strncpy( &pSendStream[96],  &nvs_datas.config.SetWifi.passwd,   sizeof(nvs_datas.config.SetWifi.passwd));
    
    uint16_t sendSize = app_bsm_send_data(		pBsmFP->Dev_Addr,	//设备地址
												pBsmFP->Frame_Fun,	//帧功能号
												112, //数据大小
												0,                  //数据偏移量
												pSendStream,        //数据源
												bsm_opt.tx_buff);	//发送缓冲区
}

/**
  * @brief F84.获取变频器
  */
void bsm_unpack_get_0x84(void *frame)
{	
	BsmFramePackage *pBsmFP 	= (BsmFramePackage*)frame;
    uint8_t  *pRecvStream   = (uint8_t *)pBsmFP->pData;
	uint8_t  *pSendStream 	= (uint8_t *)bsm_opt.ack_buff;
    
    // readback NVS data to ask
    ReadSetInfo();
        
    rt_strncpy( &pSendStream[0],   &nvs_datas.config.serial_num,       sizeof(nvs_datas.config.serial_num));
    rt_strncpy( &pSendStream[16],  &nvs_datas.config.bar_code,         sizeof(nvs_datas.config.bar_code));
    rt_strncpy( &pSendStream[48],  &nvs_datas.config.SetWifi.local_ip, sizeof(nvs_datas.config.SetWifi.local_ip));
    rt_strncpy( &pSendStream[64],  &nvs_datas.config.SetWifi.remote_ip,sizeof(nvs_datas.config.SetWifi.remote_ip));
    rt_strncpy( &pSendStream[80],  &nvs_datas.config.SetWifi.ssid,     sizeof(nvs_datas.config.SetWifi.ssid));
    rt_strncpy( &pSendStream[96],  &nvs_datas.config.SetWifi.passwd,   sizeof(nvs_datas.config.SetWifi.passwd));
    
    uint16_t sendSize = app_bsm_send_data(		pBsmFP->Dev_Addr,	//设备地址
												pBsmFP->Frame_Fun,	//帧功能号
												112, //数据大小
												0,                  //数据偏移量
												pSendStream,        //数据源
												bsm_opt.tx_buff);	//发送缓冲区
}

/**
  * @brief F87.获取逆变模块和PFC参数帧帧
  */
void bsm_unpack_get_invpfc_param(void *frame)
{
	if(NULL == frame)	return;
	BsmFramePackage *pBsmFP = (BsmFramePackage*)frame;
	BsmFrame82 *pSendStream = (BsmFrame82 *)bsm_opt.ack_buff;
	{
        vfd.temp = sizeof(BsmFrame82);
        
		ReadSetInfo();
		
		pSendStream->Ctrl_O1 = vfd.manual.o1_mask ? (vfd.manual.o1 ? 1 : 2) : 0;
        pSendStream->Ctrl_O2 = vfd.manual.o2_mask ? (vfd.manual.o2 ? 1 : 2) : 0;
        pSendStream->Ctrl_O3 = vfd.manual.o3_mask ? (vfd.manual.o3 ? 1 : 2) : 0;
        
        pSendStream->Ctrl_I1 = vfd.manual.in1_mask ? (vfd.manual.in1 ? 1 : 2) : 0;
        pSendStream->Ctrl_I2 = vfd.manual.in2_mask ? (vfd.manual.in2 ? 1 : 2) : 0;
        pSendStream->Ctrl_I3 = vfd.manual.in3_mask ? (vfd.manual.in3 ? 1 : 2) : 0;
        
        pSendStream->Ctrl_KMON1 = vfd.manual.kmon1_mask ? (vfd.manual.kmon1 ? 1 : 2) : 0;
        pSendStream->Ctrl_KMON2 = vfd.manual.kmon2_mask ? (vfd.manual.kmon2 ? 1 : 2) : 0;
        
        pSendStream->Ctrl_Inv_U_Duty = vfd.manual.inv_u_duty;
        pSendStream->Ctrl_Inv_V_Duty = vfd.manual.inv_v_duty;
        pSendStream->Ctrl_Inv_W_Duty = vfd.manual.inv_w_duty;
        pSendStream->Ctrl_PFC_U_Duty = vfd.manual.pfc_u_duty;
        pSendStream->Ctrl_PFC_V_Duty = vfd.manual.pfc_v_duty;
        pSendStream->Ctrl_PFC_W_Duty = vfd.manual.pfc_w_duty;
        
        pSendStream->clear_all_timer = 0;
        pSendStream->Ctrl_ClearAll = 0;
        
        pSendStream->kmon2_off_time         =  nvs_datas.accumulator.kmon2_off_time        ;
        pSendStream->kmon2_on_time          =  nvs_datas.accumulator.kmon2_on_time         ;
        pSendStream->pfc_startup_time       =  nvs_datas.accumulator.pfc_startup_time      ;
        pSendStream->sys_run_time           =  nvs_datas.accumulator.total_running_time    ;
        pSendStream->vfd_poweron_time       =  nvs_datas.accumulator.vfd_poweron_time      ;
        pSendStream->inv_run_time           =  nvs_datas.accumulator.inv_run_time          ;
        pSendStream->ac_power_consumption   =  nvs_datas.accumulator.power_consumption     ;
        pSendStream->dc_power_consumption   =  nvs_datas.accumulator.dc_power_consumption  ;
        
		rt_memcpy(&pSendStream->Inv_BusVol_Cal, 		&nvs_datas.accumulator.work_powerin_lvl1, 	 22*4);
        rt_memcpy(&pSendStream->statistics_data, 		&nvs_datas.accumulator.work_temp1_lvl7, 	 50*4);
        
		rt_memcpy(&pSendStream->Inv_Motor_Manufacturer, &vfd.SetMoInfo, 	 sizeof(vfd.SetMoInfo));
		rt_memcpy(&pSendStream->DriveBoardHardware, 	&vfd.SetHwVer,  	 sizeof(vfd.SetHwVer));
		rt_memcpy(&pSendStream->Can_X_Adrr, 			&vfd.SetCanAddr,	 sizeof(vfd.SetCanAddr));		
        pSendStream->Serial_Adrr = nvs_datas.config.serial_com_id;
        
        pSendStream->VF_Linear_StepUp   = nvs_datas.modbus.reg_3000[0x0001];
        pSendStream->VF_Linear_StepDown = nvs_datas.modbus.reg_3000[0x0002];
        pSendStream->VF_Linear_F1 = nvs_datas.modbus.reg_3000[0x0003];
        pSendStream->VF_Linear_V1 = nvs_datas.modbus.reg_3000[0x0004];
        pSendStream->VF_Linear_F2 = nvs_datas.modbus.reg_3000[0x0005];
        pSendStream->VF_Linear_V2 = nvs_datas.modbus.reg_3000[0x0006];
        pSendStream->VF_Linear_F3 = nvs_datas.modbus.reg_3000[0x0007];
        pSendStream->VF_Linear_V3 = nvs_datas.modbus.reg_3000[0x0008];
    
        pSendStream->SetInvOutputFreq        = nvs_datas.modbus.reg_3000[0x0009];
        pSendStream->SetEmergInvOutputFreq   = nvs_datas.modbus.reg_3000[0x000D];
        pSendStream->SetPfcWorkVolt     = nvs_datas.modbus.reg_3000[0x000A];
        pSendStream->SetInvOutputRpm    = nvs_datas.modbus.reg_3000[0x000B];
        pSendStream->SetDcdcWorkMinute  = nvs_datas.modbus.reg_3000[0x000C];

        pSendStream->adca_vin_r  = (int16_t)vfd.SetAdCa.PFC_R_InVol_Cal  ;
        pSendStream->adca_vin_s  = (int16_t)vfd.SetAdCa.PFC_S_InVol_Cal  ;
        pSendStream->adca_vin_t  = (int16_t)vfd.SetAdCa.PFC_T_InVol_Cal  ;
                                                                 
        pSendStream->adca_iin_r  = (int16_t)vfd.SetAdCa.PFC_R_InCur_Cal  ;
        pSendStream->adca_iin_s  = (int16_t)vfd.SetAdCa.PFC_S_InCur_Cal  ;
        pSendStream->adca_iin_t  = (int16_t)vfd.SetAdCa.PFC_T_ICur_Cal   ;
                                                                 
        pSendStream->adca_iout_u =  (int16_t)vfd.SetAdCa.Inv_U_OutCur_Cal;
        pSendStream->adca_iout_v =  (int16_t)vfd.SetAdCa.Inv_V_OutCur_Cal;
        pSendStream->adca_iout_w =  (int16_t)vfd.SetAdCa.Inv_W_OutCur_Cal;
                                                                 
        pSendStream->adca_vbus_inv = (int16_t)vfd.SetAdCa.Inv_BusVol_Cal ;
        pSendStream->adca_vbus_pfc = (int16_t)vfd.SetAdCa.PFC_BusVol_Cal ;
        
        pSendStream->rs485_baudrate  = nvs_datas.config.rs485_baudrate  ;
        pSendStream->rs485_check_bit = nvs_datas.config.rs485_check_bit ;
        pSendStream->can_baudrate    = nvs_datas.config.can_baudrate    ;
        pSendStream->ControlBoardHardware = vfd.ctrl_hw_ver;
        
        pSendStream->run_duty = nvs_datas.accumulator.inv_run_time*100/nvs_datas.accumulator.total_running_time;
        
        pSendStream->modbus_rx_cnter = vfd.modbus_rx_cnter;
        pSendStream->modbus_tx_cnter = vfd.modbus_tx_cnter;
        pSendStream->modbus_rx_add_err_cnter = vfd.modbus_rx_add_err_cnter;
        

	}
	
	uint16_t sendSize = app_bsm_send_data(		pBsmFP->Dev_Addr,	//设备地址
												pBsmFP->Frame_Fun, 	//帧功能号
												sizeof(BsmFrame82), 				//数据大小
												0,                    				//数据偏移量
												pSendStream,        				//数据源
												bsm_opt.tx_buff);	  				//发送缓冲区
}
/**
  * @brief 获取TOP图参数
  */
void bsm_unpack_get_top(void *frame)
{
	if(NULL == frame)	return;
	BsmFramePackage *pBsmFP = (BsmFramePackage*)frame;
	BsmFrame88 *pSendStream = (BsmFrame88 *)bsm_opt.ack_buff;
	{
		rt_memcpy(&pSendStream->date, &vfd.date, sizeof(pSendStream->date));
        
		pSendStream->Sys_State = vfd.ctrl.inv_st;
		
		pSendStream->In_Power  = 0;
		pSendStream->In_Freq   = (int16_t)vfd.acin_freq;
		pSendStream->In_Energy = 0;
		
		pSendStream->In_Vol_R = vfd.ad.ac_vin_u/10;
		pSendStream->In_Vol_S = vfd.ad.ac_vin_v/10;
		pSendStream->In_Vol_T = vfd.ad.ac_vin_w/10;
		
		pSendStream->In_Cur_R			= vfd.ad.ac_iin_u;
		pSendStream->In_Cur_S			= vfd.ad.ac_iin_v;
		pSendStream->In_Cur_T			= vfd.ad.ac_iin_w;
			
		pSendStream->Temp_L1			= vfd.ad.temp_pfc;
		pSendStream->Temp_L2			= vfd.ad.temp_pfc;
		
		pSendStream->Duty_Pfc			= EPST.BoostPwmDuty;
		pSendStream->Temp_Pfc			= vfd.ad.temp_cpu;
		
		pSendStream->Duty_Inv			= EPST.InvPwmDuty;
		pSendStream->Temp_IGBT			= EPAD.Temp_IGBT_AD;
		
		pSendStream->Duty_Fan			= vfd.fan_duty;

		pSendStream->Out_Freq			= EPST.InvWorkEreq;
		
		pSendStream->Out_Cur_U      = EPAD.UCurrent_AD;
		pSendStream->Out_Cur_V      = EPAD.VCurrent_AD;
		pSendStream->Out_Cur_W      = EPAD.WCurrent_AD;
		
		pSendStream->Out_Vol_U		= motor.Vout;
		pSendStream->Out_Vol_V		= motor.Vout;
		pSendStream->Out_Vol_W		= motor.Vout;
		
		pSendStream->Vol_Main		= EPAD.MainVoltage / 10;
		pSendStream->Vol_12V		= EPAD.DC_12V_AD / 10;
		pSendStream->Vol_5V		    = EPAD.DC_5V_AD / 10;
        
		pSendStream->Vol_Ai1		= EPAD.Sample_0_10V  / 10;
		pSendStream->Cur_Ai2		= EPAD.Sample_4_20mA / 10;
        
		pSendStream->Speed_Motor	= 0;
		
		pSendStream->Com_CAN		= vfd.bit.com_can;
		pSendStream->Com_485		= vfd.bit.com_485;

		pSendStream->Com_Wifi		= vfd.bit.com_wifi;				
		pSendStream->Com_Net		= 0;	
	}	
    
	uint16_t sendSize = app_bsm_send_data(		pBsmFP->Dev_Addr,	//设备地址
												pBsmFP->Frame_Fun, 	//帧功能号
												sizeof(BsmFrame88), 				//数据大小
												0,                    				//数据偏移量
												pSendStream,        				//数据源
												bsm_opt.tx_buff);	  				//发送缓冲区
}

#include <flashdb.h>
extern int log_counts;
extern int fault_counts;
extern struct fdb_tsdb tsdb_log;
extern struct fdb_tsdb tsdb_fault;
extern void fdb_tsl_iter_reverse(fdb_tsdb_t db, fdb_tsl_cb cb, void* arg) ;
extern void fdb_tsl_iter(fdb_tsdb_t db, fdb_tsl_cb cb, void* arg) ;
extern void fdb_tsl_iter_reverse_serial(fdb_tsdb_t db, fdb_tsl_cb cb, void *cb_arg,uint32_t cmd);
static bool tsl_query_cb(fdb_tsl_t tsl, void *arg);
static uint8_t *tsl_copy_buf = 0;


void record_com_db_type_reset(uint8_t db_type)
{
    if((db_type == 1) 
        || (db_type == 2))
    {
        struct fdb_tsdb *ptsdb = NULL;
        
        record_com.db_type = db_type;
    
        if(db_type == 1)
            record_com.ptsdb = &tsdb_fault;
        else if(db_type == 2)
            record_com.ptsdb = &tsdb_log;
        
        record_com.reset_flag = 1;
        record_com.acc_index = 0; 
        record_com.read_index = 0;   
        record_com.cache[0].tsl_time = 0;
        record_com.query_counts = 0;
        fdb_tsl_iter(record_com.ptsdb,tsl_query_cb,"get_end_tsl_time");
        record_com.query_counts = 0;
        fdb_tsl_iter_reverse(record_com.ptsdb,tsl_query_cb,"get_curr_time");
        record_com.counts = record_com.cur_tsl_time - record_com.end_tsl_time + 1;
    }
    
}

static void record_com_reset_acknowledge(uint8_t reset_flag)
{
    if(reset_flag)
    {
        record_com.reset_flag = 0;
        record_com.ctrl.clear_read = 1;
        record_com.acc_index = 0;
    }
}

uint32_t record_get_timestamp(uint8_t *data)
{
    uint32_t timestamp = 0;
    
    struct tm tm_new = {0};
    
    if((data[0] < 22) || (data[1] == 0) || (data[2] == 0))
    {
        return 0;
    }
    else
    {
        /* update date. */
        tm_new.tm_year = data[0]+2000 - 1900;
        tm_new.tm_mon  = data[1] - 1; /* tm_mon: 0~11 */
        tm_new.tm_mday = data[2];
    
        /* update time. */
        tm_new.tm_hour = data[3];
        tm_new.tm_min  = data[4];
        tm_new.tm_sec  = data[5];
    
        /* converts the local time into the calendar time. */
        timestamp = mktime(&tm_new);
    }
    
    return timestamp;
}

static bool tsl_query_cb(fdb_tsl_t tsl, void *arg)
{
    static char *log = NULL;
    struct fdb_blob blob;
    size_t read_len;
    record_log_t *tsldata = NULL;
    uint32_t timestamp = 0;
    
    record_com.query_counts++;
    
    if(log == NULL)
        log = rt_malloc(20*128);
    
    if(log)
    {
        fdb_blob_make(&blob, log, tsl->log_len);
        read_len = fdb_blob_read((fdb_db_t)record_com.ptsdb, fdb_tsl_to_blob(tsl, &blob));
        
        tsldata = ((uint8_t*)(blob.buf)); 
        timestamp = record_get_timestamp(&tsldata->head.date);
    }
            
    if(!rt_strcmp(arg,"get_curr_time"))
    {
        uint32_t timestamp = record_get_timestamp(&tsldata->head.date);
                    
        if(record_com.query_counts == 1)
            record_com.cur_tsl_time = tsl->time;
        
        if(log)
        {
            if(timestamp == 0)
            {
                /* unknown time ,donothing*/
                return false;
            }
            else
            {
                record_com.cache[0].tsl_time = tsl->time;
                rt_memcpy(record_com.cur_tsl_date,&(tsldata->head.date),6);
                rt_memcpy(&record_com.cache[0].tsl_log ,tsldata,sizeof(record_log_t));
                return true;
            }
        }
            
        return false;
    }
    else if(!rt_strcmp(arg,"get_end_tsl_time"))
    {
        if(record_com.query_counts == 1)
            record_com.end_tsl_time = tsl->time;
        
        if(log)
        {         
            if(timestamp == 0)
            {
                /* unknown time ,donothing*/
                return false;
            }
            else
            {
                rt_memcpy(record_com.end_tsl_date,&(tsldata->head.date),6);
                return true;
            }
        }

        return false;
    }
    else if(!rt_strcmp(arg,"read_one_tsl"))
    {  
        //
        #if 1
        if(record_com.query_counts % 1000 == 0)
        {
            if(tsl->time > record_com.cur_tsl_time)
            {
                record_com.cur_tsl_time = tsl->time;
                record_com.counts = record_com.cur_tsl_time - record_com.end_tsl_time;
            }
                
            *(uint32_t *)&(bsm_opt.ack_buff[0]) = (record_com.cur_tsl_time - tsl->time)*100/record_com.counts;
            
            BSM_COM_PRINT("tick%d :query %d %%,read %d,acc %d,wait \r\n",rt_tick_get()/2,*(uint32_t *)&(bsm_opt.ack_buff[0])
                                            ,record_com.read_index,record_com.acc_index);
            
            uint16_t sendSize = app_bsm_send_data(	DEV_ADDR,	//设备地址
												0x09, 	        //帧功能号
												4, 			    //数据大小
												0,                    				//数据偏移量
												bsm_opt.ack_buff,        				//数据源
												bsm_opt.tx_buff);	  				//发送缓冲区
            rt_thread_mdelay(100);
            
        }
        #endif
        
        if(tsl->status != FDB_TSL_WRITE)
            return false;
        if(tsl->time == 0)
            return false;
        else if(log)
        {  
            uint8_t cache_tsl_offset = read_len/128;
            
            {
                if(record_com.cache[0].tsl_time == 0)
                {
                    record_com.cache[0].tsl_time = tsl->time;
                    rt_memcpy(&record_com.cache[0].tsl_log ,tsldata,sizeof(record_log_t));
                }    
                
                if(record_com.ctrl.filter_time)
                {
                    uint32_t timestamp = record_get_timestamp(&tsldata->head.date);
                    
                    if(timestamp == 0)
                    {
                        /* unknown time ,donothing*/
                    }
                    else if(record_com.ctrl.from > record_com.ctrl.to)
                    {
                        if(timestamp > record_com.ctrl.from)
                            return false; 
                        if(timestamp < record_com.ctrl.to)
                            return false; 
                        if(record_com.ctrl.from < record_get_timestamp(&record_com.end_tsl_date))
                            return true; 
                    }
                    else
                    {
                        if(timestamp > record_com.ctrl.to)
                            return false;
                        if(timestamp < record_com.ctrl.from)
                            return false; 
                        if(record_com.ctrl.to < record_get_timestamp(&record_com.end_tsl_date))
                            return true; 
                    }
                    
                }
                     

                if(record_com.ctrl.filter_event)
                {        
                    if((record_com.ctrl.filter_event_code == 0xFF)
                        && (tsldata->head.event_code == LOG_Idle))
                        return false;
                    else if((record_com.ctrl.filter_event_code != 0xFF)
                        && (tsldata->head.event_code != record_com.ctrl.filter_event_code))
                        return false; 
                } 
                
                if(record_com.ctrl.filter_diag)
                {       
                    if(tsldata->head.diag_code != record_com.ctrl.filter_diag_code)
                        return false; 
                }
              
                record_com.acc_index++;
            }
//            else
//            {
//                return false;
//            }
            
            uint8_t *data = ((uint8_t*)(blob.buf));
            uint32_t valid_index = record_com.acc_index; 
            
            if(valid_index == record_com.read_index)
            {
                cache_tsl_offset = (cache_tsl_offset > RECORD_cache_tsl_offset) ? RECORD_cache_tsl_offset : cache_tsl_offset;
                
                for(int i = 0 ;i < cache_tsl_offset;i++)
                {
                    rt_memcpy(&record_com.cache[i].tsl_log ,&data[i*128],sizeof(record_log_t));
                    record_com.cache[i].tsl_time = tsl->time;
                }
                
                record_com.cache_tsl_offset = cache_tsl_offset;
                        
                if(tsl_copy_buf)
                {
                    if((record_com.db_type == 0x01) //fault
                     && (record_com.read_offset >=0) 
                     && (record_com.read_offset < cache_tsl_offset))
                        rt_memcpy(tsl_copy_buf,&record_com.cache[record_com.read_offset],sizeof(record_log_t)+4);
                    else
                        rt_memcpy(tsl_copy_buf,&record_com.cache[0],sizeof(record_log_t)+4);
                }
                
                record_com.query_haved_flag = 1;
                record_com.cache_time = tsl->time;
                return true;
            }
            
        }
        
    }
    return false;
}


/**
  * @brief 
  */
void bsm_unpack_0x07(void *frame)
{
	if(NULL == frame)	return;
	BsmFramePackage *pBsmFP = (BsmFramePackage*)frame;
    uint8_t  *pSendStream 	= (uint8_t *)bsm_opt.ack_buff;
    uint32_t tick = rt_tick_get();
    
    if(!vfd.bit.nvs_datas_init)
        return;
    
    if((pBsmFP->pData[0] == 0x1)    // fault db
       || (pBsmFP->pData[0] == 0x2) // log db
    )
    {
        record_com_db_type_reset(pBsmFP->pData[0]);

        pSendStream[0] = pBsmFP->pData[0];
         
        *(uint32_t*)&pSendStream[4] = record_com.counts;  
        *(uint32_t*)&pSendStream[8] = 128;  
        
        if(pBsmFP->pData[0] == 0x1)
        {
            *(uint16_t*)&pSendStream[2] = 20*128; 
            *(uint16_t*)&pSendStream[10] = 20;  
        }
        else if(pBsmFP->pData[0] == 0x2) 
        {
            *(uint16_t*)&pSendStream[2] = 1*128; 
            *(uint16_t*)&pSendStream[10] = 1;  
        }
        
        *(uint32_t*)&pSendStream[12] = record_com.cur_tsl_time;  
        *(uint32_t*)&pSendStream[16] = record_com.end_tsl_time; 
        rt_memcpy(&pSendStream[20],record_com.cur_tsl_date,6); 
        rt_memcpy(&pSendStream[26],record_com.end_tsl_date,6); 
    }

    BSM_COM_PRINT(" 0x07 use tick:%d \r\n",rt_tick_get() - tick);
    BSM_COM_PRINT(" tsdb.cur_tsl_time:%d \r\n",record_com.cur_tsl_time);
    BSM_COM_PRINT(" tsdb.end_tsl_time:%d \r\n",record_com.end_tsl_time);
    BSM_COM_PRINT(" tsdb.total_counts:%d \r\n",record_com.counts);
	
	uint16_t sendSize = app_bsm_send_data(		pBsmFP->Dev_Addr,	//设备地址
												pBsmFP->Frame_Fun, 	//帧功能号
												32, 				//数据大小
												0,                    				//数据偏移量
												bsm_opt.ack_buff,        				//数据源
												bsm_opt.tx_buff);	  				//发送缓冲区
}

/**
  * @brief 
  */
void bsm_unpack_0x08(void *frame)
{
	if(NULL == frame)	return;
	BsmFramePackage *pBsmFP = (BsmFramePackage*)frame;
    uint8_t  *pSendStream 	= (uint8_t *)bsm_opt.ack_buff;
    int ack_size = 0;
    static uint32_t period_tick = 0;
    uint32_t period = 0;
    
    period =  rt_tick_get() - period_tick;
    period_tick = rt_tick_get();
    
    if(!vfd.bit.nvs_datas_init)
        return;
  
    if((record_com.cur_tsl_time == 0) ||
        (record_com.counts == 0)  ||
        (pBsmFP->pData[0] != record_com.db_type))
        record_com_db_type_reset(pBsmFP->pData[0]);
    
    if(pBsmFP->Data_Lenth >= 10)
    {
        *(uint8_t *)&record_com.ctrl = pBsmFP->pData[1];
        record_com.ctrl.clear_read = 0;
        
        record_com.ctrl.filter_event_code = pBsmFP->pData[8];
        record_com.ctrl.filter_diag_code  = pBsmFP->pData[9];
        
        if(pBsmFP->Data_Lenth >= 18)
        {
            #if 1
            record_com.ctrl.from = *(uint32_t *)&pBsmFP->pData[10];
            record_com.ctrl.to = *(uint32_t *)&pBsmFP->pData[14];
            #else
            record_com.ctrl.filter_time = 1;
            record_com.ctrl.from = 1667040950;
            record_com.ctrl.to = 1667041370;//record_com.ctrl.from - 600;
            #endif
        }
    }
    
    if( ((pBsmFP->pData[0] == 0x1)||(pBsmFP->pData[0] == 0x2))
        && (record_com.cur_tsl_time != 0))
    {
        uint32_t tick = rt_tick_get();
        tsl_copy_buf = pSendStream;
        
        record_com.read_index = *(int*)&pBsmFP->pData[4];
        record_com.read_offset = *(uint16_t*)&pBsmFP->pData[2];
        
        record_com.query_haved_flag = 0;
        
        record_com_reset_acknowledge(record_com.reset_flag);
        
        record_com.ctrl.serial_read = 1;
        
        if((record_com.read_index == record_com.acc_index)
            && (record_com.read_offset < record_com.cache_tsl_offset))
        {
            record_com.query_haved_flag = 1;
            
            if(pBsmFP->pData[0] == 0x2)
                rt_memcpy(tsl_copy_buf,&record_com.cache[0],132);
            else
                rt_memcpy(tsl_copy_buf,&record_com.cache[record_com.read_offset],132);
        }    
        else
        {    
            record_com.query_counts = 0;
            
            if(record_com.ctrl.serial_read)
            {
                fdb_tsl_iter_reverse_serial(record_com.ptsdb,tsl_query_cb,"read_one_tsl",record_com.ctrl.clear_read);
            }
            else
            {
                record_com.acc_index = 0;
                fdb_tsl_iter_reverse(record_com.ptsdb,tsl_query_cb,"read_one_tsl");
            }
        }
        if(record_com.ctrl.serial_read)
            BSM_COM_PRINT("[S]");
        if(record_com.ctrl.clear_read)
            BSM_COM_PRINT("[C]");
        if(record_com.ctrl.filter_diag)
            BSM_COM_PRINT("[D%02d]",record_com.ctrl.filter_diag_code);
        
        if(record_com.ctrl.filter_event)
            BSM_COM_PRINT("[E%02d]",record_com.ctrl.filter_event_code);
        
        if(record_com.query_haved_flag)
        {
            ack_size = 132;
            BSM_COM_PRINT("[%d]0x08 usetick[%d],",period,rt_tick_get() - tick);
           
            BSM_COM_PRINT("rd_index[%d],",record_com.read_index);
            BSM_COM_PRINT("rd_offset[%d],",record_com.read_offset);
            
            BSM_COM_PRINT("tslid[%d],%4d-%2d-%2d %02d:%02d:%02d ->",*(uint32_t*)&tsl_copy_buf[0],tsl_copy_buf[4]+2000,tsl_copy_buf[5],tsl_copy_buf[6],
                                                    tsl_copy_buf[7],tsl_copy_buf[8],tsl_copy_buf[9]);
            
            BSM_COM_PRINT("event[%d],diag[%d]  \r\n",tsl_copy_buf[11],tsl_copy_buf[12]);
        }
        else
        {
            BSM_COM_PRINT("[%d]0x08 usetick[%d] ",period,rt_tick_get() - tick);
            BSM_COM_PRINT("rd_index[%d] not find \r\n",record_com.read_index);
            ack_size = 0;
        }
    }
    
    
	uint16_t sendSize = app_bsm_send_data(		pBsmFP->Dev_Addr,	//设备地址
												pBsmFP->Frame_Fun, 	//帧功能号
												ack_size, 			//数据大小
												0,                    				//数据偏移量
												bsm_opt.ack_buff,        				//数据源
												bsm_opt.tx_buff);	  				//发送缓冲区
}

/**
  * @brief 
  */
void bsm_unpack_0x09(void *frame)
{
	if(NULL == frame)	return;
	BsmFramePackage *pBsmFP = (BsmFramePackage*)frame;
    uint8_t  *pSendStream 	= (uint8_t *)bsm_opt.ack_buff;
    int ack_size = 0;

    
    
	uint16_t sendSize = app_bsm_send_data(		pBsmFP->Dev_Addr,	//设备地址
												pBsmFP->Frame_Fun, 	//帧功能号
												ack_size, 			//数据大小
												0,                    				//数据偏移量
												bsm_opt.ack_buff,        				//数据源
												bsm_opt.tx_buff);	  				//发送缓冲区
}


extern int nvs_erase_allchip(void);
/**
  * @brief 
  */
void bsm_unpack_0x17(void *frame)
{
	if(NULL == frame)	return;
	BsmFramePackage *pBsmFP = (BsmFramePackage*)frame;
    uint8_t  *pSendStream 	= (uint8_t *)bsm_opt.ack_buff;
    uint32_t tick = rt_tick_get();
    vfd.ctrl.erase_flash = 1;
    
    while(vfd.ctrl.erase_flash == 1)
    {
        vfd.bit.soft_stop = 1;
        
        rt_thread_delay(RT_TICK_PER_SECOND/2);
        *(uint32_t *)&pSendStream[0] = (rt_tick_get() - tick)/2;
        
        BSM_COM_PRINT("0x17 usetick[%d] \r\n",*(uint32_t *)&pSendStream[0]);
        
        uint16_t sendSize = app_bsm_send_data(	pBsmFP->Dev_Addr,	//设备地址
												pBsmFP->Frame_Fun, 	//帧功能号
												4, 				    //数据大小
												0,                    				//数据偏移量
												bsm_opt.ack_buff,        				//数据源
												bsm_opt.tx_buff);	  				//发送缓冲区
        
        
    }
    
    rt_thread_delay(RT_TICK_PER_SECOND/2);
    vfd.bit.soft_stop = 0;
    
    *(uint32_t *)&pSendStream[0] = 0x0;
        
    uint16_t sendSize = app_bsm_send_data(	pBsmFP->Dev_Addr,	//设备地址
												pBsmFP->Frame_Fun, 	//帧功能号
												4, 				    //数据大小
												0,                    				//数据偏移量
												bsm_opt.ack_buff,        				//数据源
												bsm_opt.tx_buff);	  				//发送缓冲区
}

/**
  * @brief app_bsm_com_process 
  */
void app_bsm_com_process(void *p)
{		
	rt_sem_init(&bsm_sem_rev, "bsm_sem_rev", 0, RT_IPC_FLAG_FIFO);
	
	BsmFramePackage *pBsmFP = (BsmFramePackage *) rt_malloc(sizeof(BsmFramePackage));
	
	for(;;)
	{	
		rt_sem_take(&bsm_sem_rev, RT_WAITING_FOREVER); 
        bsm_sem_rev.value = 0;
		rt_memcpy(pBsmFP, &bsm_opt.rx_buff[0] ,9);	
		pBsmFP->pData = &(bsm_opt.rx_buff[9]);	
		
        com_cnter_recv(&com_ptu);
        
		switch(pBsmFP->Frame_Fun)
		{			
			case UNPACK_GET_VER_F01:  
				bsm_unpack_get_version(pBsmFP);		 //F01. 获取版本帧
				break;								        
			case UNPACK_GET_DATE_F02:                       
				bsm_unpack_get_date(pBsmFP);		 //F02. 获取时间帧		
				break;				                        
			case UNPACK_GET_DIGITAL_INPUT_F03:              
				bsm_unpack_get_digital_input(pBsmFP);//F03. 获取输入数字量帧
				break;					                    
			case UNPACK_GET_DIGITAL_OUT_F04:                
				bsm_unpack_get_digital_out(pBsmFP);	 //F04. 获取输出数字量帧
				break;				                        
			case UNPACK_GET_AD_VALUE_F05:                   
				bsm_unpack_get_adc_value(pBsmFP);	 //F05. 获取AD模拟量帧			
				break;		
            case UNPACK_RESET_MCU_F06:
                bsm_unpack_reset_mcu(pBsmFP);
                break;
            case UNPACK_GET_TOTAL_FAULT_F07:  
				bsm_unpack_0x07(pBsmFP);	         //F07.		
				break;
            case UNPACK_GET_CHOOSE_NUM_F08:  
				bsm_unpack_0x08(pBsmFP);	         //F08.			
				break;
            case UNPACK_GET_CHOOSE_TIME_F09:  
				bsm_unpack_0x09(pBsmFP);		
				break;
							
			case UNPACK_FW_INFO_F0A:   
				bsm_unpack_fw_info(pBsmFP);			//F0A.固件信息
				break;					
			case UNPACK_FW_UPDATE_F0B:
                rxcnter.funcid_0x0A++;
				bsm_unpack_fw_update(pBsmFP);		//F0B.固件数据帧
				break;		
            case UNPACK_SET_RTC_F15:
                bsm_unpack_set_rtc(pBsmFP);		    //F15.设置时间
				break;
            case UNPACK_SET_BAUD_F16:
                bsm_unpack_set_baud(pBsmFP);		//F16.设置baud
				break;
            case UNPACK_ERASE_F17:
                bsm_unpack_0x17(pBsmFP);		    //F17. erase
				break;
            case UNPACK_SET_BAR_CODE_F21:
                bsm_unpack_set_0x21(pBsmFP);		//F21.
				break;
            case UNPACK_GET_BAR_CODE_F22:
                bsm_unpack_get_0x22(pBsmFP);		//F22.
                break;
			case UNPACK_GET_INV_BOOST_STATE_F81:
                rxcnter.funcid_0x81++;
                if(rt_tick_get() >= RT_TICK_PER_SECOND*2)
                    bsm_unpack_get_state(pBsmFP);		//F81.获取逆变和升压参数
				break;	
			case UNPACK_SET_INV_BOOST_PARAM_F82:	
                rxcnter.funcid_0x82++;
				bsm_unpack_set_invpfc_param(pBsmFP);//F82.设置逆变和升压参数
				break;	
            case UNPACK_SET_NVS_CONFIG_F83:	
                rxcnter.funcid_0x83++;
				bsm_unpack_set_0x83(pBsmFP);		//F83.
				break;
            case UNPACK_GET_NVS_CONFIG_F84:	
                rxcnter.funcid_0x84++;
				bsm_unpack_get_0x84(pBsmFP);		//F84.
				break;
			case UNPACK_GET_INV_BOOST_PARAM_F87:	
                rxcnter.funcid_0x87++;
				bsm_unpack_get_invpfc_param(pBsmFP);//F87.获取逆变和升压参数
				break;	
			case UNPACK_GET_TOP_F88:	
                rxcnter.funcid_0x88++;
				bsm_unpack_get_top(pBsmFP);			//F88.获取TOP图状态信息
				break;				
			default:
				break;
		}
		
	}
	rt_free(pBsmFP);
}

/**
  * @brief create bsm com task 
  */
int app_bsm_com_thread_create(void)
{
    rt_thread_t tid;
 
    //! startup a thread
    tid = rt_thread_create("bsm_com", app_bsm_com_process, RT_NULL, 4096, 13, 20);
    if (tid != RT_NULL)
        rt_thread_startup(tid);

    return 0;
}
INIT_APP_EXPORT(app_bsm_com_thread_create);


void cmd_bsm_com(int argc, char **argv)
{
    if(argc >= 3)
    {
        if(!rt_strcmp("info",argv[1]))
        {
            if((!rt_strcmp("log",argv[2]))
                ||(!rt_strcmp("fault",argv[2])))
            {
                uint8_t type = 0x1;
                
                if(!rt_strcmp("log",argv[2]))
                    type = 0x2;
                else if(!rt_strcmp("fault",argv[2]))
                    type = 0x1;
                
                BsmFramePackage *pBsmFP = &bsm_opt.rx_buff[0];
                uint8_t *pdata = &(bsm_opt.rx_buff[9]);
                pBsmFP->Frame_Fun = 0x7;
                pBsmFP->Data_Lenth = 2;
                pdata[0] = type;
				
                rt_sem_release(&bsm_sem_rev);
            }
        }
        else if((!rt_strcmp("read",argv[1])) ||
            (!rt_strcmp("serial_read",argv[1])) ||
            (!rt_strcmp("clear_read",argv[1])))
        {
            uint8_t type = 0x1;
            uint32_t start = 1;
            uint32_t num = 1;
            uint8_t  offset_num = 1;
            
            if(!rt_strcmp("log",argv[2]))
                    type = 0x2;
                else if(!rt_strcmp("fault",argv[2]))
                    type = 0x1;
            
            if(argc >= 4)
                start = atoi(argv[3]);
            
            if(argc >= 5)
                num   = atoi(argv[4]);    
            
            if(argc >= 6)
                offset_num = atoi(argv[5]);
            offset_num = (offset_num > 20) ? 20 : offset_num;
            
            for(int i =0;i < num;i++)
            {
                BsmFramePackage *pBsmFP = &bsm_opt.rx_buff[0];
                uint8_t *pdata = &(bsm_opt.rx_buff[9]);
                pBsmFP->Frame_Fun = 0x8;
                pBsmFP->Data_Lenth = 18;
                pdata[0] = type;
                
                if(!rt_strcmp("read",argv[1]))
                    pdata[1] = 0;
                else if(!rt_strcmp("serial_read",argv[1]))
                    pdata[1] = 0x02;
                else if(!rt_strcmp("clear_read",argv[1]))
                    pdata[1] = 0x01;
                
                if(i == 0)
                    pdata[1] |= 0x01;
                
                for(int j =0;j < offset_num;j++)
                {
                    *(uint16_t *)&pdata[2] = j;
                    *(uint32_t *)&pdata[4] = start;
                
                    rt_sem_release(&bsm_sem_rev);
                
                    rt_thread_mdelay(100);
                }
                
                start++;
            }
        }
        else if( (!rt_strcmp("event_read",argv[1]))
            || (!rt_strcmp("diag_read",argv[1])))
        {
            uint8_t type = 0x1;
            uint32_t start = 1;
            uint8_t filter_code = 0xFF;
            uint32_t num = 1;
            
            if(!rt_strcmp("log",argv[2]))
                    type = 0x2;
                else if(!rt_strcmp("fault",argv[2]))
                    type = 0x1;
            
            if(argc >= 4)
            {
                filter_code = atoi(argv[3]);
            }
            
            if(argc >= 5)
                num   = atoi(argv[4]);    
            
            for(int i =0;i < num;i++)
            {
                BsmFramePackage *pBsmFP = &bsm_opt.rx_buff[0];
                uint8_t *pdata = &(bsm_opt.rx_buff[9]);
                pBsmFP->Frame_Fun = 0x8;
                pBsmFP->Data_Lenth = 18;
                pdata[0] = type;
                
                if(!rt_strcmp("event_read",argv[1]))
                    pdata[1] = 0x06;
                else if(!rt_strcmp("diag_read",argv[1]))
                    pdata[1] = 0x0A;
 
                if(i == 0)
                    pdata[1] |= 0x01;
                
                *(uint16_t *)&pdata[2] = 0;
                *(uint32_t *)&pdata[4] = start++;
                
                pdata[8] = filter_code;
                pdata[9] = filter_code;
                
                rt_sem_release(&bsm_sem_rev);
                
                rt_thread_mdelay(100);
            }
        }
    }
}
MSH_CMD_EXPORT_ALIAS(cmd_bsm_com,bsm_com, cmd_bsm_com set);

