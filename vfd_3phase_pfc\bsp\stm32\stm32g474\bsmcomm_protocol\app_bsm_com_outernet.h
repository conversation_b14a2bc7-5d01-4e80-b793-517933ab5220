#ifndef __APP_BSM_COM_PROTOCOL_H__
#define __APP_BSM_COM_PROTOCOL_H__
#include <rtthread.h>
#include "stdint.h"
#include "string.h"
#include "api_bsm_com_protocol.h"


#ifdef __cplusplus
extern "C" {
#endif


//宏定义
#define BSM_DATA_RX_LENTH  512+100  //缓存区长度
#define BSM_DATA_TX_LENTH  512      //缓存区长度
//功能帧定义
#define UNPACK_GET_VER_F01     					0x01      //获取版本号
#define UNPACK_GET_DATE_F02    					0x02      //获取RTC日期
#define UNPACK_GET_DIGITAL_INPUT_F03    		0x03      //获取输入数字量
#define UNPACK_GET_DIGITAL_OUT_F04    			0x04      //获取输出数字量
#define UNPACK_GET_AD_VALUE_F05   	   			0x05      //获取模拟量

#define UNPACK_RESET_MCU_F06   	   			    0x06      //
#define UNPACK_GET_TOTAL_FAULT_F07      		0x07      //故障总条目读取
#define UNPACK_GET_CHOOSE_NUM_F08   			0x08      //指定条目故障数据读取
#define UNPACK_GET_CHOOSE_TIME_F09   			0x09      //指定时间故障数据读取
#define UNPACK_FW_INFO_F0A     					0x0A      //升级固件信息

#define UNPACK_FW_UPDATE_F0B   					0x0B      //升级固件帧数据包
#define UNPACK_GET_Inv_STATE_F10  				0x10      //获取变频器状态信息
#define UNPACK_SET_Inv_PARAM_F11   				0x11      //设置变频器参数
#define UNPACK_SET_AD_CALIBRATION_PARAM_F12   	0x12      //设置AD校准参数
#define UNPACK_SET_OUT_STATE_F13   			  	0x13      //设置输出状态

#define UNPACK_GET_PARAM_MODIFICATION_LOG_F14   0x14      //获取参数修改记录
#define UNPACK_SET_RTC_F15   					0x15      //设置RTC时间
#define UNPACK_SET_BAUD_F16   					0x16      //设置波特率
#define UNPACK_ERASE_F17   					    0x17     
#define UNPACK_SET_BAR_CODE_F21   				0x21      //设置条形码
#define UNPACK_GET_BAR_CODE_F22   				0x22      //获取条形码
#define UNPACK_SET_CAN_ADDR_F23   				0x23      //设置CAN通讯地址
#define UNPACK_SET_HARDWARE_VER_F24   			0x24      //设置硬件版本号

#define UNPACK_GET_INV_BOOST_STATE_F81     		0x81      //获取逆变和升压  变频器状态信息
#define UNPACK_SET_INV_BOOST_PARAM_F82     		0x82      //设置逆变和升压  变频器参数
#define UNPACK_SET_NVS_CONFIG_F83        		0x83      //获取逆变和升压  
#define UNPACK_GET_NVS_CONFIG_F84        		0x84      //设置逆变和升压  
#define UNPACK_GET_INV_BOOST_MODIF_PARAM_LOG_F85        0x85      //获取逆变和升压  参数修改记录
#define UNPACK_GET_INV_BOOST_FAULT_LOG_F86 		0x86       //获取逆变和升压  故障信息
#define UNPACK_GET_INV_BOOST_PARAM_F87 			0x87       //获取逆变和升压  变频器参数
#define UNPACK_GET_TOP_F88						0x88       //获取TOP图状态数据
#define DEV_ADDR        (vfd.serial_addr)      //当前设备地址 0x01+S1+
#define PFC_DEV_ADDR    0x12 //当前设备地址
#define BIN_FILE_NAME "/arm.bin"

#define TEST_BSM (0)

//固件升级状态
#define FW_STAT_IDE   0x0
#define FW_FILE_DATA  0x1
#define FW_FILE_END   0x2


#define FW_Inverter  	0x01   //逆变固件升级
#define FW_Boost  	    0x02   //升压固件升级


enum bsm_state
{
	BSM_Error = 0,
	BSM_OK = 1
};

enum bsm_com_port_dev
{
	dev_485 	= 1,		
	dev_uart3 	= 2,
	dev_esp 	= 3,
	dev_can 	= 4,
    dev_console = 5,
};
#pragma pack(1)  // 内存按照一个字节对齐

// - 1.逆变故障
typedef struct
{
/* 0Byte */
	uint8_t Inv_FAN1_FLT			: 1 ;	// 逆变 散热风扇1故障
	uint8_t Inv_FAN2_FLT			: 1 ;	// 逆变 散热风扇1故障
	uint8_t Inv_3Phase_N_FLT   	: 1 ;	// 逆变 三相输出电流负半波硬件保护
	uint8_t Inv_3Phase_O_FLT		: 1 ;	// 逆变 三相输出电流正半波硬件保护
	
	uint8_t Inv_IGBT_FLT			: 1 ; 	// 逆变 IGBT短路保护状态(1:正常 0:故障			
	uint8_t Inv_FS_INV_FLT			: 1 ; 	// 逆变 检测逆变FS输出状态 (1:正常0:停机	
	uint8_t Inv_OUT1_FLT			: 1 ; 	// 逆变 输出1			
	uint8_t Inv_OUT2_FLT			: 1 ; 	// 逆变 输出2	
	
/* 1Byte */
	uint8_t Inv_OUT3_FLT			: 1 ; 	// 逆变 输出3
	uint8_t Inv_IPM_FLT			: 1 ; 	// 逆变电机驱动模块故障状态 (1:正常0:停机
	uint8_t Inv_ExternFlash_FLT    : 1 ;   // 存储故障
	uint8_t Inv_RTC_FLT			: 1 ;   // RTC故障	
	
	uint8_t Inv_IntranetCOM_FLT    : 1 ;   // 内网通信故障
	uint8_t Inv_485COM_FLT			: 1 ;   // 外网485通讯故障
	uint8_t Inv_CANCOM_FLT    		: 1 ;   // 外网CAN通信故障
	uint8_t Res0    					: 1 ;   // 外网CAN通信故障
/* 2Byte */
	uint8_t Inv_BusUnderVol_FLT	: 1 ; 	// 逆变 母线电压故障
	uint8_t Inv_BusOverVol1_FLT	: 1 ; 	// 逆变 母线1级过压故障
	uint8_t Inv_BusOverVol2_FLT    : 1 ;   // 逆变	母线2级过压故障
	uint8_t Inv_BusOverVol3_FLT	: 1 ;   // 逆变	母线3级过压故障	
	
	uint8_t Inv_U_OverCur1_FLT    : 1 ;   // U相1级过流故障
	uint8_t Inv_U_OverCur2_FLT    : 1 ;   // U相2级过流故障	
	uint8_t Inv_U_OverCur3_FLT    : 1 ;   // U相3级过流故障	
	uint8_t Inv_V_OverCur1_FLT    : 1 ;   // V相1级过流故障
/* 3Byte */	
	uint8_t Inv_V_OverCur2_FLT    : 1 ;   // V相2级过流故障	
	uint8_t Inv_V_OverCur3_FLT    : 1 ;   // V相3级过流故障	
	uint8_t Inv_W_OverCur1_FLT    : 1 ;   // W相1级过流故障
	uint8_t Inv_W_OverCur2_FLT    : 1 ;   // W相2级过流故障
	
	uint8_t Inv_W_OverCur3_FLT    : 1 ;   // W相3级过流故障
	uint8_t Inv_12VPowerOverVol1_FLT  : 1 ;   // 12V主电源过压故障
	uint8_t Inv_12VPowerUnderVol_FLT  : 1 ;   // 12V主电源欠压故障
	uint8_t Res1    					   : 1 ;   // 保留
	
/* 4Byte */	
	uint8_t Inv_CPUOverTemp_FLT    	: 1 ;   // 逆变控制室过温故障（CPU温度）
	uint8_t Res2    						: 1 ;   // 保留
	uint8_t Inv_BusVolSensor_FLT    	: 1 ;   // 逆变母线电压传感器故障
	uint8_t Inv_U_CurSensor_FLT    : 1 ;   // 逆变U相输出交流电流采样传感器故障
	
	uint8_t Inv_V_CurSensor_FLT    : 1 ;   // 逆变V相输出交流电流采样传感器故障
	uint8_t Inv_W_CurSensor_FLT  	: 1 ;   // 逆变W相输出交流电流采样传感器故障
	uint8_t Inv_IGBT_Sensor_FLT  		: 1 ;   // 逆变IGBT温度传感器故障
	uint8_t DriveBoardRefeVol_FLT  	        : 1 ;   // 驱动主板参考源故障
/* 5Byte */
	uint8_t Inv_U_Loss_FLT  	        : 1 ;   //逆变U相输出缺相故障
	uint8_t Inv_V_Loss_FLT  	        : 1 ;   //逆变V相输出缺相故障
	uint8_t Inv_W_Loss_FLT  	        : 1 ;   //逆变W相输出缺相故障
	uint8_t OUT_GND_FLT  	        			: 1 ;   //输出接地故障
	
	uint8_t Res3                                : 4 ; //保留
}Inv_Fault;

// - 2.逆变锁死故障
typedef struct
{
/* 0Byte */
	uint8_t Inv_FAN1_LOCKFLT				: 1 ;	// 逆变 散热风扇1故障
	uint8_t Inv_FAN2_LOCKFLT				: 1 ;	// 逆变 散热风扇1故障
	uint8_t Inv_OUT1_LOCKFLT				: 1 ; 	// 逆变 输出1			
	uint8_t Inv_OUT2_LOCKFLT				: 1 ; 	// 逆变 输出2
	
	uint8_t Inv_OUT3_LOCKFLT				: 1 ; 	// 逆变 输出3
	uint8_t Res0								: 3 ;   // 保留 
/* 1Byte */	
	uint8_t Inv_BusOverVol3_LOCKFLT		: 1 ;   // 逆变	母线3级过压故障			
	uint8_t Inv_U_OverCur3_LOCKFLT    	: 1 ;   // U相3级过流故障		
	uint8_t Inv_V_OverCur3_LOCKFLT    	: 1 ;   // V相3级过流故障	
	uint8_t Inv_W_OverCur3_LOCKFLT   	: 1 ;   // W相3级过流故障
	
	uint8_t Inv_12VPowerOverVol1_LOCKFLT  	: 1 ;   // 12V主电源过压故障
	uint8_t Inv_12VPowerUnderVol_LOCKFLT  	: 1 ;   // 12V主电源欠压故障
	uint8_t Res1    					   		: 2 ;   // 保留
	
/* 2Byte */	
	uint8_t Inv_BusVolSensor_LOCKFLT    	: 1 ;   // 逆变母线电压传感器故障
	uint8_t Inv_U_CurSensor_LOCKFLT    : 1 ;   // 逆变U相输出交流电流采样传感器故障	
	uint8_t Inv_V_CurSensor_LOCKFLT   	: 1 ;   // 逆变V相输出交流电流采样传感器故障
	uint8_t Inv_W_CurSensor_LOCKFLT  	: 1 ;   // 逆变W相输出交流电流采样传感器故障
	
	uint8_t Inv_IGBT_Sensor_LOCKFLT  		: 1 ;   // 逆变IGBT温度传感器故障
	uint8_t Res2  	        					: 3 ;   // 驱动主板参考源故障
/* 3Byte */
	uint8_t DriveBoardRefeVol_LOCKFLT  	        : 1 ;   // 驱动主板参考源故障
	uint8_t Inv_U_LossLOCKFLT  	    : 1 ;   //逆变U相输出缺相故障
	uint8_t Inv_V_Loss_LOCKFLT   	    : 1 ;   //逆变V相输出缺相故障
	uint8_t Inv_W_Loss_LOCKFLT   	    : 1 ;   //逆变W相输出缺相故障
	
	uint8_t OUT_GND_LOCKFLT  	        		: 1 ;   //输出接地故障	
	uint8_t Res3                                : 3 ;	//保留
}Inv_LockFault;


// - 3.升压故障
typedef struct
{
/* 0Byte */
	uint8_t Boost_3Phase_N_FLT 					: 1 ; // 升压 三相输入电流负半波硬件保护
	uint8_t Boost_3Phase_O_FLT 					: 1 ; // 升压 三相输入电流负正波硬件保护
	uint8_t Boost_BusOverVol_FLT 				: 1 ; // 升压 母线过压硬件保护
	uint8_t Boost_IPM_FLT 						: 1 ; // 升压 桥臂短路硬件保护
	
	uint8_t Boost_Module_FLT					: 1 ; // 升压 模块硬件故障
	uint8_t Boost_FS_FLT    					: 1 ; // 升压 FS检测硬件故障
	uint8_t Boost_KMNO1_FLT						: 1 ; // 升压 主接触器故障
	uint8_t Boost_PrechargeRES_FLT				: 1 ; // 升压 预充电电阻故障
/* 1Byte */
	uint8_t Boost_CPU_OverTemp_FLT				: 1 ; // 升压 控制室过温故障（CPU温度）
	uint8_t Boost_L1_OverTemp_FLT    			: 1 ; // 升压 电感1过温故障
	uint8_t Boost_L2_OverTemp_FLT				: 1 ; // 升压 电感2过温故障
	uint8_t Boost_HeatSink_FLT					: 1 ; // 升压 散热器过温故障
	
	uint8_t Res0                                : 2 ; // 保留
	uint8_t Boost_ExternFlash_FLT               : 1 ; // 保留
	uint8_t Boost_IntranetCom_FLT               : 1 ; // 保留
/* 2Byte */	
	uint8_t Boost_R_Loss_FLT 				: 1 ; // 升压 R相输入缺相故障
	uint8_t Boost_S_Loss_FLT 				: 1 ; // 升压 S相输入缺相故障
	uint8_t Boost_T_Loss_FLT 				: 1 ; // 升压 T相输入缺相故障
	uint8_t Boost_BusUnderVol_FLT				: 1 ; // 升压 母线欠压一级故障
	
	uint8_t Boost_BusOverVol1_FLT				: 1 ; // 升压 母线过压一级故障
	uint8_t Boost_BusOverVol2_FLT 				: 1 ; // 升压 母线过压二级故障
	uint8_t Boost_BusOverVol3_FLT 				: 1 ; // 升压 母线过压三级故障
	uint8_t Boost_R_UnderVol1_FLT			: 1 ; // 升压 输入R相交流电压欠压一级故障
/* 3Byte */		
	uint8_t Boost_S_UnderVol1_FLT			: 1 ; // 升压 输入S相交流电压欠压一级故障
	uint8_t Boost_T_UnderVol1_FLT			: 1 ; // 升压 输入T相交流电压欠压一级故障	
	uint8_t Boost_R_UnderVol2_FLT			: 1 ; // 升压 输入R相交流电压欠压二级故障
	uint8_t Boost_S_UnderVol2_FLT			: 1 ; // 升压 输入S相交流电压欠压二级故障
	
	uint8_t Boost_T_UnderVol2_FLT			: 1 ; // 升压 输入T相交流电压欠压二级故障	
	uint8_t Boost_R_OverCurrrent1_FLT		: 1 ; // 升压 R相输入电流过流一级故障
	uint8_t Boost_R_OverCurrrent2_FLT		: 1 ; // 升压 R相输入电流过流二级故障
	uint8_t Boost_R_OverCurrrent3_FLT		: 1 ; // 升压 R相输入电流过流三级故障
	
/* 4Byte */	
	uint8_t Boost_S_OverCurrrent1_FLT		: 1 ; // 升压 S相输入电流过流一级故障
	uint8_t Boost_S_OverCurrrent2_FLT		: 1 ; // 升压 S相输入电流过流二级故障
	uint8_t Boost_S_OverCurrrent3_FLT		: 1 ; // 升压 S相输入电流过流三级故障
	
	uint8_t Boost_T_OverCurrrent1_FLT		: 1 ; // 升压 T相输入电流过流一级故障
	uint8_t Boost_T_OverCurrrent2_FLT		: 1 ; // 升压 T相输入电流过流二级故障
	uint8_t Boost_T_OverCurrrent3_FLT		: 1 ; // 升压 T相输入电流过流三级故障
	uint8_t Res1								: 2 ; // 保留
/* 5Byte */
	uint8_t Boost_BusVolSensor_FLT    	: 1 ;   // 升压 母线电压传感器故障
	uint8_t Boost_R_VolSensor_FLT   	: 1 ;   // 升压 R相输入交流电压采样传感器故障	
	uint8_t Boost_S_VolSensor_FLT   	: 1 ;   // 升压 S相输入交流电压采样传感器故障
	uint8_t Boost_T_VolSensor_FLT  		: 1 ;   // 升压 T相输入交流电压采样传感器故障
	
	uint8_t Boost_R_CurSensor_FLT   : 1 ;   // 升压 R相输入交流电流采样传感器故障	
	uint8_t Boost_S_CurSensor_FLT   : 1 ;   // 升压 S相输入交流电流采样传感器故障
	uint8_t Boost_T_CurSensor_FLT  	: 1 ;   // 升压 T相输入交流电流采样传感器故障
	uint8_t Res2                        : 1 ;   // 保留
/* 6Byte */
	uint8_t Boost_L1_Sensor_FLT   		: 1 ;   // 升压 电感1温度传感器故障	
	uint8_t Boost_L2_Sensor_FLT   		: 1 ;   // 升压 电感2温度传感器故障
	uint8_t Boost_HeakSink_Sensor_FLT  	: 1 ;   // 升压 散热器温度传感器故障
	uint8_t Res3                        : 5 ;   // 保留
/* 7Byte */
	
	uint8_t Boost_InPhase_FLT   		: 1 ;   // 升压 输入相序故障
	uint8_t Boost_InLowFreq_FLT   		: 1 ;   // 升压 输入频率低故障
	uint8_t Boost_InHighFreq_FLT  		: 1 ;   // 升压 输入频率高故障
	uint8_t Boost_BusLowCap_FLT         : 1 ;   // 升压 母线电容容量低故障
	uint8_t Boost_DriveBoardRefe_FLT    : 1 ;   // 升压 驱动主板参考源故障
	uint8_t Boost_MasterPowerGnd_FLT    : 1 ;   // 主电源接错故障
	uint8_t Res4						: 2 ;   // 保留
}Boost_Fault;

// - 4.升压锁死故障
typedef struct
{
/* 0Byte */
	uint8_t Boost_KMNO1_LockFLT						: 1 ; // 升压 主接触器故障
	uint8_t Boost_PrechargeRES_LockFLT				: 1 ; // 升压 预充电电阻故障
	uint8_t Res0									: 3 ; // 保留
	
	uint8_t Boost_R_Loss_LockFLT 				: 1 ; // 升压 R相输入缺相故障
	uint8_t Boost_S_Loss_LockFLT 				: 1 ; // 升压 S相输入缺相故障
	uint8_t Boost_T_Loss_LockFLT  				: 1 ; // 升压 T相输入缺相故障
/* 1Byte */
	uint8_t Boost_BusOverVol3_LockFLT 				: 1 ; // 升压 母线过压三级故障
	uint8_t Boost_R_OverCurrrent3_LockFLT		: 1 ; // 升压 R相输入电流过流三级故障
	uint8_t Boost_S_OverCurrrent3_LockFLT		: 1 ; // 升压 S相输入电流过流三级故障
	uint8_t Boost_T_OverCurrrent3_LockFLT		: 1 ; // 升压 T相输入电流过流三级故障
	
	uint8_t Res1									: 4 ; // 保留
/* 2Byte */	
	uint8_t Boost_BusVolSensor_LockFLT    	: 1 ;   // 升压 母线电压传感器故障
	uint8_t Boost_R_VolSensor_LockFLT   	: 1 ;   // 升压 R相输入交流电压采样传感器故障	
	uint8_t Boost_S_VolSensor_LockFLT   	: 1 ;   // 升压 S相输入交流电压采样传感器故障
	uint8_t Boost_T_VolSensor_LockFLT  		: 1 ;   // 升压 T相输入交流电压采样传感器故障
	
	uint8_t Boost_R_CurSensor_LockFLT   : 1 ;   // 升压 R相输入交流电流采样传感器故障	
	uint8_t Boost_S_CurSensor_LockFLT   : 1 ;   // 升压 S相输入交流电流采样传感器故障
	uint8_t Boost_T_CurSensor_LockFLT  	: 1 ;   // 升压 T相输入交流电流采样传感器故障
	uint8_t Res2                        	: 1 ;   // 保留
	
/* 3Byte */		
	uint8_t Boost_L1_Sensor_LockFLT   		: 1 ;   // 升压 电感1温度传感器故障	
	uint8_t Boost_L2_Sensor_LockFLT   		: 1 ;   // 升压 电感2温度传感器故障
	uint8_t Boost_HeakSink_Sensor_LockFLT  	: 1 ;   // 升压 散热器温度传感器故障
	uint8_t Boost_DriveBoardRefe_LockFLT    : 1 ;   // 升压 驱动主板参考源故障
	
	uint8_t Boost_BusLowCap_LockFLT         : 1 ;   // 升压 母线电容容量低故障
	uint8_t Res3                        	: 3 ;   // 保留
}Boost_LockFault;



// - 1.版本号
typedef struct
{
	uint16_t InvSoftVersion;   		//逆变软件版本
	uint16_t DriveBoardHardware;    //驱动主板硬件版本
	uint16_t ControlBoardHardware;  //控制硬件版本
	uint16_t CapHardware;           //电容硬件版本
	uint16_t InductanceHardware;    //电感硬件版本
}BsmFrame01,Bsm_Ver_t;


// - 2.RTC时间
typedef struct
{
	uint8_t Year;
	uint8_t Month;
	uint8_t Day;
	uint8_t Hour;
	uint8_t Min;
	uint8_t Sec;
}BsmFrame02,date_t;

// - 3.数字量输入
typedef struct
{
// -| 0 Byte
	uint8_t In1  	: 1 ;
	uint8_t In2		: 1 ;
	uint8_t In3		: 1 ;	
	uint8_t In4		: 1 ;
	uint8_t Res0							: 4 ;
	
// -| 1 Byte
	uint8_t HardwareVer1			: 1 ;
	uint8_t HardwareVer2			: 1 ;
	uint8_t HardwareVer3			: 1 ;
	
	uint8_t S11					: 1 ;
	uint8_t S22					: 1 ;
	
	uint8_t Addr1				: 1 ;
	uint8_t Addr2				: 1 ;
	
	uint8_t Res1							: 1 ;
	
}BsmFrame03;

// - 4.数字量输出
typedef struct
{
// - 0 Byte
	uint8_t DO1  	: 1 ;
	uint8_t DO2		: 1 ;
	uint8_t DO3		: 1 ;	
	uint8_t Res0							: 5 ;
}BsmFrame04;

// - 5.获取模拟量数值
typedef struct
{
// - 0 Byte
	uint32_t Inv_BusVol ; 				//逆变母线电压采样
	uint32_t Inv_OutU_Cur	;	//逆变输出U相电流采样	
	uint32_t Inv_OutV_Cur	;	//逆变输出V相电流采样		
	uint32_t Inv_OutW_Cur	;	//逆变输出W相电流采样
	
	uint32_t Inv_0_10v_Governor	;	//逆变外接0-10V调速采样
	uint32_t Inv_4_20mA_Governor	;	//逆变外接0-10V调速采样	
	
	uint32_t Inv_5v	;				//逆变5V采样
	uint32_t Inv_12v_ControlVol	;	//逆变12v控制电压采样
	
	uint32_t Inv_IGBT_TEMP	;	//逆变IGBT温度采样
	uint32_t Inv_CPU_TEMP	;	//逆变CPU温度采样
}BsmFrame05;


// - 6.设置参数
typedef struct
{	
	uint16_t Bus_UnderVol;
	uint16_t Bus_OverVol_1;
	uint16_t Bus_OverVol_2;
	
}BsmFrame06;

// - 7.故障总条目读取（0x07）
typedef struct
{
	// - 1.序号和时间
	uint32_t Seq;
	date_t date;
// - 2.逆变故障	
	uint8_t Inv_AllFault_State[6];
	uint8_t Inv_DeadLock_Fault_State[4];
	uint8_t Inv_Fault_State;
// - 3.电机运行参数状态	
	uint8_t Inv_MotorWorkMode;   	// 电机工作模式
	uint8_t Inv_MotorControlState; // 电机控制状态
	uint8_t Inv_LibCode;       	// 电机库代码
	uint16_t Inv_MotorEncoder; 	// 电机编码器
	uint16_t Inv_MotorFreqHz;  	// 电机频率Hz
	uint16_t Inv_MotorRotateSpeed; // 电机转速
	
// - 4.功率因数谐波等
	uint16_t Inv_VolOUT_Harmonic;  		//逆变 输出电压谐波
	uint16_t Inv_CurOUT_Harmonic;  	//逆变 输出电流谐波
	uint16_t Inv_Power_Factor;  			//逆变 输出功率因数
// - 5.逆变模拟量输出
	int32_t Inv_BusVol ; 				//逆变 母线电压
	int32_t Inv_OutU_Cur	;	//逆变 输出U相电流	
	int32_t Inv_OutV_Cur	;	//逆变 输出V相电流		
	int32_t Inv_OutW_Cur	;	//逆变 输出W相电流
	
	int32_t Inv_0_10v_Governor	;		//逆变 外接0-10V调速
	int32_t Inv_4_20mA_Governor	;	//逆变 外接0-10V调速	
	
	int32_t Inv_5v	;					//逆变 5V
	int32_t Inv_12v_ControlVol	;		//逆变 12v控制电压
	
	int32_t Inv_IGBT_TEMP	;			//逆变 IGBT温度
	int32_t Inv_CPU_TEMP	;			//逆变 CPU温度
// - 6.输入数字量	
// -| 0 Byte
	uint8_t HardwareVer1			: 1 ;
	uint8_t HardwareVer2			: 1 ;
	uint8_t HardwareVer3			: 1 ;
	
	uint8_t S11					: 1 ;
	uint8_t S22					: 1 ;
	
	uint8_t Addr1				: 1 ;
	uint8_t Addr2				: 1 ;
	
	uint8_t Res1				: 1 ;

// -| 1 Byte
	uint8_t In1  	: 1 ;
	uint8_t In2		: 1 ;
	uint8_t In3		: 1 ;	
	uint8_t In4		: 1 ;
	uint8_t Res0							: 4 ;

// - 7.硬件故障信号状态
	// -  1.其他故障反馈
	uint8_t Inv_Fan1_FLT_State   	: 1 ; 	// 逆变散热风扇1故障(1:正常 0:故障
	uint8_t Inv_Fan2_FLT_State		: 1 ; 	// 逆变散热风扇1故障(1:正常 0:故障
	
	uint8_t Inv_3Phase_N_FLT_State	: 1 ; 	// 逆变三相输出电流负半波硬件保护状态(1:正常 0:故障
	uint8_t Inv_3Phase_O_FLT_State	: 1 ; 	// 逆变三相输出电流正半波硬件保护状态(1:正常 0:故障
	
	uint8_t Inv_IGBT_FLT_State		: 1 ; 	// 逆变IGBT短路保护状态(1:正常 0:故障
		
	
	uint8_t Inv_FS_INV_FLT	: 1 ; 	//逆变检测逆变FS输出状态 (1:正常0:停机
	uint8_t Inv_IPM_FLT		: 1 ; 	//逆变电机驱动模块故障状态 (1:正常0:停机
	uint8_t Res2						: 1 ; 	//保留

	// -  2.输出控制故障
	uint8_t Inv_O1_FLT 		: 1 ;  		//逆变输出1故障反馈 (1:故障 0:正常)
	uint8_t Inv_O2_FLT 		: 1 ;  		//逆变输出2故障反馈 (1:故障 0:正常)
	uint8_t Inv_O3_FLT 		: 1 ;  		//逆变输出3故障反馈 (1:故障 0:正常)
	uint8_t Res3					  	: 5 ; 		//保留
	
// - 8.输出控制信号
	uint16_t Inv_Fan_Pwm_OUT;  		//逆变 风扇PWM输出
	uint32_t Inv_DAC1_Vol_OUT; 		//逆变 DAC1电压输出
	uint32_t Inv_DAC2_Vol_OUT; 		//逆变 DAC2电压输出
	
	int8_t Inv_MotorDrive_Enable 	: 1 ; //逆变 电机驱动使能（1:使能 0:关闭）（只读
	uint8_t Inv_FLT_Clear     		: 1 ; //逆变 电机驱动使能（1:使能 0:关闭）（只读
	uint8_t Inv_O1 	: 1 ;  //逆变 数字输出1控制
	uint8_t Inv_O2 	: 1 ;  //逆变 数字输出2控制
	uint8_t Inv_O3 	: 1 ;  //逆变 数字输出3控制
	uint8_t Res5                    	: 3 ;  //保留
	
// - 9.功能预留口	
	uint8_t Inv_IO1_OUT  : 1 ;  		//逆变IO1状态（喂狗DO）
	uint8_t Inv_IO2_IN   : 1 ;  		//逆变IO2状态（喂狗DI）
	uint8_t Inv_IO3_OUT  : 1 ;  		//逆变IO3状态（升压硬线启动信号DO  1:启动  0:停机
	uint8_t Inv_IO4_OUT  : 1 ;  		//逆变IO4状态（升压硬线故障信号DO  1:正常 0:故障）
	uint8_t Inv_IO5  	: 1 ;  		//逆变IO5状态（保留）
	uint8_t Res8	                : 3 ;		//预留
// - 10.通讯灯状态	
	uint8_t Inv_CAN_COM_State  		: 1 ;  		//CAN通讯状态
	uint8_t Inv_485_COM_State  		: 1 ;  		//485通讯状态
	
	uint8_t Inv_Intranet_COM_State  	: 1 ;  		//逆变内网通讯状态
	uint8_t PFC_Intranet_COM_State  		: 1 ;  		//升压内网通讯状态
	
	uint8_t Inv_WIFI_COM_State  		: 1 ;  		//WiFi串口通信状态
	uint8_t Inv_WIFI_NET_State  		: 1 ;  		//WiFi网络通信状态
	uint8_t Res10							: 2 ;       // 预留
// - 11.变频器操作记录状态
	uint32_t Inv_PowerOnTime; 				//累计上电时间（H）
	uint32_t Inv_WorkTime;					//压缩机累计工作时间（H）
	uint32_t Inv_380VPowerOn_Cnt;		//累计380V上电次数（次）
	uint32_t Inv_380VPowerConsumption;		//累计380V消耗电量（KW.H）
	uint32_t Inv_FAN_WorkTime;             //散热器风扇工作时间（H)
	
	uint8_t Inv_Motor_Manufacturer;       	//电机厂家
	uint8_t Inv_Motor_Type;       			//电机类型
	uint8_t Inv_Motor_NumberofPoles;       //电机级数
	uint8_t Inv_Motor_Dir;       			//电机转向
	
	uint8_t Can_X_Adrr;   //can x通讯地址
	uint8_t Can_Y_Adrr;   //can y通讯地址
}BsmFrame07;

// - 8.指定条目故障数据读取（0x08）
typedef struct
{
	BsmFrame07 Ack;
}BsmFrame08;

// - 9.指定时间故障数据读取（0x09）
typedef struct
{
	BsmFrame07 Ack;
}BsmFrame09;

// - 10.升级固件信息
typedef struct
{	
	//接收帧
	struct{
		uint8_t  FWSW;   		   //固件升级选择	
		uint8_t  BinFileName[8];   //固件名字
		uint32_t BinFileSize;      //固件大小
		uint32_t BinVersionNum;    //固件版本号
		uint32_t BinSubSize;       //子包大小
	}Rev;
	
	//应答帧
	struct{
		uint8_t  FWSW;   				  //固件升级选择
		uint8_t  SaveBinFileInfoFlag;     //保存固件信息状态
		uint8_t  EraseFlashFlag;          //擦除固件信息状态
	}Ack;
	

}BsmFrame0A;

// - 11.升级固件分包帧
typedef struct
{
	//接收帧
	struct{
		uint8_t BinFileFrameData[1024];   	//固件数据
	}Rev;
	
	//应答帧
	struct{
		uint16_t  BinFileFrameNum;     	   //帧序号
		
		uint8_t Is_OneFrameRev_OK : 1 ;    //接收完1帧OK
		uint8_t Is_AllFrameRev_OK : 1 ;    //接收完所有帧OK	
		uint8_t Is_AllFrameCRC_OK : 1 ;    //接收完所有帧CRC
		uint8_t Res1              : 5 ;    //保留
	}Ack;
}BsmFrame0B;

// - 12.获取变频器状态信息（0x10）
typedef struct
{
// - 1 模拟量校准值
	uint32_t Inv_BusVol; 		//逆变母线电压采样
	uint32_t Inv_OutU_Cur_Cal;	//逆变输出U相电流采样	
	uint32_t Inv_OutV_Cur_Cal;	//逆变输出V相电流采样		
	uint32_t Inv_OutW_Cur_Cal;	//逆变输出W相电流采样
	
	uint32_t Inv_0_10v_Governor_Cal;	//逆变外接0-10V调速采样
	uint32_t Inv_4_20mA_Governor_Cal;	//逆变外接0-10V调速采样	
	
	uint32_t Inv_5v_Cal	;				//逆变5V采样
	uint32_t Inv_12v_ControlVol_Cal;	//逆变12v控制电压采样
	
	uint32_t Inv_IGBT_TEMP_Cal;			//逆变IGBT温度采样
	uint32_t Inv_CPU_TEMP_Cal;			//逆变CPU温度采样
	
	
// - 2 模拟量数值
	uint16_t Inv_VolOUT_Harmonic;  	//逆变输出电压谐波
	uint16_t Inv_CurOUT_Harmonic;  	//逆变输出电流谐波
	uint16_t Inv_Power_Factor;  	//逆变输出功率因数
	
	uint32_t Inv_OutU_Vol;	//逆变输出U相电压
	uint32_t Inv_OutV_Vol;	//逆变输出V相电压		
	uint32_t Inv_OutW_Vol;	//逆变输出W相电压
	
	uint32_t Inv_Bus_Vol;	//逆变母线电压
	
	uint32_t Inv_OutU_Cur;	//逆变输出U相电流	
	uint32_t Inv_OutV_Cur;	//逆变输出V相电流		
	uint32_t Inv_OutW_Cur;	//逆变输出W相电流
	
	uint32_t Inv_0_10v_Governor	;	//逆变外接0-10V调速
	uint32_t Inv_4_20mA_Governor;	//逆变外接0-10V调速	
	
	uint32_t Inv_5v	;				//逆变5V
	uint32_t Inv_12v_ControlVol	;	//逆变12v控制电压
	
	uint32_t Inv_IGBT_TEMP	;	//逆变IGBT温度
	uint32_t Inv_CPU_TEMP	;	//逆变CPU温度
	
// - 3.硬件故障信号状态
// - 3.1 其他故障反馈
	uint8_t Inv_Fan1_FLT   		: 1 ; 	// 逆变散热风扇1故障(1:正常 0:故障
	uint8_t Inv_Fan2_FLT		: 1 ; 	// 逆变散热风扇1故障(1:正常 0:故障
	
	uint8_t Inv_3Phase_N_FLT	: 1 ; 	// 逆变三相输出电流负半波硬件保护状态(1:正常 0:故障
	uint8_t Inv_3Phase_O_FLT	: 1 ; 	// 逆变三相输出电流正半波硬件保护状态(1:正常 0:故障
	
	uint8_t Inv_IGBT_FLT		: 1 ; 	// 逆变IGBT短路保护状态(1:正常 0:故障
		
	
	uint8_t Inv_FS_INV_FLT	: 1 ; 	//逆变检测逆变FS输出状态 (1:正常0:停机
	uint8_t Inv_IPM_FLT		: 1 ; 	//逆变电机驱动模块故障状态 (1:正常0:停机
	uint8_t Res0							: 1 ; 	//保留

// - 3.2 输出控制故障
	uint8_t Inv_O1_FLT : 1 ;  		//逆变输出1故障反馈 (1:故障 0:正常)
	uint8_t Inv_O2_FLT : 1 ;  		//逆变输出2故障反馈 (1:故障 0:正常)
	uint8_t Inv_O3_FLT : 1 ;  		//逆变输出3故障反馈 (1:故障 0:正常)
	uint8_t Res1	   : 5 ; 		//保留
	
// - 4 输出控制信号状态	
	uint16_t Inv_Fan_Pwm_OUT;  			// 风扇PWM占空比输出
	uint32_t Inv_DAC1_Vol_OUT; 			// DAC1电压输出
	uint32_t Inv_DAC2_Vol_OUT; 			// DAC2电压输出
	
	uint8_t Inv_MotorDrive_Enable 	: 1 ;    //逆变电机驱动使能（1:使能 0:关闭）（只读
	uint8_t Inv_FLT_Clear     		: 1 ; 	//逆变电机驱动使能（1:使能 0:关闭）（只读
	uint8_t Res2              : 6 ;
	
	uint8_t Inv_O1 : 1 ;  		//逆变数字输出1控制
	uint8_t Inv_O2 : 1 ;  		//逆变数字输出2控制
	uint8_t Inv_O3 : 1 ;  		//逆变数字输出3控制
	uint8_t Res3   : 5 ;
// - 5 功能预留口
	uint8_t Inv_IO1_OUT  : 1 ;  		//逆变IO1状态（喂狗DO）
	uint8_t Inv_IO2_IN   : 1 ;  		//逆变IO2状态（喂狗DI）
	uint8_t Inv_IO3_OUT  : 1 ;  		//逆变IO3状态（升压硬线启动信号DO  1:启动  0:停机
	uint8_t Inv_IO4_OUT  : 1 ;  		//逆变IO4状态（升压硬线故障信号DO  1:正常 0:故障）
	uint8_t Inv_IO5  	 : 1 ;  		//逆变IO5状态（保留）
	uint8_t Res4	     : 3 ;		    //预留
	
// - 6.通讯状态	
	uint8_t Inv_CAN_COM_State  		: 1 ;  		//CAN通讯状态
	uint8_t Inv_485_COM_State  		: 1 ;  		//485通讯状态
	
	uint8_t Inv_Intranet_COM_State  	: 1 ;  		//逆变内网通讯状态
	uint8_t PFC_Intranet_COM_State  	: 1 ;  		//升压内网通讯状态
	
	uint8_t Inv_WIFI_COM_State  		: 1 ;  		//WiFi串口通信状态
	uint8_t Inv_WIFI_NET_State  		: 1 ;  		//WiFi网络通信状态
	uint8_t Res5						: 2 ;       // 预留
// - 7.大数据记录	
	uint32_t Inv_PowerOnTime; 				//累计上电时间（H）
	uint32_t Inv_WorkTime;					//压缩机累计工作时间（H）
	uint32_t Inv_380VPowerOn_Cnt;			//累计380V上电次数（次）
	uint32_t Inv_380VPowerConsumption;		//累计380V消耗电量（KW.H）
	uint32_t Inv_FAN_WorkTime;             	//散热器风扇工作时间（H)

	uint8_t Inv_Motor_Manufacturer;       	//电机厂家
	uint8_t Inv_Motor_Type;       			//电机类型
	uint8_t Inv_Motor_NumberofPoles;       	//电机级数
	uint8_t Inv_Motor_Dir;       			//电机转向	
}BsmComFrame10;




// - 13.设置变频器参数（0x11）
typedef struct
{
	uint32_t Inv_BusVol_Cal; 	//逆变母线电压采样
	uint32_t Inv_OutU_Cur_Cal;	//逆变输出U相电流采样	
	uint32_t Inv_OutV_Cur_Cal;	//逆变输出V相电流采样		
	uint32_t Inv_OutW_Cur_Cal;	//逆变输出W相电流采样
	
	uint32_t Inv_0_10v_Governor_Cal	;	//逆变外接0-10V调速采样
	uint32_t Inv_4_20mA_Governor_Cal;	//逆变外接0-10V调速采样	
	
	uint32_t Inv_5v_Cal	;				//逆变5V采样
	uint32_t Inv_12v_ControlVol_Cal;	//逆变12v控制电压采样
	
	uint32_t Inv_IGBT_TEMP_Cal;			//逆变IGBT温度采样
	uint32_t Inv_CPU_TEMP_Cal;			//逆变CPU温度采样
	
	uint16_t Inv_Fan_Pwm_OUT;  			// 风扇PWM输出
	uint32_t Inv_DAC1_Vol_OUT; 			// DAC1电压输出
	uint32_t Inv_DAC2_Vol_OUT; 			// DAC2电压输出
	
	uint8_t Inv_O1 : 1 ;  		//逆变数字输出1控制
	uint8_t Inv_O2 : 1 ;  		//逆变数字输出2控制
	uint8_t Inv_O3 : 1 ;  		//逆变数字输出3控制
	uint8_t Res0   : 5 ;

}BsmFrame11;


// - 14.设置AD校准参数（0x12）
typedef struct
{
	uint32_t Inv_BusVol_Cal ; 	//逆变母线电压采样
	uint32_t Inv_OutU_Cur_Cal;	//逆变输出U相电流采样	
	uint32_t Inv_OutV_Cur_Cal;	//逆变输出V相电流采样		
	uint32_t Inv_OutW_Cur_Cal;	//逆变输出W相电流采样
	
	uint32_t Inv_0_10v_Governor_Cal;	//逆变外接0-10V调速采样
	uint32_t Inv_4_20mA_Governor_Cal;	//逆变外接0-10V调速采样	
	
	uint32_t Inv_5v_Cal	;				//逆变5V采样
	uint32_t Inv_12v_ControlVol_Cal;	//逆变12v控制电压采样
	
	uint32_t Inv_IGBT_TEMP_Cal;	//逆变IGBT温度采样
	uint32_t Inv_CPU_TEMP_Cal;	//逆变CPU温度采样	
}BsmFrame12;


// - 15.设置输出状态（0x13）
typedef struct
{
	uint8_t Inv_O1 : 1 ;  //逆变 数字输出1控制
	uint8_t Inv_O2 : 1 ;  //逆变 数字输出2控制
	uint8_t Inv_O3 : 1 ;  //逆变 数字输出3控制
	uint8_t Res0   : 3 ;  //保留
	
	uint8_t Inv_MotorDrive_Enable 	: 1 ; //逆变 电机驱动使能（1:使能 0:关闭）（只读
	uint8_t Inv_FLT_Clear     		: 1 ; //逆变 电机驱动使能（1:使能 0:关闭）（只读
	
	uint16_t Inv_Fan_Pwm_OUT;  // 风扇PWM输出
	uint32_t Inv_DAC1_Vol_OUT; // DAC1电压输出
	uint32_t Inv_DAC2_Vol_OUT; // DAC2电压输出
	uint8_t  Inv_Control_Power;// 控制权转移 1：上位机控制  0：下位机控制
}BsmFrame13;

// - 16.获取参数修改记录（0x14）
typedef struct
{
// - 1.时间和序号
	uint8_t Seq;
	date_t date;
	
// - 1.逆变AD校准
	int32_t Inv_BusVol_Cal ; 	//逆变 母线电压校准
	int32_t Inv_OutU_Cur_Cal;	//逆变 输出U相电流校准	
	int32_t Inv_OutV_Cur_Cal;	//逆变 输出V相电流校准		
	int32_t Inv_OutW_Cur_Cal;	//逆变 输出W相电流校准
	
	int32_t Inv_0_10v_Governor_Cal;		//逆变 外接0-10V调速校准
	int32_t Inv_4_20mA_Governor_Cal;	//逆变 外接0-10V调速校准	
	
	int32_t Inv_5v_Cal	;				//逆变 5V校准
	int32_t Inv_12v_ControlVol_Cal;		//逆变 12v控制电压校准
	
	int32_t Inv_IGBT_TEMP_Cal;			//逆变 IGBT温度校准
	int32_t Inv_CPU_TEMP_Cal;			//逆变 CPU温度校准
	
	// - 3.设置参数范围	
	uint16_t Bus_Undervol_Value;  		// - 逆变 母线欠压值设置（范围-5%，滞回+5V）≤240V
	uint16_t Bus_Overvoltage_1;       	// - 逆变 母线一级过压值设置（范围+5%，滞回-5V）≥800V
	uint16_t Bus_Overvoltage_2;      	// - 逆变 母线二级过压值设置（范围+5%，滞回-5V）≥860V  
}BsmFrame14;

// - 16.设置RTC时间（0x15）
typedef struct
{
	date_t date;
}BsmFrame15;

// - 17.设置条形码（0x21）
typedef struct
{
	uint8_t DriveBoardHwBarCode[20];    //驱动主板硬件版本
	uint8_t ControlBoardHwBarCode[20];  //控制硬件版本
	uint8_t CapHwBarCode[20];           //电容硬件版本
	uint8_t InductanceHwBarCode[20];    //电感硬件版本
}BsmFrame22;


// - 18.获取条形码（0x22）
typedef struct
{
	BsmFrame22 Rev;	
}BsmFrame21;



// - 19.设置CAN通讯地址（0x23）
typedef struct
{
	uint8_t Can_X_Adrr;   //can x通讯地址
	uint8_t Can_Y_Adrr;   //can y通讯地址
}BsmFrame23;

// - 20.设置硬件版本（0x24）
typedef struct
{
	uint16_t DriveBoardHardware;    //驱动主板硬件版本
	uint16_t ControlBoardHardware;  //控制硬件版本
	uint16_t CapHardware;           //电容硬件版本
	uint16_t InductanceHardware;    //电感硬件版本
}BsmFrame24;



typedef struct
{
    uint8_t bit0 : 1;
    uint8_t bit1 : 1;
    uint8_t bit2 : 1;
    uint8_t bit3 : 1;
    uint8_t bit4 : 1;
    uint8_t bit5 : 1;
    uint8_t bit6 : 1;
    uint8_t bit7 : 1;
    
    uint8_t bit8 : 1;
    uint8_t bit9 : 1;
    uint8_t bit10 : 1;
    uint8_t bit11 : 1;
    uint8_t bit12 : 1;
    uint8_t bit13 : 1;
    uint8_t bit14 : 1;
    uint8_t bit15 : 1;
    
    uint8_t bit16 : 1;
    uint8_t bit17 : 1;
    uint8_t bit18 : 1;
    uint8_t bit19 : 1;
    uint8_t bit20 : 1;
    uint8_t bit21 : 1;
    uint8_t bit22 : 1;
    uint8_t bit23 : 1;
    
    uint8_t bit24 : 1;
    uint8_t bit25 : 1;
    uint8_t bit26 : 1;
    uint8_t bit27 : 1;
    uint8_t bit28 : 1;
    uint8_t bit29 : 1;
    uint8_t bit30 : 1;
    uint8_t bit31 : 1;
    
    uint8_t bit32 : 1;
    uint8_t bit33 : 1;
    uint8_t bit34 : 1;
    uint8_t bit35 : 1;
    uint8_t bit36 : 1;
    uint8_t bit37 : 1;
    uint8_t bit38 : 1;
    uint8_t bit39 : 1;
 
}bit_t;


// - 1.获取逆变和升压  变频器状态信息（0x81）
typedef struct
{
    uint32_t time_stamp;
    uint8_t  bit0_valid     : 1;
    uint8_t  bit1_dio_ctrl  : 1;
    uint8_t  bit2_ptu_ctrl  : 1;
    uint8_t  res : 5;
    
    
    
    bit_t    mask;
    bit_t    val;
    uint16_t inv_freq_set;
}BsmFrame81_RPDO;

// - 2.设置逆变和升压  变频器参数（0x82）
typedef struct
{
// - 1.逆变AD校准  4*10 = 40Byte 0
	int32_t Inv_BusVol_Cal ; 	//逆变 母线电压校准
	int32_t Inv_U_OutCur_Cal;	//逆变 输出U相电流校准	
	int32_t Inv_V_OutCur_Cal;	//逆变 输出V相电流校准		
	int32_t Inv_W_OutCur_Cal;	//逆变 输出W相电流校准
	
	int32_t Inv_0_10v_Cal;	//逆变 外接0-10V调速校准
	int32_t Inv_4_20mA_Cal;	//逆变 外接0-10V调速校准	
	
	int32_t Inv_5v_Cal;		//逆变 5V校准
	int32_t Inv_12v_Cal;	//逆变 12v控制电压校准
	
	int32_t Inv_IGBT_TEMP_Cal	;			//逆变 IGBT温度校准
	int32_t Inv_CPU_TEMP_Cal	;			//逆变 CPU温度校准

// - 2.升压AD校准 12*4 = 48Byte 40
	int32_t PFC_BusVol_Cal ; 	//PFC 母线电压校准
	int32_t PFC_R_InCur_Cal	;	//PFC 输入U相电流校准	
	int32_t PFC_S_InCur_Cal	;	//PFC 输入V相电流校准		
	int32_t PFC_T_ICur_Cal	;		//PFC 输入W相电流校准
	
	int32_t PFC_R_InVol_Cal	;		//PFC 输入U相电压校准	
	int32_t PFC_S_InVol_Cal	;		//PFC 输入V相电压校准		
	int32_t PFC_T_InVol_Cal	;		//PFC 输入W相电压校准
	
	int32_t PFC_L1_Temp_Cal;				//PFC 电感1温度补偿
	int32_t PFC_L2_Temp_Cal;				//PFC 电感2温度补偿
	
	int32_t PFC_IPM_Cur_Cal;			//PFC 桥臂电流偿
	int32_t PFC_HeatSink_Temp_Cal;		//PFC 散热器风扇温度补偿
	int32_t PFC_CPU_Temp_Cal;			//PFC CPU芯片温度补偿
	
// - 3.输出 12Byte  88
/* offset 88 */    uint8_t     Ctrl_KMON1      ;
/* offset 89 */    uint8_t     Ctrl_KMON2      ;
/* offset 90 */    uint8_t     Ctrl_KMON3      ;
    
    uint16_t        modbus_rx_cnter;
    uint16_t        modbus_tx_cnter;
    uint8_t         blank00;
/* offset 96 */    uint16_t    SetEmergInvOutputFreq;
    uint16_t        modbus_rx_add_err_cnter;

    
// - 4.电机参数	 4Byte  100
	uint8_t Inv_Motor_Manufacturer;       	//电机厂家
	uint8_t Inv_Motor_Type;       			//电机类型
	uint8_t Inv_Motor_NumberofPoles;       	//电机级数
	uint8_t Inv_Motor_Dir;       			//电机转向

// - 5.版本号 8Byte 104
	uint16_t DriveBoardHardware;    //驱动主板硬件版本
	uint16_t ControlBoardHardware;  //控制硬件版本
	uint16_t CapHardware;           //电容硬件版本
	uint16_t InductanceHardware;    //电感硬件版本

// - 6.CAN的通讯地址 2Byte 112
	uint8_t Can_X_Adrr;   //can x通讯地址
	uint8_t Can_Y_Adrr;   //can y通讯地址

/* offset 114 */    uint32_t    ac_power_consumption;   
/* offset 118 */    uint32_t    dc_power_consumption;   //
    
/* offset 122 - 130 ⬇ */     
/* offset 122 */    uint8_t     Ctrl_ClearAll   ;
/* offset 123 */    uint8_t     Ctrl_O1         ;
/* offset 124 */    uint8_t     Ctrl_O2         ;
/* offset 125 */    uint8_t     Ctrl_O3         ;
/* offset 126 */    uint8_t     Ctrl_I1         ;
/* offset 127 */    uint8_t     Ctrl_I2         ;
/* offset 128 */    uint8_t     Ctrl_I3         ;
/* offset 129 */    uint8_t     Ctrl_I4         ;

/* offset 130 */    uint8_t     Ctrl_DCDC_Duty;

/* offset 131 */    uint8_t     Ctrl_PFC_U_Duty;
/* offset 132 */    uint8_t     Ctrl_PFC_V_Duty;
/* offset 133 */    uint8_t     Ctrl_PFC_W_Duty;
        
/* offset 134 */    uint8_t     Ctrl_Fan_Duty;
/* offset 135 */    uint8_t     Ctrl_Inv_U_Duty;
/* offset 136 */    uint8_t     Ctrl_Inv_V_Duty;
/* offset 137 */    uint8_t     Ctrl_Inv_W_Duty;
    
/* offset 138 */    uint8_t     Ctrl_Addr1;
/* offset 139 */    uint8_t     Ctrl_Addr2;
/* offset 140 */    int16_t     adca_vin_r;
 
/* offset 142 */    int16_t     adca_vin_s;
/* offset 144 */    int16_t     adca_vin_t;
      
/* offset 146 */    int16_t     adca_iin_r;
/* offset 148 */    int16_t     adca_iin_s;
 
/* offset 150 */    int16_t     adca_iin_t;
/* offset 152 */    int16_t     adca_vbus_pfc;
 
/* offset 154 */    int16_t     adca_vbus_inv;
/* offset 156 */    int16_t     adca_iout_u;

/* offset 158 */    int16_t     adca_iout_v;
/* offset 114 */    int16_t     adca_iout_w;
   
/* offset 162 */    uint32_t    rs485_baudrate;     //485外网波特率配置
/* offset 166 */    uint32_t    can_baudrate;       //can波特率配置
 
/* offset 170 */    uint8_t     rs485_check_bit;    //485外网波特率配置
/* offset 171 */    uint8_t     Serial_Adrr;        //串行通讯地址  包含 PTU/modbus
/* offset 172 */    uint8_t     clear_all_timer;
/* offset 173 */    uint8_t     run_duty;                //
  
/* offset 174 */    uint16_t    SetInvOutputFreq;
/* offset 176 */    uint16_t    SetPfcWorkVolt;
/* offset 178 */    uint16_t    SetInvOutputRpm;  
/* offset 180 */    uint16_t    SetDcdcWorkMinute;

/* offset 182 */    uint32_t    pfc_startup_time; //累计三相启停次数
/* offset 186 */    uint32_t    kmon2_on_time;    //累计主继电器吸合次数
/* offset 190 */    uint32_t    kmon2_off_time;   //累计主继电器断开次数
/* offset 194 */    uint32_t    sys_run_time;     //累计系统运行时间
/* offset 198 */    uint32_t    fan_run_time;     //累计风扇散热时间
/* offset 202 */    uint32_t    vfd_poweron_time; //累计380V上电次数    
/* offset 206 */    uint32_t    inv_run_time;     //累计逆变运行时间   
  
/* offset 210 */    uint16_t    VF_Linear_StepUp;
/* offset 212 */    uint16_t    VF_Linear_StepDown;

/* offset 214 */    uint16_t    VF_Linear_F1;
/* offset 216 */    uint16_t    VF_Linear_V1;
  
/* offset 218 */    uint16_t    VF_Linear_F2;
/* offset 220 */    uint16_t    VF_Linear_V2;
    
/* offset 222 */    uint16_t    VF_Linear_F3;
/* offset 224 */    uint16_t    VF_Linear_V3;
            
/* offset 226 */    uint32_t    statistics_data[50];
}BsmFrame82;

// - 3.获取逆变和升压  AD校准信息（0x83）
typedef struct
{
	// - 1.逆变AD校准
	int32_t Inv_BusVol_Cal ; 		//逆变 母线电压校准
	int32_t Inv_OutU_Cur_Cal	;	//逆变 输出U相电流校准	
	int32_t Inv_OutV_Cur_Cal	;	//逆变 输出V相电流校准		
	int32_t Inv_OutW_Cur_Cal	;	//逆变 输出W相电流校准
	
	int32_t Inv_0_10v_Cal	;		//逆变 外接0-10V调速校准
	int32_t Inv_4_20mA_Cal	;		//逆变 外接0-10V调速校准	
	
	int32_t Inv_5v_Cal	;			//逆变 5V校准
	int32_t Inv_12v_Cal	;			//逆变 12v控制电压校准
	
	int32_t Inv_IGBT_TEMP_Cal	;			//逆变 IGBT温度校准
	int32_t Inv_CPU_TEMP_Cal	;			//逆变 CPU温度校准

// - 2.升压AD校准
	int32_t PFC_BusVol_Cal ; 	//PFC 母线电压校准
	int32_t PFC_R_InCur_Cal	;	//PFC 输入U相电流校准	
	int32_t PFC_S_InCur_Cal	;	//PFC 输入V相电流校准		
	int32_t PFC_T_ICur_Cal	;	//PFC 输入W相电流校准
	
	int32_t PFC_R_InVol_Cal	;		//PFC 输入U相电压校准	
	int32_t PFC_S_InVol_Cal	;		//PFC 输入V相电压校准		
	int32_t PFC_T_InVol_Cal	;		//PFC 输入W相电压校准
	
	int32_t PFC_L1_Temp_Cal;			//PFC 电感1温度补偿
	int32_t PFC_L2_Temp_Cal;			//PFC 电感2温度补偿
	
	int32_t PFC_IPM_Cur_Cal;			//PFC 桥臂电流偿
	int32_t PFC_HeatSink_Temp_Cal;		//PFC 散热器风扇温度补偿
	int32_t PFC_CPU_Temp_Cal;			//PFC CPU芯片温度补偿
}BsmFrame83;

// - 4.设置逆变和升压  AD校准信息（0x84）
typedef struct
{
	// - 1.逆变AD校准
	int32_t Inv_BusVol_Cal ; 			//逆变 母线电压校准
	int32_t Inv_OutU_Cur_Cal	;		//逆变 输出U相电流校准	
	int32_t Inv_OutV_Cur_Cal	;		//逆变 输出V相电流校准		
	int32_t Inv_OutW_Cur_Cal	;		//逆变 输出W相电流校准
	
	int32_t Inv_0_10v_Cal	;	//逆变 外接0-10V调速校准
	int32_t Inv_4_20mA_Cal	;	//逆变 外接0-10V调速校准			
	int32_t Inv_5v_Cal	;		//逆变 5V校准
	int32_t Inv_12v_Cal	;		//逆变 12v控制电压校准
	
	int32_t Inv_IGBT_TEMP_Cal	;		//逆变 IGBT温度校准
	int32_t Inv_CPU_TEMP_Cal	;		//逆变 CPU温度校准

// - 2.升压AD校准
	int32_t PFC_BusVol_Cal ; 		//PFC 母线电压校准
	int32_t PFC_R_InCur_Cal	;		//PFC 输入U相电流校准	
	int32_t PFC_S_InCur_Cal	;		//PFC 输入V相电流校准		
	int32_t PFC_T_ICur_Cal	;		//PFC 输入W相电流校准
	
	int32_t PFC_R_InVol_Cal	;		//PFC 输入U相电压校准	
	int32_t PFC_S_InVol_Cal	;		//PFC 输入V相电压校准		
	int32_t PFC_T_InVol_Cal	;		//PFC 输入W相电压校准
	
	int32_t PFC_L1_Temp_Cal;				//PFC 电感1温度补偿
	int32_t PFC_L2_Temp_Cal;				//PFC 电感2温度补偿
	
	int32_t PFC_IPM_Cur_Cal;				//PFC 桥臂电流偿
	int32_t PFC_HeatSink_Temp_Cal;			//PFC 散热器风扇温度补偿
	int32_t PFC_CPU_Temp_Cal;				//PFC CPU芯片温度补偿
}BsmFrame84;


// - 5.获取逆变和升压  参数修改记录（0x85）
typedef struct
{
// - 1.逆变AD校准
	int32_t Inv_BusVol_Cal ; 		//逆变 母线电压校准
	int32_t Inv_OutU_Cur_Cal	;	//逆变 输出U相电流校准	
	int32_t Inv_OutV_Cur_Cal	;	//逆变 输出V相电流校准		
	int32_t Inv_OutW_Cur_Cal	;	//逆变 输出W相电流校准
	
	int32_t Inv_0_10v_Governor_Cal	;		//逆变 外接0-10V调速校准
	int32_t Inv_4_20mA_Governor_Cal	;		//逆变 外接0-10V调速校准	
	
	int32_t Inv_5v_Cal	;					//逆变 5V校准
	int32_t Inv_12v_ControlVol_Cal	;		//逆变 12v控制电压校准
	
	int32_t Inv_IGBT_TEMP_Cal	;			//逆变 IGBT温度校准
	int32_t Inv_CPU_TEMP_Cal	;			//逆变 CPU温度校准

// - 2.升压AD校准
	int32_t PFC_BusVol_Cal ; 		//PFC 母线电压校准
	int32_t PFC_R_InCur_Cal	;		//PFC 输入U相电流校准	
	int32_t PFC_S_InCur_Cal	;		//PFC 输入V相电流校准		
	int32_t PFC_T_ICur_Cal	;		//PFC 输入W相电流校准
	
	int32_t PFC_R_InVol_Cal	;		//PFC 输入U相电压校准	
	int32_t PFC_S_InVol_Cal	;		//PFC 输入V相电压校准		
	int32_t PFC_T_InVol_Cal	;		//PFC 输入W相电压校准
	
	int32_t PFC_L1_Temp_Cal;			//PFC 电感1温度补偿
	int32_t PFC_L2_Temp_Cal;			//PFC 电感2温度补偿
	
	int32_t PFC_IPM_Cur_Cal;			//PFC 桥臂电流偿
	int32_t PFC_HeatSink_Temp_Cal;		//PFC 散热器风扇温度补偿
	int32_t PFC_CPU_Temp_Cal;			//PFC CPU芯片温度补偿
	
// - 3.设置参数范围	
	uint16_t Bus_Undervol_Value;  		// - 逆变 母线欠压值设置（范围-5%，滞回+5V）≤240V
	uint16_t Bus_Overvoltage_1;       	// - 逆变 母线一级过压值设置（范围+5%，滞回-5V）≥800V
	uint16_t Bus_Overvoltage_2;      	// - 逆变 母线二级过压值设置（范围+5%，滞回-5V）≥860V  

	uint16_t PFC_R_Undervol_Value; 		// -升压 R相欠压值设置（范围-5%，滞回+10V）≤240V
	uint16_t PFC_S_Undervol_Value; 		// -升压 S相欠压值设置（范围-5%，滞回+10V）≤240V
	uint16_t PFC_T_Undervol_Value; 		// -升压 T相欠压值设置（范围-5%，滞回+10V）≤240V
	
	uint16_t PFC_R_Overvol_Value; 		// -升压R相过压值设置（范围+5%，滞回+10V）≥500V
	uint16_t PFC_S_Overvol_Value; 		// -升压S相过压值设置（范围+5%，滞回+10V）≥500V
	uint16_t PFC_T_OvervolValue; 		// -升压T相过压值设置（范围+5%，滞回+10V）≥500V
	
}BsmFrame85;


// - 6.获取逆变和升压  故障信息（0x86）
typedef struct
{
// - 1.序号和时间
	uint32_t Seq;
	date_t date;
// - 2.逆变故障	
	uint8_t Inv_AllFault_State[6];
	uint8_t Inv_DeadLock_Fault_State[4];
	uint8_t Inv_Fault_State;
// - 3.升压故障	
	uint8_t PFC_AllFault_State[8];
	uint8_t PFC_DeadLock_Fault_State[4];
	uint8_t PFC_Fault_State;
	
// - 4.电机运行参数状态	
	uint8_t Inv_MotorWorkMode;   	// 电机工作模式
	uint8_t Inv_MotorControlState; 	// 电机控制状态
	uint8_t Inv_LibCode;       		// 电机库代码
	uint16_t Inv_MotorEncoder; 		// 电机编码器
	uint16_t Inv_MotorFreqHz;  		// 电机频率Hz
	uint16_t Inv_MotorRotateSpeed; 	// 电机转速
	
	uint8_t  PFC_BoostState; 		// 升压 状态（0:停止 1:启动  2:预充电  3:软启动  4:运行  ）
	uint16_t PFC_BoosetPWM; 		// 升压 占空比(%)
// - 5.功率因数谐波等
	uint16_t Inv_VolOUT_Harmonic;  		//逆变 输出电压谐波
	uint16_t Inv_CurOUT_Harmonic;  		//逆变 输出电流谐波
	uint16_t Inv_Power_Factor;  		//逆变 输出功率因数
	
	uint16_t PFC_VolOUT_Harmonic;  		//升压 输出电压谐波
	uint16_t PFC_CurOUT_Harmonic;  		//升压 输出电流谐波
	uint16_t PFC_Power_Factor;  		//升压 输出功率因数
	
// - 6.逆变模拟量输出
	int32_t Inv_BusVol ; 		//逆变 母线电压
	int32_t Inv_OutU_Cur	;	//逆变 输出U相电流	
	int32_t Inv_OutV_Cur	;	//逆变 输出V相电流		
	int32_t Inv_OutW_Cur	;	//逆变 输出W相电流
	
	int32_t Inv_0_10v	;		//逆变 外接0-10V调速
	int32_t Inv_4_20m	;		//逆变 外接0-10V调速	
	
	int32_t Inv_5v	;			//逆变 5V
	int32_t Inv_12v	;			//逆变 12v控制电压
	
	int32_t Inv_IGBT_TEMP	;	//逆变 IGBT温度
	int32_t Inv_CPU_TEMP	;	//逆变 CPU温度
	
// - 7.升压模拟量
	int32_t PFC_BusVol_Cal ; 	//PFC 母线电压
	int32_t PFC_R_InCur	;		//PFC 输入U相电流	
	int32_t PFC_S_InCur	;		//PFC 输入V相电流		
	int32_t PFC_T_ICur	;		//PFC 输入W相电流
	
	int32_t PFC_R_InVol	;		//PFC 输入U相电压	
	int32_t PFC_S_InVol	;		//PFC 输入V相电压		
	int32_t PFC_T_InVol	;		//PFC 输入W相电压
	
	int32_t PFC_L1_Temp;		//PFC 电感1温度补偿
	int32_t PFC_L2_Temp;		//PFC 电感2温度补偿
	
	int32_t PFC_IPM_Cur;				//PFC 桥臂电流偿
	int32_t PFC_HeatSink_Temp_Cal;	    //PFC 散热器风扇温度补偿
	int32_t PFC_CPU_Temp;				//PFC CPU芯片温度补偿
	
// - 8.输入数字量	
// -| 0 Byte
	uint8_t HardwareVer1		: 1 ;
	uint8_t HardwareVer2		: 1 ;
	uint8_t HardwareVer3		: 1 ;

	uint8_t S11					: 1 ;
	uint8_t S22					: 1 ;
	
	uint8_t Addr1				: 1 ;
	uint8_t Addr2				: 1 ;
	
	uint8_t Res1				: 1 ;

// -| 1 Byte
	uint8_t In1  	: 1 ;
	uint8_t In2		: 1 ;
	uint8_t In3		: 1 ;	
	uint8_t In4		: 1 ;
	uint8_t Res0	: 4 ;
	
// - 9.硬件故障信号状态
	// -  其他故障反馈
	uint8_t Inv_Fan1_FLT   	: 1 ; 		// 逆变散热风扇1故障(1:正常 0:故障
	uint8_t Inv_Fan2_FLT		: 1 ; 	// 逆变散热风扇1故障(1:正常 0:故障
	
	uint8_t Inv_3Phase_N_FLT	: 1 ; 	// 逆变三相输出电流负半波硬件保护状态(1:正常 0:故障
	uint8_t Inv_3Phase_O_FLT	: 1 ; 	// 逆变三相输出电流正半波硬件保护状态(1:正常 0:故障
	
	uint8_t Inv_IGBT_FLT		: 1 ; 	// 逆变IGBT短路保护状态(1:正常 0:故障
		
	
	uint8_t Inv_FS_INV_FLT	: 1 ; 	//逆变检测逆变FS输出状态 (1:正常0:停机
	uint8_t Inv_IPM_FLT		: 1 ; 	//逆变电机驱动模块故障状态 (1:正常0:停机
	uint8_t Res2			: 1 ; 	//保留

// -  11.输出控制故障
	uint8_t Inv_O1_FLT 		: 1 ;  		//逆变输出1故障反馈 (1:故障 0:正常)
	uint8_t Inv_O2_FLT 		: 1 ;  		//逆变输出2故障反馈 (1:故障 0:正常)
	uint8_t Inv_O3_FLT 		: 1 ;  		//逆变输出3故障反馈 (1:故障 0:正常)
	uint8_t Res3			: 5 ; 		//保留
	
	uint8_t PFC_3Phase_N_FLT	: 1 ; 	// 升压 三相输出电流负半波硬件保护状态(1:正常 0:故障
	uint8_t PFC_3Phase_O_FLT	: 1 ; 	// 升压 三相输出电流正半波硬件保护状态(1:正常 0:故障
	uint8_t PFC_BusVol_FLT    	: 1 ;   // 升压 母线过压硬件保护状态
	uint8_t PFC_IPM_FLT    		: 1 ;   // 升压 桥臂短路硬件保护状态
	uint8_t PFC_BoostModule_FLT : 1 ;  	// 升压 模块故障状态
	uint8_t PFC_BoostFS_FLT    	: 1 ;   // 升压 _FS检测输出状态
	uint8_t Res4                : 2 ;
	
// - 12.输出控制信号
	uint16_t Inv_Fan_Pwm_OUT;  		//逆变 风扇PWM输出
	uint32_t Inv_DAC1_Vol_OUT; 		//逆变 DAC1电压输出
	uint32_t Inv_DAC2_Vol_OUT; 		//逆变 DAC2电压输出
	
	uint32_t PFC_DAC1_Vol_OUT; 		//PFC DAC1电压输出
	uint32_t PFC_DAC2_Vol_OUT; 		//PFC DAC2电压输出
	
	
	
	uint8_t Inv_MotorDrive_Enable 	: 1 ; //逆变 电机驱动使能（1:使能 0:关闭）（只读
	uint8_t Inv_FLT_Clear     		: 1 ; //逆变 电机驱动使能（1:使能 0:关闭）（只读
	uint8_t Inv_O1 	: 1 ;  //逆变 数字输出1控制
	uint8_t Inv_O2 	: 1 ;  //逆变 数字输出2控制
	uint8_t Inv_O3 	: 1 ;  //逆变 数字输出3控制
	uint8_t Res5    : 3 ;  //保留

	uint8_t Res6;
	
	uint8_t kmon2   : 1 ;  	//升压 主继电器控制KMON1 (1：断开 0：吸合
	uint8_t PFC_KMON2   : 1 ;  	//升压 主继电器控制KMON2 (未使用）
	uint8_t Res7        : 6 ;  	//保留
// - 13.功能预留口	
	uint8_t Inv_IO1_OUT  : 1 ;  		//逆变IO1状态（喂狗DO）
	uint8_t Inv_IO2_IN   : 1 ;  		//逆变IO2状态（喂狗DI）
	uint8_t Inv_IO3_OUT  : 1 ;  		//逆变IO3状态（升压硬线启动信号DO  1:启动  0:停机
	uint8_t Inv_IO4_OUT  : 1 ;  		//逆变IO4状态（升压硬线故障信号DO  1:正常 0:故障）
	uint8_t Inv_IO5      : 1 ;  		//逆变IO5状态（保留）
	uint8_t Res8	     : 3 ;			//预留
	
	uint8_t PFC_BoostIO1_IN   : 1 ;
	uint8_t PFC_BoostIO2_OUT  : 1 ;
	uint8_t PFC_BoostIO3_IN   : 1 ;
	uint8_t PFC_BoostIO4_IN   : 1 ;
	uint8_t PFC_BoostIO5   	: 1 ;
	uint8_t Res9;
// - 6.通讯状态	
	uint8_t Inv_CAN_COM_State  		: 1 ;  		//CAN通讯状态
	uint8_t Inv_485_COM_State  		: 1 ;  		//485通讯状态
	
	uint8_t Inv_Intranet_COM_State  	: 1 ;  		//逆变内网通讯状态
	uint8_t PFC_Intranet_COM_State  		: 1 ;  	//升压内网通讯状态
	
	uint8_t Inv_WIFI_COM_State  		: 1 ;  		//WiFi串口通信状态
	uint8_t Inv_WIFI_NET_State  		: 1 ;  		//WiFi网络通信状态
	uint8_t Res10						: 2 ;   	// 预留
// 7.变频器操作记录状态
	uint32_t Inv_PowerOnTime; 				//累计上电时间（H）
	uint32_t Inv_WorkTime;					//压缩机累计工作时间（H）
	uint32_t Inv_380VPowerOn_Cnt;			//累计380V上电次数（次）
	uint32_t Inv_380VPowerConsumption;		//累计380V消耗电量（KW.H）
	uint32_t Inv_FAN_WorkTime;             	//散热器风扇工作时间（H)
	
	uint32_t kmon2_Not_CurConnect_Cnt; 	//累计主继电器无流吸合次数（次）
	uint32_t kmon2_Not_CurBreak_Cnt;   	//累计主继电器无流断开次数（次）	
	uint32_t kmon2_Have_CurConnect_Cnt; 	//累计主继电器有流吸合次数（次）
	uint32_t kmon2_Have_CurBreak_Cnt;   	//累计主继电器有流断开次数（次）
	
	uint32_t PFC_KMON2_Not_CurConnect_Cnt; 	//累计预充电继电器无流吸合次数（次）
	uint32_t PFC_KMON2_Not_CurBreak_Cnt;   	//累计预充电继电器无流断开次数（次）					 
	uint32_t PFC_KMON2_Have_CurConnect_Cnt; 	//累计主继电器有流吸合次数（次）
	uint32_t PFC_KMON2_Have_CurBreak_Cnt;   	//累计主继电器有流断开次数（次）
	
	uint8_t Inv_Motor_Manufacturer;       	//电机厂家
	uint8_t Inv_Motor_Type;       			//电机类型
	uint8_t Inv_Motor_NumberofPoles;       	//电机级数
	uint8_t Inv_Motor_Dir;       			//电机转向
	
	uint8_t Can_X_Adrr;   //can x通讯地址
	uint8_t Can_Y_Adrr;   //can y通讯地址

}BsmFrame86;

typedef struct{
	date_t   date;
	uint8_t  Sys_State;
	
	uint16_t In_Power;
	int16_t  In_Freq;
	uint16_t In_Energy;
	
	uint16_t In_Vol_R;
	uint16_t In_Vol_S;
	uint16_t In_Vol_T;
	
	uint16_t In_PowerFactor;
	uint16_t In_VolHarmonic;
	uint16_t In_CurHarmonic;
	
	uint16_t In_Cur_R;
	uint16_t In_Cur_S;
	uint16_t In_Cur_T;
	
	uint16_t Temp_L1;
	uint16_t Temp_L2;
	
	uint16_t Duty_Pfc;
	uint16_t Temp_Pfc;
	
	uint16_t Duty_Inv;
	uint16_t Temp_IGBT;
	
	uint16_t Duty_Fan;
	
	uint16_t Out_PowerFactor;
	uint16_t Out_VoltHarmonic;
	uint16_t Out_CurrHarmonic;
	uint16_t Out_Freq;
	
	uint16_t Out_Cur_U;
	uint16_t Out_Cur_V;
	uint16_t Out_Cur_W;
	
	uint16_t Out_Vol_U;
	uint16_t Out_Vol_V;
	uint16_t Out_Vol_W;
	
	uint16_t Vol_Main;
	uint16_t Vol_12V;
	uint16_t Vol_5V;
	uint16_t Vol_Ai1;
	uint16_t Cur_Ai2;
	uint16_t Speed_Motor;
	
	uint8_t  Com_CAN		: 1 ;
	uint8_t  Com_485		: 1 ;
	uint8_t  Com_Inv		: 1 ;
	uint8_t  Com_Pfc		: 1 ;	
	uint8_t  Com_Wifi		: 1 ;
							
	uint8_t  Com_Net		: 1 ;
	uint8_t  Res0			: 2 ;
}BsmFrame88;
#pragma pack()  // 内存恢复编译器默认对齐方式

extern uint8_t bsm_com_port;
#ifdef __cplusplus
}
#endif

#endif
