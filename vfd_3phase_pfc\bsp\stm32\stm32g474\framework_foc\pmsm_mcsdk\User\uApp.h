
/***************************************************************************

Copyright (C), 1999-2012, BSM Tech. Co., Ltd.

* @file           uApp.h
* <AUTHOR> @version        V0.0.1
* @date           2012-02-25
* @brief          Ӧ�ò����ݼ�

History:          // Revision Records

       <Author>             <time>         <version >               <desc>

        grape             2012-02-25         V0.0.1                ���ν���

***************************************************************************/
#include "rtthread.h"
#include "stm32g4xx.h"
#include "stdlib.h"
#include "uLed.h"
#include "ukout.h"
#include "uaFullBridge.h"
#include "drv_gpio.h"
#include "stdbool.h"
#ifndef _DS_APP_H_
#define _DS_APP_H_

void UserApp_Tick(void);
void UserApp_Init(void);
void UserApp_Task(void);


//-------------------------------------------------------------
// App��汾��? 
//-------------------------------------------------------------
extern uint32_t _kernel_v;
#define UAPP_VERSION_U16    ((_kernel_v >> 0) & 0xFFFF)
#define UAPP_VERSION        ((_kernel_v >> 8) & 0xFF)
#define UAPP_SUBVERSION     ((_kernel_v >> 0) & 0xFF)

#define UAPP_SUB1_VER   ((_kernel_v >> 4) & 0xF)
#define UAPP_SUB2_VER   ((_kernel_v >> 0) & 0xF)

#define BSP_USE_IWDG    (1)         /* Start IWDG ,Timeout set 2s */


#define PROJ_NAME       "PMSM-03"
#define BOARD_NAME      "PMSM-03"
#define BOARD_VER       "H1.1"


#define PMSM03_TEST          /** remember disable this macro when release */   


#ifdef PMSM03_TEST

    //#define PMSM03_5V_DEBUG
    //#define USE_PMSM01        // define When Debug PMSM01
    #define PMSM03_TEST_OPENLOOP
    
    #define IN1_ENABLE_VAL  (0)
    #define IN1_ENABLE_MASK (0)
    #define IN2_SWITCH_VAL  (0) // 1:force open DCDC boost
    #define IN2_SWITCH_MASK (0)
    #define DISABLE_45MIN_STOP
#endif

#define USE_DCDC          
#define USE_MCSDK           
// s#define USE_MCSDK_UART    /* defined in mdk options->target->C/C++ -> Define \
                            define:                                         \
                            Debug MotorControl workbench GUI console        \
                            baudrate:380400                                 \
                            undefine:                                       \
                            enable to use Finsh/MSH or Freemaster           \
                            baudrate:380400                                 \
                            */

/* Definitions of environment analog values */
/* Value of analog reference voltage (Vref+), connected to analog voltage   */
/* supply Vdda (unit: mV).                                                  */
#define VDDA_APPLI                (3000UL)
#define DATA_TO_VOLTAGE(ad)       (__LL_ADC_CALC_DATA_TO_VOLTAGE(VDDA_APPLI, ad, LL_ADC_RESOLUTION_12B))
#define DATA_TO_VIRTUAL(ad, ref)  (__LL_ADC_CALC_DATA_TO_VOLTAGE(ref, ad, LL_ADC_RESOLUTION_12B))
//-------------------------------------------------------------
// �������״�?
//-------------------------------------------------------------
#define Mc_Idle   (0)  //������
#define Mc_Start  (1) //������
#define Mc_Run    (2)   //�������?
#define Mc_Stop   (3)  //���ͣ�?
#define Mc_Err    (4)   //������

#define SYS_NORMAL  (0) //����380Vģʽ
#define SYS_EMERG   (1)  //���ģ�?
#define SYS_STOP    (2)   //ͣ��ģʽ

#define PS_MODE_STOP        (0)
#define PS_MODE_AC380V      (1)
#define PS_MODE_EMERG       (2)

/*  IO Pin use RTT PIN device ,API follows:
    rt_pin_mode()   ��������ģʽ
    rt_pin_write()  �������ŵ�ƽ
    rt_pin_read()   ��ȡ���ŵ�ƽ
    rt_pin_attach_irq() �������жϻص�����
    rt_pin_irq_enable() ʹ�������ж�
    rt_pin_detach_irq() ���������жϻص�����
*/

#define DCDC_PWM_MAX    450
#define DCDC_PWM_MIN    2
#define DEAFULT_SPEED   600
#ifdef USE_PMSM01
    #define IN1_PIN     GET_PIN(D, 3)
    #define IN2_PIN     GET_PIN(D, 3)
    #define KMON_PIN    GET_PIN(D, 3)
#else
    #define IN1_PIN     GET_PIN(D, 1)
    #define IN2_PIN     GET_PIN(D, 2)
    #define KMON_PIN    GET_PIN(D, 0)
#endif

#define IN3_PIN     GET_PIN(D, 3)
#define O1_PIN      GET_PIN(D, 4)
#define FO1_PIN     GET_PIN(D, 5)
#define O2_PIN      GET_PIN(D, 6)
#define FO2_PIN     GET_PIN(C, 13)
#define O3_PIN      GET_PIN(E, 0)
#define FO3_PIN     GET_PIN(E, 1)

#define KMON1_PIN   GET_PIN(C, 8)
#define KMON2_PIN   GET_PIN(B, 6)
#define S1_PIN      GET_PIN(E, 2)
#define S2_PIN      GET_PIN(E, 3)
#define INOPP_PIN   GET_PIN(E, 4)
#define F_IPM_PIN   GET_PIN(E, 14)
#define F_IPM1_PIN  GET_PIN(A, 6)
#define BIin_FAULT_PIN  GET_PIN(A, 0)
#define Iin_FAULT_PIN   GET_PIN(A, 7)

#define PERCENT(x)              (x * 10)
#define Volt(x)                 (x * 10)
#define Amp(x)                  (x * 100)

#define MOTOR_STOP      (0)
#define MOTOR_POSITIVE  (1)
#define MOTOR_NEGATIVE  (2)
//------------------------------------------------------------------------------------------
// DCUϵͳ״̬��Ϣ
//------------------------------------------------------------------------------------------
typedef struct _PMSM_Sys_State_
{
    //--------------------------------------------------------------
    // PMSM������Ϣ
    //--------------------------------------------------------------
    /*Bety01-02*/ uint16_t ControlCurrent;  //���Ƶ���
    /*Bety03-04*/ int16_t ControlSpeed;    //����ת��
    /*Bety05-06*/ uint16_t ControlState;    //����״̬
    /*Bety03-04*/ int16_t ControlNewSpeed; //�µĿ���ת��
    /*Bety01-02*/ uint16_t ControlVbus;     //
    /*Bety03-04*/ uint16_t ControlIin;   //
    /*Bety03-04*/ int16_t  ControlDirection; 
    
    /*Bety01-02*/ uint16_t DcdcState : 8;             //
    /*Bety01-02*/ uint16_t DcdcFLT_FucnA : 1;         //
    /*Bety01-02*/ uint16_t DcdcFLT_FucnB : 1;         //
    /*Bety01-02*/ uint16_t DcdcFLT_FucnC : 1;         //
    /*Bety01-02*/ uint16_t DcdcFLT_Prio1 : 1;         //
    /*Bety01-02*/ uint16_t DcdcFLT_Prio2 : 1;         //
    /*Bety01-02*/ uint16_t DcdcFLT_Prio3 : 1;         //
    /*Bety01-02*/ uint16_t DcdcHardFaultStopFlag : 1; //
    /*Bety01-02*/ uint16_t DcdcTimeoutStopFlag   : 1; //

    /*Bety01-02*/ uint16_t CMD_MC_Stop : 1;         //
    /*Bety01-02*/ uint16_t CMD_MC_Start : 1;         //
                  uint16_t Sys_Fault_Stop   : 1;
                  uint16_t Sys_Fault_Clear  : 1;
                  uint16_t Sys_Fault_DeadLock   : 1;
		          uint16_t res: 14;

    /*Bety03-04*/ uint16_t DcdcPwm;        //
    /*Bety03-04*/ uint16_t SysState;       //
    //--------------------------------------------------------------
    // PMSM�ɼ�״̬��Ϣ
    //--------------------------------------------------------------
    /*Bety07-08*/ uint16_t InputVoltage;    //������?
    /*Bety07-08*/ uint16_t VbusVoltage;    //������?
    /*Bety07-08*/ uint16_t ACvinVoltage;   //������?
    /*Bety07-08*/ uint16_t VinbusVoltage;  //������?
    ///*Bety07-08*/ int16_t IinCurrent;      //�������?
	/*Bety07-08*/ uint16_t IinCurrent;   
    /*Bety07-08*/ int16_t BosstIinCurrent; //�������?
    /*Bety09-10*/ uint16_t MCState;        //�������״�?
    /*Bety11-12*/ int16_t Temperature;    //�¶Ȳɼ�
    /*Bety11-12*/ int16_t Temperature2;    //�¶Ȳɼ�
    /*Bety11-12*/ int16_t Temperature3;    //�¶Ȳɼ�
    /*Bety11-12*/ uint16_t VSR;             //����ɼ�?
    
    /*Bety13-14*/ uint16_t MotorCurrent;   //�ɼ�����
    /*Bety15-16*/ uint16_t  MotorSpeed;     //�ɼ�ת��
    /*Bety11-12*/ int16_t  Ia;              //�����?
    /*Bety11-12*/ int16_t  Ib;              //�����?
    /*Bety11-12*/ int16_t  Ic;              //�����?
		
    /*Bety11-12*/ int16_t  Iq;              //�?子电�?//////////////////////////////////////////////////////////
    /*Bety11-12*/ int16_t  Iqref;           //相电�?
    /*Bety11-12*/ int16_t  Id;              //励�?�电�?
    /*Bety11-12*/ int16_t  Idref;           //相电�?
    
    /*Bety17-18*/ uint16_t StateCode;      //״̬����
    /*Bety19-20*/ uint16_t NowFault;       //���ϱ���
		
		
    /*Bety17-18*/ uint16_t OutIpamp;      //������ֵ
    /*Bety17-18*/ uint16_t OutVoltage;      //���

    uint16_t OccurredFault;                //�������Ĺ���
    uint16_t MotorPower;                   //��﹦��?
		
    uint16_t ou_effective_vol;
    int16_t  T_cpu;
		
    /*Bety21-23*/ uint32_t MCstart_cnt;    //����������
    /*Bety21-23*/ uint32_t MCerr_cnt;      //����������
    /*Bety21-23*/ uint32_t MCworktime_cnt; //�������ʱ��?
	int16_t Temperature4;	
	uint8_t COMM_OK;
    
    uint8_t  power_supply_mode;
    //uint32_t power_on_time;
    
    uint8_t  DI_in_bits;
    uint8_t  DO_flt_bits;
    uint8_t  kmon_bits;
    uint8_t  ou_bits;
    
    uint8_t  speed_is_set;
    
    uint16_t ac380_act_times;
    uint16_t dc110_act_times;
    
    uint16_t kmon_act_times;
    uint16_t kmon1_act_times;
    uint16_t kmon2_act_times;
    
    uint32_t dirver_board_ver_in_file;
    uint32_t dirver_board_ver_in_flash;
    
    double total_consumpation;
    double ac380_consumpation;
    double dc110_consumpation;
    
  


} PMSM_Sys_State, *PPMSM_Sys_State;



//////////////////////////////////////////////////////////////////////////////////
typedef struct _PMSM_Para_Display_
{
	
	uint16_t V_out;
	uint16_t I_out;
	uint16_t M_speed;
	uint16_t Vbus_Vol;
	uint16_t ACvin;
	uint16_t DCvin;
}PMSM_Para_Display, *P_PMSM_Para_Display_;

typedef struct __f_io_ctr__
{
    uint16_t fo_5sdelay_cnt;
    uint8_t  fo_trig_times;
    uint8_t  ou_dis;
    uint16_t fo_10min_delay;
} _f_io_ctr_t;

#define DC_100V_LOW   (70)
#define DC_100V_HIGH  (147)
//#define DC_100V_HIGH  (130)
#define DC_ERROR      (5)
#define AC_380V_LOW   (240)
#define AC_380V_HIGH  (500)
#define AC_ERROR      (10)
//#define AC_ERROR      (10)
#define Iin_HIGH      (19)
#define BosstIin_HIGH1  (500)
#define BosstIin_HIGH2  (500)
#define Iin_ERROR       (10)
//------------------------------------------------------------------------------------------
// DCUϵͳ����״̬��Ϣ
//------------------------------------------------------------------------------------------
#define DIAG_CODE_LEN (32)
typedef struct _PMSM_Sys_Fault_
{
    /*Bety01*/
    /*   Bit 00*/ uint16_t FLT_MC_FOC_DURATION 		: 1;		//���Ƶ�ʹ���
    /*   Bit 01*/ uint16_t FLT_MC_OVER_VOLT 		: 1;		//ĸ��Ƿѹ
    /*   Bit 02*/ uint16_t FLT_MC_UNDER_VOLT 		: 1;		//ĸ��Ƿѹ
    /*   Bit 03*/ uint16_t FLT_MC_OVER_TEMP 		: 1;		//���ģ�����
    /*   Bit 04*/ uint16_t FLT_MC_START_UP 			: 1;		//�������ʧ��
    /*   Bit 05*/ uint16_t FLT_MC_SPEED_FDBK 		: 1;		//�����·
    /*   Bit 06*/ uint16_t FLT_MC_BREAK_IN 			: 1;		//�����·
    /*   Bit 07*/ uint16_t FLT_MC_SW_ERROR 			: 1;		//��������

    /*Bety02*/
    /*   Bit 00*/ uint16_t FLT_RECORD_ERR 			: 1;		//�洢����
    /*   Bit 01*/ uint16_t FLT_RECORD_FULL 			: 1;		//����
    /*   Bit 02*/ uint16_t FLT_ENCODER_ERR 			: 1;		//����
    /*   Bit 03*/ uint16_t FLT_COM_ERR 				: 1;		//ͨ�Ź���
    /*   Bit 04*/ uint16_t FLT_DCDC_OVER_Vbus 		: 1;		//ĸ�߹�ѹ
    /*   Bit 05*/ uint16_t FLT_DCDC_UNDER_Vbus 		: 1;		//����
    /*   Bit 06*/ uint16_t FLT_DCDC_OVER_Vinbus 	: 1;		//��ѹ�����ѹ
    /*   Bit 07*/ uint16_t FLT_DCDC_UNDER_Vinbus 	: 1;		//��ѹ����Ƿѹ

    /*Bety03*/
    /*   Bit 00*/ uint16_t FLT_DCDC_OVER_Iin 		: 1;		//��ѹ�������
    /*   Bit 01*/ uint16_t FLT_DCDC_OVER_BosstIin 	: 1;		//�������δ����
    /*   Bit 02*/ uint16_t FLT_F_Iin 				: 1;		//����
    /*   Bit 03*/ uint16_t FLT_F_IPM1 				: 1;		//��ѹģ�����
    /*   Bit 04*/ uint16_t FLT_F_IPM 				: 1;		//���ģ�����
    /*   Bit 05*/ uint16_t FLT_F_O1 				: 1;		//���1��·
    /*   Bit 06*/ uint16_t FLT_F_O2 				: 1;		//���2��·
    /*   Bit 07*/ uint16_t FLT_F_O3 				: 1;		//���3��·

    /*Bety04*/
    /*   Bit 00*/ uint16_t FLT_F_inOPP          	: 1;		//����ȱ��
    /*   Bit 01*/ uint16_t FLT_DCDC_OVER_ACvin  	: 1;		//���������ѹ
    /*   Bit 02*/ uint16_t FLT_DCDC_UNDER_ACvin 	: 1;		//��������Ƿѹ
    /*   Bit 03*/ uint16_t FLT_DCDC_SOFTSTART_ERROR : 1;		//��ѹ��������
    /*   Bit 04*/ uint16_t FLT_DCDC_Vinbus_Reverse 	: 1;		//����
    /*   Bit 05*/ uint16_t FLT_DCDC_Timeout     	: 1;		//����ͨ�糬ʱ
    /*   Bit 06*/ uint16_t FLT_Sensor 				: 1;		//����
    /*   Bit 07*/ uint16_t FLT_DCDC_OVER_Temp       : 1; 		//��ѹģ�����

} PMSM_Sys_Fault, *PPMSM_Sys_Fault;

#define PRI_1 1
#define PRI_2 2
#define PRI_3 3

#define FUNC_A 1    /* stop mechine */
#define FUNC_B 2    /* warning      */
#define FUNC_C 3    /* light        */

#define AC_IS_OK ((0 == PMSMFLT.FLT_DCDC_UNDER_ACvin) && (0 == PMSMFLT.FLT_DCDC_OVER_ACvin))
#define DC_IS_OK ((0 == PMSMFLT.FLT_DCDC_UNDER_Vinbus) && (0 == PMSMFLT.FLT_DCDC_OVER_Vinbus))

typedef struct
{
    uint16_t code;
    uint8_t priority;
    uint8_t func;
    char *str;
} DiagMsg;

typedef struct task_bit_control_state
{
    uint32_t sem_init : 1;
    uint32_t tick : 31;
} TaskState;

//------------------------------------------------------------------------------
//  AD_IN  ����ӳ��ӿ�?
//------------------------------------------------------------------------------
typedef struct _DS_AD_IN_
{
    uint16_t inVal01_AD; //ģ��������ͨ�� 01
    uint16_t inVal02_AD; //ģ��������ͨ�� 02
    uint16_t inVal03_AD; //ģ��������ͨ�� 03
    uint16_t inVal04_AD; //ģ��������ͨ�� 04
    uint16_t inVal05_AD; //ģ��������ͨ�� 05
    uint16_t inVal06_AD; //ģ��������ͨ�� 06
    uint16_t inVal07_AD; //ģ��������ͨ�� 07
    uint16_t inVal08_AD; //ģ��������ͨ�� 08
    uint16_t inVal09_AD; //ģ��������ͨ�� 09
    uint16_t inVal10_AD; //ģ��������ͨ�� 10
    uint16_t inVal11_AD; //ģ��������ͨ�� 11
    uint16_t inVal12_AD; //ģ��������ͨ�� 12
    uint16_t inVal13_AD; //ģ��������ͨ�� 13
    uint16_t inVal14_AD; //ģ��������ͨ�� 14
    uint16_t inVal15_AD; //ģ��������ͨ�� 15
    uint16_t inVal16_AD; //ģ��������ͨ�� 16
} DS_AD_IN, *PDS_AD_IN;
//------------------------------------------------------------------------------
//  AD_IN  ����ӳ��ӿ�?
//------------------------------------------------------------------------------
typedef struct _DS_AD_CA_
{
    int16_t inVal01_AD; //ģ��������ͨ�� 01
    int16_t inVal02_AD; //ģ��������ͨ�� 02
    int16_t inVal03_AD; //ģ��������ͨ�� 03
    int16_t inVal04_AD; //ģ��������ͨ�� 04
    int16_t inVal05_AD; //ģ��������ͨ�� 05
    int16_t inVal06_AD; //ģ��������ͨ�� 06
    int16_t inVal07_AD; //ģ��������ͨ�� 07
    int16_t inVal08_AD; //ģ��������ͨ�� 08
    int16_t inVal09_AD; //ģ��������ͨ�� 09
    int16_t inVal10_AD; //ģ��������ͨ�� 10
    int16_t inVal11_AD; //ģ��������ͨ�� 11
    int16_t inVal12_AD; //ģ��������ͨ�� 12
    int16_t inVal13_AD; //ģ��������ͨ�� 13
    int16_t inVal14_AD; //ģ��������ͨ�� 14
    int16_t inVal15_AD; //ģ��������ͨ�� 15
    int16_t inVal16_AD; //ģ��������ͨ�� 16
    uint16_t crc;
} DS_AD_CA, *PDS_AD_CA;

typedef struct _DS_AD
{
    uint16_t digital;
    uint16_t analog;
    uint16_t virtualvalue;
} DS_AD;

typedef struct _DS_AD_SAMPLE
{
    DS_AD uBosstIin_10mA;
    DS_AD uVINBUS_100mV;
    DS_AD uIin_10mA;
    DS_AD uVBUS_100mV;
    DS_AD uACvin_100mV;
    
    DS_AD u2T;
    DS_AD u3T;
    DS_AD uVSR;
	DS_AD uTS;
    DS_AD u4T;

} DS_AD_SAMPLE;

typedef struct
{
    uint8_t isVbusNormal        : 1;
    uint8_t isVbusReady         : 1;
    uint8_t isVinbusNormal      : 1;
    uint8_t isVinbusReady       : 1;
    uint8_t isAcVNormal         : 1;
    uint8_t isAcVReady          : 1;
    uint8_t isSysNormalReady    : 1;
    uint8_t isSysEmergReady     : 1;
    uint8_t isCutOffKmon        : 1;
    uint8_t isKmonReady         : 1;
    uint8_t isCutOffKmon1       : 1;
    uint8_t isKmon1Ready        : 1;
    uint8_t isMcRunReady        : 1;
    uint8_t isACvinPrechargeReady  : 1;
    uint8_t isMcStopReady        : 1;
    uint8_t isIn1DisableReady        : 1;
    uint8_t isIn1EnableReady        : 1;
} DS_Case;

#define OBJ_VBUS            (OBJ_ACVIN * 14 * 8 / 100)
#define OBJ_VINBUS          DC_100V_LOW
#define OBJ_ACVIN           AC_380V_LOW
typedef struct
{
    uint16_t objVbus;
    uint16_t objVinbus;
    uint16_t objACvin;
    uint16_t objPwmMax;
    uint16_t objPwmMin;
    uint16_t objIinHigh;
    uint32_t objDcdcTimeout;
} DS_Para;

typedef struct
{
    uint8_t bIN1_Mask   : 1;
    uint8_t bIN1_Val    : 1;
    uint8_t bIN2_Mask   : 1;
    uint8_t bIN2_Val    : 1;
    uint8_t bIN3_Mask   : 1;
    uint8_t bIN3_Val    : 1;

    uint8_t bFO1_Mask   : 1;
    uint8_t bFO1_Val    : 1;
    uint8_t bFO2_Mask   : 1;
    uint8_t bFO2_Val    : 1;
    uint8_t bFO3_Mask   : 1;
    uint8_t bFO3_Val    : 1;

    uint8_t bS1_Mask   : 1;
    uint8_t bS1_Val    : 1;
    uint8_t bS2_Mask   : 1;
    uint8_t bS2_Val    : 1;

    uint8_t bOU1_Mask   : 1;
    uint8_t bOU1_Val    : 1;
    uint8_t bOU2_Mask   : 1;
    uint8_t bOU2_Val    : 1;
    uint8_t bOU3_Mask   : 1;
    uint8_t bOU3_Val    : 1;

    uint8_t bKM_Mask   : 1;
    uint8_t bKM_Val    : 1;
    uint8_t bKM1_Mask   : 1;
    uint8_t bKM1_Val    : 1;
    uint8_t bKM2_Mask   : 1;
    uint8_t bKM2_Val    : 1;

} DS_Debug;



typedef struct
{
    uint32_t gsTick;
    uint32_t gsPeriod;
    uint32_t gsClearTick;
    int16_t  gsLast;
    int16_t  gsTempMaxSampleValue;
    int16_t  gsMaxSampleValue;
    int16_t  gsEffectiveValue;
    
} DS_Iabc;

typedef struct
{
    uint32_t pw_top;
    uint32_t fix_tick;
    uint32_t resume_tick;
    
    uint16_t fix_step;
    uint16_t resume_step;
    uint16_t cur_top;
    uint16_t cur_low;
    uint16_t volt_top;
    uint16_t volt_low;
    uint16_t temp_top;
    uint16_t temp_low;
    uint16_t back_lash;
    uint16_t ref_step;
    
    uint16_t invter_ot_flag      : 1;
    uint16_t dcdc_ot_flag        : 1;
    uint16_t dcdc_dc110_low_flag : 1;
    uint16_t dcdc_op_flag        : 1;
    uint16_t fix_active_flag     : 1;
    uint16_t ptu_stop_flag       : 1;
    uint16_t dcdc_oc_flag        : 1;

} DS_SpeedFix;

typedef struct
{
    uint8_t  isNormal            : 1;
    uint8_t  isTimeout           : 1;
    uint8_t  isReset             : 1;
    
    uint32_t crcErr;
    uint32_t irqCnter            ;
    uint32_t rxCnter             ;
    uint32_t ptuCnter            ;
    uint32_t tx2ptuCnter         ;
    uint32_t tickRx;
    uint32_t tickTx;
    uint32_t tickfromRxToTx;
    uint32_t tickfromRxToRx;
} DS_Comm;
/*=============================================================================*/
/*******************************************************************************
*                                                                              *
*                       �������ݼ�  (App Layer)                                *
*                                                                              *
*                                                                              *
********************************************************************************/
/*=============================================================================*/
#define Dig(name) (AD.u##name.digital)
#define Ana(name) (AD.u##name.analog)
#define Vir(name) (AD.u##name.virtualvalue)

#ifdef DAP_MACRO
PMSM_Sys_State PMSMST = {0};
PMSM_Sys_Fault PMSMFLT = {0};

PMSM_Para_Display PMSMST_PARA_DISPLAY = { 0 };
_f_io_ctr_t f_O1_ctrl = { 0 };
_f_io_ctr_t f_O2_ctrl = { 0 };
_f_io_ctr_t f_O3_ctrl = { 0 };

DS_Comm  rs485;
uint16_t sBosstIin_AD;
uint16_t sBosstIin_AD2;
uint16_t sIin_AD;
uint16_t sVINBUS_AD;

uint8_t bIN1_Enable;
uint8_t bIN2_Switch;
uint8_t bIN3_Pressure;
uint8_t bO1_Fault;
uint8_t bO2_AirValveOpen;
uint8_t bO3_AirValveClose;
uint8_t bFO1_Fault;
uint8_t bFO2_AirValveOpen;
uint8_t bFO3_AirValveClose;
uint8_t bS1_AddrBit0;
uint8_t bS2_AddrBit1;

uint8_t bKMON_Precharge;
uint8_t bKMON1_Precharge;
uint8_t bKMON2_Precharge;
uint8_t bINOPP_LosePase;
uint8_t bFIPM;
uint8_t bFIPM1;
uint8_t bBIin_Fault;
uint8_t bIin_Fault;

/*  */
uint8_t bIrq_INOPP_LosePase;
uint8_t bIrq_FIPM;
uint8_t bIrq_FIPM1;
uint8_t bIrq_BIin_Fault;
uint8_t bIrq_Iin_Fault;
uint8_t bIrq_FO1_Fault;
uint8_t bIrq_FO2_AirValveOpen;
uint8_t bIrq_FO3_AirValveClose;
uint16_t gsHardFaultStopSum;
DS_AD_IN ADIN = {0};
DS_AD_CA ADCA = {0};
DS_AD_SAMPLE AD = {0};
DS_Case _CS = {0};
DS_Para _Para = {0};
DS_Debug _Dbug = {0};
uint16_t diagCodeHs[5];
char _ulog_str[5][128] = {0};
DS_Iabc  MotorPhaseIa = {0};
DS_Iabc  MotorPhaseIb = {0};
DS_Iabc  MotorPhaseIc = {0};


DS_SpeedFix fan_cur_fix =
{
    .pw_top = 1430000,
    .back_lash = 100,
    .fix_step = 20,
    .resume_step = 20,
    .fix_tick = RT_TICK_PER_SECOND / 5,
    .resume_tick = 150,
    .cur_low = 750,
    .cur_top = 1550,
    .volt_top = 950,
    .volt_low = 800,
    .temp_top = 90,
    .temp_low = 85,
    .ref_step = 40	  //10s�ڼ��ٵ�1500ת

};
#else
extern void rs485_send_multiBytes(void *buff, int size);
extern DS_Comm  rs485;
extern PMSM_Para_Display PMSMST_PARA_DISPLAY;
extern _f_io_ctr_t f_O1_ctrl;
extern _f_io_ctr_t f_O2_ctrl;
extern _f_io_ctr_t f_O3_ctrl;
extern uint16_t sBosstIin_AD;
extern uint16_t sBosstIin_AD2;
extern PMSM_Sys_State PMSMST;
extern PMSM_Sys_Fault PMSMFLT;
extern uint8_t bIN1_Enable;
extern uint8_t bIN2_Switch;
extern uint8_t bIN3_Pressure;
extern uint8_t bO1_Fault;
extern uint8_t bO2_AirValveOpen;
extern uint8_t bO3_AirValveClose;
extern uint8_t bFO1_Fault;
extern uint8_t bFO2_AirValveOpen;
extern uint8_t bFO3_AirValveClose;
extern uint8_t bS1_AddrBit0;
extern uint8_t bS2_AddrBit1;
extern uint8_t bKMON_Precharge;
extern uint8_t bKMON1_Precharge;
extern uint8_t bKMON2_Precharge;
extern uint8_t bINOPP_LosePase;
extern uint8_t bFIPM;
extern uint8_t bFIPM1;
extern uint8_t bBIin_Fault;
extern uint8_t bIin_Fault;
extern uint16_t sIin_AD;
extern uint16_t sVINBUS_AD;
extern DS_AD_IN ADIN;
extern DS_AD_CA ADCA;
extern DS_AD_SAMPLE AD;
extern DS_Iabc  MotorPhaseIa;
extern DS_Iabc  MotorPhaseIb;
extern DS_Iabc  MotorPhaseIc;
extern uint8_t bIrq_INOPP_LosePase;
extern uint8_t bIrq_FIPM;
extern uint8_t bIrq_FIPM1;
extern uint8_t bIrq_BIin_Fault;
extern uint8_t bIrq_Iin_Fault;
extern uint8_t bIrq_FO1_Fault;
extern uint8_t bIrq_FO2_AirValveOpen;
extern uint8_t bIrq_FO3_AirValveClose;
extern DS_Case _CS;
extern DS_Para _Para;
extern DS_Debug _Dbug;
extern uint16_t gsHardFaultStopSum;

extern char _ulog_str[5][ULOG_LINE_BUF_SIZE];
extern uint16_t diagCodeHs[5];
extern int16_t ADC2_ValTemp[];
extern int16_t ADC3_ValTemp[];
extern int16_t ADC4_ValTemp[];
extern int16_t ADC5_ValTemp[];
extern int16_t ADC_AcValTemp[];
extern DS_SpeedFix fan_cur_fix;
#endif

extern uint32_t DCDC_Machine_Delay;

void User_SysCtrl(void);
void PMSM03_IN_Read(void);
void PMSM03_OUT_Write(void);
int sigs_connect_func(int (*func)(void), int time);
int sigs_scan_funs(uint32_t time);
uint8_t CreateDelay(const uint8_t bAddUp, uint32_t *const pwCnt, const uint32_t wLimit);
void DcdcSetMachine(rt_uint8_t state);
rt_uint8_t DcdcGetMachine(void);
void DcdcPwmStop(void);
void SML_Setup(void);
void SML_Loop(void);
int DcdcPwmOpenLoopDebug(void);
//unsigned short getCRC16(volatile unsigned char *ptr, unsigned char len);
unsigned short getCRC16(volatile unsigned char *ptr, uint16_t len);
void sys_read_adca(void);
void sys_write_adca(void);
void DcdcTimClearCounterPwm(void);
int User_MotorControl(char *ctrl_cmd, uint32_t param);
int User_DcdcControl(char *ctrl_cmd, uint32_t param);
int User_SysControl(char *ctrl_cmd, uint32_t param);
#endif

/******************* (C) COPYRIGHT 2012 Shenzhen BSM *****END OF FILE****/
