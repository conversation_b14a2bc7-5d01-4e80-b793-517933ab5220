//#############################################################################
// $TI Release: C2000Ware MotorControl SDK F28002x v3.00.00.01 $
// $Release Date: Mon Nov 30 14:24:55 CST 2020 $
// $Copyright:
//     Copyright (C) 2020 Texas Instruments Incorporated -
//     http://www.ti.com/ ALL RIGHTS RESERVED
// $
//#############################################################################

#ifndef _USER_H_
#define _USER_H_

#include <stdbool.h>
#include "math.h"
//!


// **************************************************************************
// the includes

// modules

//!
//!
//! \defgroup USER USER
//!
//@{


#ifdef __cplusplus
extern "C" {
#endif

#ifndef float32_t
typedef float float32_t;
#endif

// **************************************************************************
// the defines

//! \brief Defines the nominal DC bus voltage, V
//!
#define USER_NOMINAL_DC_BUS_VOLTAGE_V          ((float32_t)(650.0f))


//! \brief Defines the maximum voltage at the AD converter 3.3V Vref
//!
// Full scale voltage of AD converter, not the current voltage
#define USER_ADC_FULL_SCALE_ACIN_VOLTAGE_V         ((float32_t)(1636.0f))
#define USER_ADC_FULL_SCALE_DCBUS_VOLTAGE_V         ((float32_t)(926.5f))

//! \brief Defines the maximum current at the AD converter
//!
#define USER_ADC_FULL_SCALE_CURRENT_A         ((float32_t)(49.20f))


//! \brief ADC current offsets for A, B, and C phases
#define   IA_OFFSET_A    (23.2301521f)  // ~=0.5*USER_ADC_FULL_SCALE_CURRENT_A     
#define   IB_OFFSET_A    (23.250845f)  // ~=0.5*USER_ADC_FULL_SCALE_CURRENT_A
#define   IC_OFFSET_A    (23.2811012f)  // ~=0.5*USER_ADC_FULL_SCALE_CURRENT_A

//! \brief ADC voltage offsets for A, B, and C phases
#define   VA_OFFSET_V    (790.959656f)   // ~=0.5*USER_ADC_FULL_SCALE_ACIN_VOLTAGE_V
#define   VB_OFFSET_V    (790.322632f)   // ~=0.5*USER_ADC_FULL_SCALE_ACIN_VOLTAGE_V
#define   VC_OFFSET_V    (790.703735f)   // ~=0.5*USER_ADC_FULL_SCALE_ACIN_VOLTAGE_V


//! \brief Vbus used to calculate the voltage offsets A, B, and C
//!
// =0.5*USER_NOMINAL_DC_BUS_VOLTAGE_VC
#define   VBUS_OFFSET_V  (0.0*USER_ADC_FULL_SCALE_VOLTAGE_V)


//! \brief Defines the maximum negative current to be applied in Id reference
//!
#define USER_MAX_NEGATIVE_ID_REF_CURRENT_A    ((float32_t)(-2.0f))


//! \brief Defines the number of pwm clock ticks per isr clock tick
//!        Note: Valid values are 1, 2 or 3 only
#define USER_NUM_PWM_TICKS_PER_ISR_TICK         (1)

//! \brief Defines the number of ISR clock ticks per current controller clock tick
//!
#define USER_NUM_ISR_TICKS_PER_CURRENT_TICK     (1)


//! \brief Defines the number of ISR clock ticks per speed controller clock tick
//!
#define USER_NUM_ISR_TICKS_PER_SPEED_TICK      (10)


//! \brief Defines the number of current sensors
//!
#define USER_NUM_CURRENT_SENSORS               (3)


//! \brief Defines the number of voltage sensors
//!
#define USER_NUM_VOLTAGE_SENSORS               (0)

//! \brief Defines the system maximum input frequency, MHz
//!
#define USER_MAXIMUM_SCALE_FREQ_Hz      ((float32_t)(1000.0f))

//! \brief Defines the system clock frequency, MHz
//!
#define USER_SYSTEM_FREQ_MHz            ((float32_t)(170.0f))


//! \brief Defines the Pulse Width Modulation (PWM) frequency, kHz
//!
#define USER_PFC_PWM_FREQ_Hz          ((float32_t)(10000.0f))              //15KHz PWM Frequnecy
#define USER_PWM_FREQ_kHz             ((float32_t)USER_PFC_PWM_FREQ_Hz/1000.0f)

//! \brief Defines the Pulse Width Modulation (PWM) period, usec
//!
#define USER_PWM_PERIOD_USEC       ((float32_t)1000.0f/USER_PWM_FREQ_kHz)
#define USER_PFC_PWM_TIM             (USER_SYSTEM_FREQ_MHz*1000.0f/USER_PWM_FREQ_kHz)
#define USER_PFC_PWM_ARR             (USER_PFC_PWM_TIM/2)
//! \brief Defines the Interrupt Service Routine (ISR) frequency, Hz
//!
#define USER_ISR_FREQ_Hz           (USER_PWM_FREQ_kHz * (float32_t)1000.0f / (float32_t)USER_NUM_PWM_TICKS_PER_ISR_TICK)


//! \brief Defines the Interrupt Service Routine (ISR) period, usec
//!
#define USER_ISR_PERIOD_USEC       (USER_PWM_PERIOD_USEC * (float32_t)USER_NUM_PWM_TICKS_PER_ISR_TICK)


//! \brief Defines the timer frequency for controller, Hz
//!
#define USER_CTRL_FREQ_Hz        (USER_ISR_FREQ_Hz)


//! \brief Defines the controller execution period, usec
//!
#define USER_CTRL_PERIOD_USEC   (USER_ISR_PERIOD_USEC)


//! \brief Defines the controller execution period, sec
//!
#define USER_CTRL_PERIOD_SEC    ((float32_t)USER_CTRL_PERIOD_USEC/(float32_t)1000000.0)

//! \brief Defines the timer frequency for trajectory, Hz
//!
#define USER_TRAJ_FREQ_Hz       (USER_ISR_FREQ_Hz)


//! \brief Defines the direct voltage (Vd) scale factor
//!
#define USER_VD_SF                 ((float32_t)(0.95f))


//! \brief Defines the voltage scale factor for the system
//!
#define USER_VOLTAGE_SF               (USER_ADC_FULL_SCALE_VOLTAGE_V / (float32_t)4096.0f)            // 12 bit ADC, 2^12 = 4096
#define USER_ACIN_VOLTAGE_SF          (USER_ADC_FULL_SCALE_ACIN_VOLTAGE_V / (float32_t)4096.0f)            // 12 bit ADC, 2^12 = 4096
#define USER_DCBUS_VOLTAGE_SF         (USER_ADC_FULL_SCALE_DCBUS_VOLTAGE_V / (float32_t)4096.0f)      // 12 bit ADC, 2^12 = 4096

//! \brief Defines the current scale factor for the system
//!
#define USER_CURRENT_SF               (USER_ADC_FULL_SCALE_CURRENT_A / (float32_t)4096.0f)    // 12 bit ADC, 2^12 = 4096


//! \brief Defines the pole location for the DC bus filter, rad/sec
//!
#define USER_DCBUS_POLE_rps            ((float32_t)(100.0f))


//! \brief Defines the pole location for the voltage and current offset estimation, rad/s
//!
#define USER_OFFSET_POLE_rps            ((float32_t)(20.0f))


//! \brief Defines the pole location for the speed control filter, rad/sec
//!
#define USER_SPEED_POLE_rps        ((float32_t)(100.0f))


//! \brief Defines the analog voltage filter pole location, rad/s
//!
#define USER_VOLTAGE_FILTER_POLE_Hz     ((float32_t)(100.0f))
#define USER_VOLTAGE_FILTER_POLE_rps  (MATH_TWO_PI * USER_VOLTAGE_FILTER_POLE_Hz)


//! \brief Defines the maximum Vs magnitude in per units allowed
//!        This value sets the maximum magnitude for the output of the Id and
//!        Iq PI current controllers. The Id and Iq current controller outputs
//!        are Vd and Vq. The relationship between Vs, Vd, and Vq is:
//!        Vs = sqrt(Vd^2 + Vq^2).  In this FOC controller, the Vd value is set
//!        equal to USER_MAX_VS_MAG*USER_VD_MAG_FACTOR.
//!        so the Vq value is set equal to sqrt(USER_MAX_VS_MAG^2 - Vd^2).
//!
//!        Set USER_MAX_VS_MAG = 0.5 for a pure sinewave with a peak at
//!        SQRT(3)/2 = 86.6% duty cycle.  No current reconstruction
//!        is needed for this scenario.
//!
//!        Set USER_MAX_VS_MAG = 1/SQRT(3) = 0.5774 for a pure sinewave
//!        with a peak at 100% duty cycle.  Current reconstruction
//!        will be needed for this scenario (Lab08).
//!
//!        Set USER_MAX_VS_MAG = 2/3 = 0.6666 to create a trapezoidal
//!        voltage waveform.  Current reconstruction will be needed
//!        for this scenario (Lab08).
//!
//!        For space vector over-modulation, see lab08 for details on
//!        system requirements that will allow the SVM generator to
//!        go all the way to trapezoidal.
//!
//#define USER_MAX_VS_MAG_PU            (0.66f)
//#define USER_MAX_VS_MAG_PU            (0.57f)
#define USER_MAX_VS_MAG_PU              (0.53f)


//! \brief Defines the reference Vs magnitude in per units allowed
//! \      Set the value equal from 0.5 to 0.95 of the maximum Vs magnitude
#define USER_VS_REF_MAG_PU              ((float32_t)(0.8f) * USER_MAX_VS_MAG_PU)


//! \brief Defines the R/L excitation frequency, Hz
//!
#define USER_R_OVER_L_EXC_FREQ_Hz  ((float32_t)(300.0f))


//! \brief Defines the R/L Kp scale factor, pu
//! \brief Kp used during R/L is USER_R_OVER_L_KP_SF * USER_NOMINAL_DC_BUS_VOLTAGE_V / USER_MOTOR_MAX_CURRENT_A;
//!
#define USER_R_OVER_L_KP_SF        ((float32_t)(0.02f))


//! \brief Defines maximum acceleration for the estimation speed profiles, Hz/sec
//!
#define USER_MAX_ACCEL_Hzps        ((float32_t)(400.0f))
#define USER_MAX_ACCEL_STARTUP_Hzps ((float32_t)(50.0f))

//! \brief Defines the IdRated delta to use during estimation
//!
#define USER_IDRATED_DELTA_A       ((float32_t)(0.0001f))


//! \brief Defines the forced angle frequency, Hz
//!
#define USER_STARTUP_ANGLE_FREQ_Hz            ((float32_t)(5.0f))



//! \brief Defines the maximum current slope for Id trajectory
//!
#define USER_MAX_CURRENT_DELTA_A        (USER_MOTOR_RES_EST_CURRENT_A / USER_ISR_FREQ_Hz)


//! \brief Defines the maximum current slope for Id trajectory during power warp mode
//!
#define USER_MAX_CURRENT_DELTA_PW_A    (0.3 * USER_MOTOR_RES_EST_CURRENT_A / USER_ISR_FREQ_Hz)


/* CORDIC coprocessor configuration register settings */

/* CORDIC FUNCTION: PHASE q1.31 (Electrical Angle computation) */
#define CORDIC_CONFIG_PHASE     (LL_CORDIC_FUNCTION_PHASE | LL_CORDIC_PRECISION_6CYCLES | LL_CORDIC_SCALE_0 |\
                 LL_CORDIC_NBWRITE_2 | LL_CORDIC_NBREAD_1 |\
                 LL_CORDIC_INSIZE_32BITS | LL_CORDIC_OUTSIZE_32BITS)

/* CORDIC FUNCTION: SQUAREROOT q1.31 */
#define CORDIC_CONFIG_SQRT      (LL_CORDIC_FUNCTION_SQUAREROOT | LL_CORDIC_PRECISION_6CYCLES | LL_CORDIC_SCALE_1 |\
                 LL_CORDIC_NBWRITE_1 | LL_CORDIC_NBREAD_1 |\
                 LL_CORDIC_INSIZE_32BITS | LL_CORDIC_OUTSIZE_32BITS)

/* CORDIC FUNCTION: COSINE q1.15 */
#define CORDIC_CONFIG_COSINE    (LL_CORDIC_FUNCTION_COSINE | LL_CORDIC_PRECISION_6CYCLES | LL_CORDIC_SCALE_0 |\
                 LL_CORDIC_NBWRITE_1 | LL_CORDIC_NBREAD_1 |\
                 LL_CORDIC_INSIZE_16BITS | LL_CORDIC_OUTSIZE_16BITS)


#ifdef __cplusplus
}
#endif // extern "C"

//@}  // ingroup
#endif // end of _USER_H_ definition

