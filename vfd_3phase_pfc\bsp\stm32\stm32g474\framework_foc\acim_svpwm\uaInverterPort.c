
/******************** (C) COPYRIGHT 2022 BSM ********************
* File Name          : uaInverterPort.c
* Author             : 
* Version            : V1.0
* Date               : 
* Description        : 
********************************************************************************/
#include <rtthread.h>
#include <stm32g4xx.h>
#include "uaInverter.h"
#include "uapp.h"
#include "uaInverterPort.h"

static uint8_t  push_set_flag = 0;
static float    push_set_freq = 0;
float Inv_GetFreqRef(void)
{
    return EPSET.System_work_ferq;
}

float Inv_GetFreqNow(void)
{
    if(EPST.SysState  == SYS_InvRun)
        return motor.FreqOut;
    else
        return 0;
}

float Inv_GetFreqAllow(void)
{
    return motor.Freq_Allow;
}

float Inv_GetPushFreqRef(void)
{
    if(push_set_flag)
    {
        return push_set_freq;
    }
    
    return 0;
}

void Inv_PushSetFreqRef(float ref)
{
    static float prev_ref = 0;
    
    if(push_set_flag == 0)
    {
        push_set_flag = 1;
        push_set_freq = EPSET.System_work_ferq;
    }
    
    if(abs(prev_ref-ref) >= 5)
    {
        prev_ref = ref;
        record_logdata_push(LOG_SetFreq,(uint8_t)ref);
    }    
    
    EPSET.System_work_ferq = ref;
}

void Inv_PopSetFreqRef(void)
{
    static float prev_ref = 0;
    
    if(push_set_flag)
    {
        push_set_flag = 0;
        Inv_SetFreqRef(push_set_freq);
    }
}

void Inv_SetFreqRef(float ref)
{
    static float prev_ref = 5;
    
    if(ref > 200)
        return;
    
    push_set_flag = 0;
    
    if(abs(prev_ref-ref) >= 5)
    {
        prev_ref = ref;
        record_logdata_push(LOG_SetFreq,(uint8_t)ref);
    }    
    
    prev_ref = ref;
    EPSET.System_work_ferq = ref;
}

void Inv_FreqRampStop(uint8_t bit)
{
    motor.freq_ramp_stop |= 1<<bit;
}


void Inv_FreqRampStart(uint8_t bit)
{
    motor.freq_ramp_stop &= ~(1<<bit);
}
