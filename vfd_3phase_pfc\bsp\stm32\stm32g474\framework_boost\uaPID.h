
/******************** (C) COPYRIGHT 2022 bus-lan ********************
* File Name          : uaPID.h
* Author             :
* Version            : V1.0
* Date               :
* Description        :
********************************************************************************/
#ifndef __UAP_PID_H__
#define __UAP_PID_H__

#include "stm32g4xx.h"
#include "rthw.h"
#include <string.h>

//PID�������ݿ�
typedef struct _uap_pid_cb_
{
    int32_t  SetValue;          // Ŀ���趨ֵ
    int32_t  FeedbackValue;     // ����ֵ
    int32_t  Err[3];            // 0��ǰ��1�ϴ���2���ϴ����
    int32_t  CtrlKp;            // ������
    int32_t  CtrlKi;            // ������
    int32_t  CtrlKiMax;         // ������
    int32_t  CtrlKd;            // ΢����
    int32_t  CtrlVar;           // ��������
    int32_t  CtrlOut;           // ���������

    int32_t  ErrMin;            // ��Сƫ����ڼ�����

    int32_t  CtrlOutMax;        // ��������
    int32_t  CtrlOutMin;        // ��С������
    int32_t  Kp;                // ����ϵ��
    int32_t  Ki;                // ����ϵ��
    int32_t  Kd;                // ΢��ϵ��
    int32_t  Kdiv;              // ���ֳ˷�ϵ��

    uint32_t calCounts;         // PID��������������
} PID_CB, *P_PID_CB;

void uaPID_Ctrl(P_PID_CB pid);
#endif


/******************* (C) COPYRIGHT 2021 *****END OF FILE****/


