
/******************** (C) COPYRIGHT 2022 bus-lan ********************
* File Name          : Bsp_boost_pwm.c
* Author             :
* Version            : V1.0
* Date               :
* Description        :
********************************************************************************/

#ifndef __BSP_BOOST_PWM_H__
#define __BSP_BOOST_PWM_H__

#include <stm32g4xx.h>

//-----------------------------------------------------------------------
// Ƶ�� : 50K  perio 850
// Ƶ�� : 30K  perio 1417
// Ƶ�� : 80K  perio 531

//-----------------------------------------------------------------------
//#define PWM_PERIOD                (1417)/**< 30K*/
//#define PWM_PERIOD                 (1062) /**< 40K*/
#define PWM_PERIOD                (850) /**< 50K*/
//#define PWM_PERIOD                (513) /**< 80K*/
//#define PWM_PERIOD                (425) /**< 100K*/

#define PWM_PRESCALER             (2)





void System_hw_pwmInit(void);
void DcdcPwmStart(void);
void DcdcPwmStop(void);
void DcdcPwmSetCompare(int pwm);

#endif

/******************* (C) COPYRIGHT 2012 Shenzhen TY *****END OF FILE****/
