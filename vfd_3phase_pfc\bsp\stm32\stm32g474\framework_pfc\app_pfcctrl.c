
/***************************************************************************

Copyright (C), 2022-2022, BSM Tech. Co., Ltd.

* @file           app_pfcctrl.c
* <AUTHOR> @version        V0.0.1
* @date           2022-09-06
* @brief

History:          // Revision Records

       <Author>             <time>         <version >               <desc>



***************************************************************************/

//--------------------------------------------------------------------------
//
//--------------------------------------------------------------------------
#define  PFCCTRL_MACRO
#include "uapp.h"
#include "math.h"
#include "pfc_dqctrl.h"
#include "pfc_sdpll.h"
#include "app_pfcctrl.h"
#define DBG_SECTION_NAME               "pfc"
#define DBG_LEVEL                      DBG_INFO
#include <rtdbg.h>

void App_PFCCtrl(void);
void App_PFCGetData(void);

uint16_t pfc_adc_value[8];

extern ADC_HandleTypeDef hadc4;
extern TIM_HandleTypeDef htim8;

static rt_err_t pfc_init(void);
static rt_err_t pfc_start(void);
static rt_err_t pfc_stop(void);
static rt_err_t pfc_control(int cmd, void *arg);
static rt_err_t pfc_machine_task(void);

static const struct rt_pfc_ops ops =
{
    .init   = pfc_init,
    .start  = pfc_start,
    .stop   = pfc_stop,
    .machine_task = pfc_machine_task,
    .control = pfc_control,
};

rt_pfc_t    pfc =
{
    .ops = &ops,
    .PFCVAR = &PFCVAR,
    .DPLL3P = &DPLL3P,
};

static rt_err_t pfc_init(void)
{
    static uint8_t first = 1;
    pfc.CMD_PFC_Stop = 1;
    
    if(first)
    {
        first = 0;
        Pfc_PWMInit();
        MX_TIM8_Pfc_Init();

    }
    
    InitSdpll3P();
    InitDqctrl();
    pfc.ControlState = Pfc_Stop;


    
    pfc.init_flag = 1;
    return 0;
}
static rt_err_t pfc_start(void)
{
    if (!pfc.init_flag)
        pfc.ops->init();

    pfc.CMD_PFC_Stop = 0;

    return 0;
}

static rt_err_t pfc_stop(void)
{
    if (!pfc.init_flag)
        pfc.ops->init();

    if(pfc.CMD_PFC_Stop == 0)
        PfcDisPwm();
    
    pfc.PFC_Start  = 0;
    pfc.PFC_Runing = 0;
    pfc.CMD_PFC_Stop = 1;

    return 0;
}

static rt_err_t pfc_control(int cmd, void *arg)
{
    if (!pfc.init_flag)
        pfc.ops->init();

    switch (cmd)
    {
    case PFC_CMD_SET_VOLTREF:
        SetSdpll3P_Vref((uint32_t)arg);
        break;

    case PFC_CMD_SET_CURREF:
    {
        float ref = *(float *)arg;
        pfc.debug_iref = ref;
    }
        break;

    case PFC_CMD_GET_STATE:
        *(uint8_t *)arg = boost.state;
        break;

    case PFC_CMD_INIT_TIM8:
        MX_TIM8_Pfc_Init();
        break;
    
    default:
        break;


    }

    return 0;
}


/*******************************************************************************
* Function Name  : void pfc_control_task(void)
* Description    : PFC controll task
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static rt_err_t pfc_machine_task(void)
{
    static uint32_t tick = 0;
    
    if(!pfc.init_flag)
    {
        pfc.ops->init();
        return 0;
    }
    
    tick++;
    
    if(tick % 10 == 0)
    {
        App_PFCGetData();
        computeDF22_PRcontrollerCoeff(&PFCVAR.PR6coeff , PFCVAR.PR6para.kpc, PFCVAR.PR6para.krc, 50 * 6,  DPLL3P.SysPar.Fs,  PFCVAR.PR6para.wrc);
        computeDF22_PRcontrollerCoeff(&PFCVAR.PR12coeff, PFCVAR.PR6para.kpc, PFCVAR.PR6para.krc, 50 * 12, DPLL3P.SysPar.Fs,  PFCVAR.PR6para.wrc);
        computeDF22_PRcontrollerCoeff(&PFCVAR.PR18coeff, PFCVAR.PR6para.kpc, PFCVAR.PR6para.krc, 50 * 18, DPLL3P.SysPar.Fs,  PFCVAR.PR6para.wrc);
        App_PFCCtrl();
    }

    return 0;
}

const char *(pfc_state[]) = {
    "idle ",
    "start",
    "run  ",
    "stop ",
    "err  ",
};

int vfd_get_pfc_breakflag(void)
{
    uint8_t ipm_irq_flag =  
                    (!DIO_READ_BIT(F_IPM_PFC_PIN)          || (DIO_IRQ_DELAY(F_IPM_PFC_PIN) > 0))         || 
                    (vfd.ctrl.start_pfc && (!DIO_READ_BIT(OVP_P_BUS_PIN)  || (DIO_IRQ_DELAY(OVP_P_BUS_PIN) > 0)))   ||
                    (!DIO_READ_BIT(ICP_N_PIN)              || (DIO_IRQ_DELAY(ICP_N_PIN) > 0))             ||
                    (!DIO_READ_BIT(ICP_P_PIN)              || (DIO_IRQ_DELAY(ICP_P_PIN) > 0))             ||
                    LL_TIM_IsActiveFlag_BRK(TIM8)          || LL_TIM_IsActiveFlag_BRK2(TIM8);

    return ipm_irq_flag;
}

/*******************************************************************************
* Function Name  : void App_PFCCtrl(void)
* Description    : motor speed ctrl
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void App_PFCCtrl(void)
{
    static uint32_t err_cnt = 0;
    static uint8_t last_st = 0xff;
    static uint8_t  pfc_start_cmd = 0;
    static uint8_t  pfc_stop_cmd = 1;
    static uint16_t pfc_start_cnt = 0;
    static uint16_t pfc_stop_cnt = 0;
    static uint8_t last_ipm = 0;
    static uint8_t last_fs = 0;
    static uint8_t last_run = 0;

    if ((pfc.CMD_PFC_Stop == 1) 
        || (rt_tick_get() <= RT_TICK_PER_SECOND*5)
        #ifndef DEBUG_PFC_INVERT_MODE
        || (pfc.AbsFreq < 40.1f) || (pfc.AbsFreq > 63.9f)
        #endif
    )
    {
        pfc_start_cnt = 0;
        if (pfc_stop_cnt < 10) //100ms
        {
            pfc_stop_cnt++;
        }
        else
        {
            pfc_stop_cnt = 0;
            if (pfc_start_cmd )
            {
                if((pfc.AbsFreq < 40.1f) || (pfc.AbsFreq > 63.9f))
                    LOG_I(" freq: %d to stop , vbus: %d ", (int)pfc.Freq, (int)pfc.VbusVoltage);
                else if(pfc.CMD_PFC_Stop == 1)
                    LOG_I(" CMD_PFC_Stop 1 to stop , vbus: %d ", (int)pfc.VbusVoltage);
            }

            pfc_start_cmd = 0;
            pfc.CMD_PFC_Stop = 1;
            pfc_stop_cmd = 1;
        }
    }
    #ifndef DEBUG_PFC_INVERT_MODE
    else if ((pfc.AbsFreq > 46.7f) && (pfc.AbsFreq < 63.3f))
    #else
    else
    #endif
    {
        pfc_stop_cnt = 0;
        if (pfc_start_cnt < 100) //1s
        {
            pfc_start_cnt++;
        }
        else
        {
            pfc_start_cmd = 1;
            pfc_stop_cmd = 0;
        }
    }


    if(vfd_get_pfc_breakflag())
    {
        if(pfc.ControlState != Pfc_Err)
            vfd.diag_latch = 0x2;
        err_cnt++;
        pfc.ControlState = Pfc_Err;
    }
    
    if (last_st != pfc.ControlState)
    {
        LOG_I("  state:%d BK%d %s  cpu:%f ", pfc.ControlState,hardware_irq_pin.f_tim8_breakin_flag,pfc_state[pfc.ControlState],pfc.cpu);
        last_st = pfc.ControlState;
        pfc.state_machine_cnt = 0;
        pfc.worktime_cnt = 0;
    }

    switch (pfc.ControlState)
    {
    //
    case Pfc_Idle:
        pfc.ControlState = Pfc_Stop;

        break;

    case Pfc_Start:

        if (pfc_stop_cmd == 1)
        {
            pfc.ControlState = Pfc_Stop;
        }
        else
        {
            PFC_KMON_OUT(1);
            PFC_FS_OUT(1);    
            /* clear fault pin before start */
            if(pfc.state_machine_cnt == 0)
            {
                LL_TIM_ClearFlag_BRK2(TIM8);
                LL_TIM_ClearFlag_BRK(TIM8);
            }
            
            if(pfc.state_machine_cnt > 50)
            {
                pfc.PFC_Start = 1;
                pfc.ControlState = Pfc_Run;
            }
        }

        break;

    case Pfc_Run:
        
        pfc.worktime_cnt++;
        PFC_FS_OUT(1);   
    
        if((pfc_stop_cmd == 1) || (pfc.PFC_Start == 0))
        {
            pfc.ControlState = Pfc_Stop;
        }
        
        break;

    case Pfc_Stop:

        PFC_FS_OUT(0);
        PFCVAR.PIVbus.Ref  = 0;
        pfc.PFC_Start  = 0;
        pfc.PFC_Runing = 0;
    
        if ((pfc_start_cmd == 1) && (pfc.state_machine_cnt > 50))
        {
            PFC_KMON_OUT(0);
            pfc.ControlState = Pfc_Start;
            PFCVAR.PIVbus.Ref  = DPLL3P.SysPar.UserVdcref * 0.1f + DPLL3P.Vbus.In * 0.9f;
        }
        
        if(pfc.state_machine_cnt > 50)
            PFC_KMON_OUT(0);
  
        break;

    case Pfc_Err:

        PFC_FS_OUT(0);
        pfc.CMD_PFC_Stop   = 1;
        PFCVAR.PIVbus.Ref  = 0;
        pfc.PFC_Start  = 0;
        pfc.PFC_Runing = 0;
        
        if((pfc.state_machine_cnt > 50) && !(AC380_FreqIs_Ok))
            PFC_KMON_OUT(0);
    
        if (pfc.state_machine_cnt > PFC_ERR_RESET_TICK) //delay 1s
        {
            LL_TIM_ClearFlag_BRK2(TIM8) ;
            LL_TIM_ClearFlag_BRK(TIM8)  ;
            hardware_irq_pin.f_tim8_breakin_flag = 0;
            
            vfd.diag_latch = 0x0;
            pfc.ControlState = Pfc_Stop;
            pfc.state_err_cnt++;
        }
        
        break;

    default:
        pfc.ControlState = Pfc_Stop;
        break;

    }
    
    pfc.state_machine_cnt++;

}


void App_PFCGetData(void)
{
    pfc.VbusVoltage  = DPLL3P.Vbus.In;
    pfc.Temperature  = 0;
    pfc.PfcPower     = DPLL3P.VabcVdq0PN.dp * DPLL3P.IabIdq.d + DPLL3P.VabcVdq0PN.qp * DPLL3P.IabIdq.q;
    
    pfc.Freq         = DPLL3P.DPLLDDSRF.fo_lpf;
    pfc.AbsFreq      = fabs(DPLL3P.DPLLDDSRF.fo_lpf);

    pfc.IacinAmp     = sqrtf(DPLL3P.IabIdq.d * DPLL3P.IabIdq.d + DPLL3P.IabIdq.q * DPLL3P.IabIdq.q);
    pfc.VacinAmp     = sqrtf(DPLL3P.VabcVdq0PN.dp * DPLL3P.VabcVdq0PN.dp + DPLL3P.VabcVdq0PN.qp * DPLL3P.VabcVdq0PN.qp);

    pfc.Id = DPLL3P.IabIdq.d;
    pfc.Iq = DPLL3P.IabIdq.q;

    pfc.Idref = PFCVAR.PIId.Ref;
    pfc.Iqref = 0;

    pfc.Ia = pfc.IacinAmp;
    pfc.Ib = pfc.IacinAmp;
    pfc.Ic = pfc.IacinAmp;

}

void pfc_fault_stop(void)
{
    PFC_FS_OUT(0);
    PfcDisPwm();
    pfc.CMD_PFC_Stop = 1;
    pfc.PFC_Start  = 0;
    pfc.PFC_Runing = 0;
    pfc.ControlState = Pfc_Stop;
    Pfcvar_Clear();
}

////////////////////////////////////////////////////////////////////
//////////////Finsh Msh CMD
////////////////////////////////////////////////////////////////////
#ifdef RT_USING_FINSH
#include "finsh.h"
#include "pfc_dqctrl.h"

void cmd_pfc(int argc, char **argv)
{
    if(argc >= 2)
    {
        if(!rt_strcmp("info",argv[1]))
        {
            rt_kprintf("[Tick %d] state:%d-%s   vbus:%4d   vac:%d  iac:%d freq:%d\r\n",rt_tick_get()/2,pfc.ControlState,pfc_state[pfc.ControlState],
                                                    (int)DPLL3P.Vbus.In,(int)vfd.ad.ac_vin_u,(int)vfd.ad.ac_iin_u,(int)(pfc.Freq));
        }
        else if(!rt_strcmp("cu",argv[1]))
        {
            int val = atoi(argv[2]);
            if(argc >= 3)
                DPLL3P.Vac.Offset.a = (float)val/100;
            
        }
        else if(!rt_strcmp("cv",argv[1]))
        {
            int val = atoi(argv[2]);
            if(argc >= 3)
                DPLL3P.Vac.Offset.b = (float)val/100;
            
        }
        else if(!rt_strcmp("cw",argv[1]))
        {
            int val = atoi(argv[2]);
            if(argc >= 3)
                DPLL3P.Vac.Offset.c = (float)val/100;
            
        }
        else if(!rt_strcmp("start",argv[1]))
        {
            if (pfc.CMD_PFC_Stop)
            {
                rt_kprintf("[Tick %d] pfc set start pwm,delay 1s \r\n",rt_tick_get()/2);
                rt_thread_delay(RT_TICK_PER_SECOND * 1);
                pfc.CMD_PFC_Stop = 0;

            }
            else
            {
                rt_kprintf("[Tick %d] pfc set stop pwm\r\n",rt_tick_get()/2);
                pfc.CMD_PFC_Stop = 1;
            }
        }
        else if(!rt_strcmp("stop",argv[1]))
        {
            rt_kprintf("[Tick %d] pfc set stop pwm\r\n",rt_tick_get()/2);
            pfc.CMD_PFC_Stop = 1;     
        }
        else if(!rt_strcmp("err",argv[1]))
        {
            pfc.ControlState = Pfc_Err;
            rt_kprintf("[Tick %d] pfc set err state \r\n",rt_tick_get()/2);
        }
        else if(!rt_strcmp("iref",argv[1]))
        {
            if(argc >= 3)
            {
                pfc.debug_iref = atof(argv[2]) * 1.414f;
                pfc.debug_iref = (pfc.debug_iref < 1.0f) ? 1.0f : pfc.debug_iref;
            }
            rt_kprintf("[Tick %d] pfc set i ref:%f A \r\n",rt_tick_get()/2,pfc.debug_iref);
        }
        else if(!rt_strcmp("uref",argv[1]))
        {
            if(argc >= 3)
            {
                DPLL3P.SysPar.UserVdcref = atoi(argv[2]);
            }
            rt_kprintf("[Tick %d] pfc set vbus ref:%f V \r\n",rt_tick_get()/2,DPLL3P.SysPar.UserVdcref);
        }
        else if(!rt_strcmp("out",argv[1]))
        {
            rt_kprintf("[Tick %d] %f:Vout.d,%f:Vout.q,%f:Vbus.out, %f:Id.out, %f:Iq.out  \r\n",rt_tick_get()/2,PFCVAR.Vout.d,PFCVAR.Vout.q,PFCVAR.PIVbus.Out,PFCVAR.PIId.Out,PFCVAR.PIIq.Out);
        }
    }
}

MSH_CMD_EXPORT_ALIAS(cmd_pfc, pfc, change pfc work status);
#endif

/******************* (C) COPYRIGHT 2021 Group *****END OF FILE****/
