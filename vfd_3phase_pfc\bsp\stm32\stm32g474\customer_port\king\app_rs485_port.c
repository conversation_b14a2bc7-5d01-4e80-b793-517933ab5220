/******************** (C) COPYRIGHT 2022     ***********************************
* File Name          : app_rs485_port.c
* Author             : xiou
* Version            : V1.0
* Date               :
* Description        : 基于 PMSM08型变频器 RS485通讯协议 Rev 1.0
                        2023.05.07
********************************************************************************/
#include <rtthread.h>
#include <rtdevice.h>
#include "uapp.h"
#include <board.h>
#define DBG_SECTION_NAME               "modbus"
#define DBG_LEVEL                      DBG_LOG
#include <rtdbg.h>
#include "drv_rs485.h"
#include "alg_modbus_common.h"

#define SLAVE_ADDR       (vfd.serial_addr)

extern void rs485_send_frame(const void *buff, int size);
extern uint32_t  HardfaultNormalDelay;
static uint8_t rx_buf[1024] = {0};
static uint8_t tx_buf[1024] = {0};

uint16_t reg_0x2000[32];
uint16_t reg_0x3000[32];
uint16_t reg_0x4000[32];
uint16_t reg_0x0001[32];
uint16_t reg_0x1000[32];
uint16_t reg_0xF000[32];

uint16_t mdbus_regaddr_maptoarray(uint16_t reg_addr)
{
    uint16_t preg_base = RT_NULL;
    
    if((reg_addr >= 0x0000) && (reg_addr < 0x0020))
    {
        preg_base = reg_0x0001;
    }
    else if((reg_addr >= 0x1000) && (reg_addr < 0x1020))
    {
        preg_base = reg_0x1000;
    }
    else if((reg_addr >= 0x2000) && (reg_addr < 0x2020))
    {
        preg_base = reg_0x2000;
    }
    else if((reg_addr >= 0x3000) && (reg_addr < 0x3020))
    {
        preg_base = reg_0x3000;
    }
    else if((reg_addr >= 0x4000) && (reg_addr < 0x4020))
    {
        preg_base = reg_0x4000;
    }
    else if((reg_addr >= 0xF000) && (reg_addr < 0xF020))
    {
        preg_base = reg_0xF000;
    }

    return preg_base;
}

uint16_t mdbus_regaddr_maptoreg(uint16_t reg_addr)
{
    uint16_t preg_base = RT_NULL;
    uint16_t preg_offset = RT_NULL;
    
    preg_offset = reg_addr & 0xFF;
    
    if((reg_addr >= 0x0000) && (reg_addr < 0x0020))
    {
        if(preg_offset < sizeof(reg_0x0001))
            preg_base = &reg_0x0001[preg_offset];
    }
    else if((reg_addr >= 0x1000) && (reg_addr < 0x1020))
    {
        if(preg_offset < sizeof(reg_0x1000))
            preg_base = &reg_0x1000[preg_offset];
    }
    else if((reg_addr >= 0x2000) && (reg_addr < 0x2020))
    {
        if(preg_offset < sizeof(reg_0x2000))
            preg_base = &reg_0x2000[preg_offset];
    }
    else if((reg_addr >= 0x3000) && (reg_addr < 0x3020))
    {
        if(preg_offset < sizeof(reg_0x3000))
            preg_base = &reg_0x3000[preg_offset];
    }
    else if((reg_addr >= 0x4000) && (reg_addr < 0x4020))
    {
        if(preg_offset < sizeof(reg_0x4000))
            preg_base = &reg_0x4000[preg_offset];
    }
    else if((reg_addr >= 0xF000) && (reg_addr < 0xF010))
    {
        if(preg_offset < sizeof(reg_0xF000))
            preg_base = &reg_0xF000[preg_offset];
    }

    return preg_base;
}

typedef union
{
    struct
    {
        uint16_t  cmd_stop                       : 1;
        uint16_t  cmd_speed_enable               : 1;
        uint16_t  cmd_auto_dcdc                  : 1;
        uint16_t  cmd_vbus_enable                : 1;   
        uint16_t  cmd_silent                     : 1;
        uint16_t  cmd_resume_ac380               : 1;  
        uint16_t  cmd_VF_enable                  : 1;
        uint16_t  cmd_clear_powerconsumption     : 1;
        uint16_t  cmd_reset_fault                : 1;
        uint16_t  cmd_set_freq                   : 1;
    }bit;
    
    uint16_t u16;
} cmd_t;

typedef struct
{
    uint32_t  rxcnt;
    uint32_t  txcnt;
    uint16_t  reg;
    uint16_t  set_freq;
    uint16_t  set_speed;
    uint16_t  set_vbus;
    uint16_t  slave_period;          /* set slave period 1=1ms*/
    cmd_t     cmd;
    
    uint8_t   year;
    uint8_t   month;
    uint8_t   day;
    uint8_t   hour;
    uint8_t   min;
    uint8_t   sec;
    
    uint8_t   year_month_flag   : 1;
    uint8_t   day_hour_flag     : 1;
    uint8_t   min_sec_flag      : 1;
    uint8_t   reset_lock_flag      : 1;
    uint8_t   reset_power_flag     : 1;
    
    uint8_t   recv_addr;
}custm_com_t;

custm_com_t modbus_slave = {0};

uint8_t mdbus_get_addr(void)
{
    if(modbus_slave.recv_addr != 0)
        return modbus_slave.recv_addr;
    else
        return SLAVE_ADDR;
}

extern UART_HandleTypeDef huart2;


int Rs485_Baudrate_Get(void)
{
    if((nvs_datas.config.rs485_baudrate >= 9600) && (nvs_datas.config.rs485_baudrate <= 921600))
        return nvs_datas.config.rs485_baudrate;
    else
    {
        nvs_datas.config.rs485_baudrate  = 9600;
        nvs_datas.config.rs485_check_bit = 0;
        return nvs_datas.config.rs485_baudrate;
    }
}


/**
  * @brief USART2 Initialization Function
  * @param None
  * @retval None
  */
int MX_USART2_UART_Init(uint32_t BaudRate)
{
    /* DMA controller clock enable */
    __HAL_RCC_DMAMUX1_CLK_ENABLE();
    __HAL_RCC_DMA1_CLK_ENABLE();

    /* DMA interrupt init */
    HAL_NVIC_SetPriority(DMA1_Channel1_IRQn, 3, 3);
    HAL_NVIC_EnableIRQ(DMA1_Channel1_IRQn);
	
    HAL_NVIC_SetPriority(DMA1_Channel7_IRQn, 3, 3);
    HAL_NVIC_EnableIRQ(DMA1_Channel7_IRQn);
	
    /* USART1_IRQn interrupt configuration */
    HAL_NVIC_SetPriority(USART2_IRQn, 3, 1);
    HAL_NVIC_EnableIRQ(USART2_IRQn);
    
    /* USER CODE END USART2_Init 1 */
    huart2.Instance = USART2;
    
    if(vfd.usart2_default_flag) /**!> PTU Type */
    {
        huart2.Init.BaudRate = BaudRate; 
        huart2.Init.WordLength = UART_WORDLENGTH_8B;
        huart2.Init.StopBits = UART_STOPBITS_1;  
        huart2.Init.Parity = UART_PARITY_NONE;  
    }        
    else
    {
        huart2.Init.BaudRate = BaudRate; 
        huart2.Init.StopBits = UART_STOPBITS_1;  
        
        if(nvs_datas.config.rs485_check_bit == 1)
        {
            huart2.Init.Parity = UART_PARITY_ODD;
            huart2.Init.WordLength = UART_WORDLENGTH_9B;
        }
        else if(nvs_datas.config.rs485_check_bit == 2)
        {
            huart2.Init.Parity = UART_PARITY_EVEN; 
            huart2.Init.WordLength = UART_WORDLENGTH_9B;
        }
        else      
        {
            huart2.Init.Parity = UART_PARITY_NONE;
            huart2.Init.WordLength = UART_WORDLENGTH_8B;
        }
    }        
    
    huart2.Init.Mode = UART_MODE_TX_RX;
    huart2.Init.HwFlowCtl = UART_HWCONTROL_NONE;
    huart2.Init.OverSampling = UART_OVERSAMPLING_16;
    huart2.Init.OneBitSampling = UART_ONE_BIT_SAMPLE_DISABLE;
    huart2.Init.ClockPrescaler = UART_PRESCALER_DIV1;
    huart2.AdvancedInit.AdvFeatureInit = UART_ADVFEATURE_NO_INIT;

    if (HAL_UART_Init(&huart2) != HAL_OK)
    {
        Error_Handler();
    }
    if (HAL_UARTEx_SetTxFifoThreshold(&huart2, UART_TXFIFO_THRESHOLD_1_8) != HAL_OK)
    {
        Error_Handler();
    }
    if (HAL_UARTEx_SetRxFifoThreshold(&huart2, UART_RXFIFO_THRESHOLD_1_8) != HAL_OK)
    {
        Error_Handler();
    }
    if (HAL_UARTEx_DisableFifoMode(&huart2) != HAL_OK)
    {
        Error_Handler();
    }
    
    __HAL_DMA_DISABLE_IT(huart2.hdmatx, DMA_IT_HT); 
    __HAL_DMA_DISABLE_IT(huart2.hdmarx, DMA_IT_HT);
    
    HAL_UART_Receive_DMA(&huart2, RxBuffer_UR_DMA, UR_RX_BUFFER_SIZE_MAX);
    R485_DE_RX();
    /* USER CODE BEGIN USART2_Init 2 */
    return 0;
}

#include "uaInverter.h"
#include "uaInverterPort.h"
int modbus_data_update(void)
{
    static uint8_t first = 1;
    
    reg_0x0001[0x0001] = (int)(Inv_GetFreqRef()*100);
    
    if(SYS_InvRun == vfd.ctrl.inv_st)
    {
        reg_0x0001[0x0002] = (int)(Inv_GetFreqNow()*100);
        reg_0x0001[0x0003] =  vfd.ad.ac_vout;
        reg_0x0001[0x0004] = (vfd.ad.ac_iout_u+vfd.ad.ac_iout_v+vfd.ad.ac_iout_w)/3;
    }
    else
    {
        reg_0x0001[0x0002] = 0x0;
        reg_0x0001[0x0003] =  0;
        reg_0x0001[0x0004] =  0;
    }
    
    reg_0x0001[0x0005] =  vfd.ad.vbus_inv;
    
    reg_0x0001[0x0006] =  vfd.ad.dc_Iin;
    reg_0x0001[0x0007] =  vfd.ad.dc_vinbus;
    
    
    reg_0x0001[0x0008]  = vfd.ad.ac_vin_u;  
    reg_0x0001[0x0009]  = vfd.ad.ac_vin_v;
    reg_0x0001[0x000A]  = vfd.ad.ac_vin_w;
    
    if(vfd.ctrl.pfc_st == Pfc_Run)
    {
        reg_0x0001[0x000B] = vfd.ad.ac_iin_u;
        reg_0x0001[0x000C] = vfd.ad.ac_iin_u;
        reg_0x0001[0x000D] = vfd.ad.ac_iin_u;
    }
    else
    {
        reg_0x0001[0x000B] = 0;
        reg_0x0001[0x000C] = 0;
        reg_0x0001[0x000D] = 0;
    }
    
    reg_0x0001[0x000E] = (uint16_t)(vfd.acin_freq*100);
    
    if((vfd.acin_freq >= 40) && (vfd.acin_freq <= 65))
        reg_0x0001[0x000F] = 1;
    else if((vfd.acin_freq <= -40) && (vfd.acin_freq >= -65))
        reg_0x0001[0x000F] = 0;
    else
        reg_0x0001[0x000F] = 0xFF;
    
    
    reg_0x0001[0x0012] = (uint16_t)(nvs_datas.accumulator.power_consumption/100);
    
    
    if((vfd.ctrl.sys_st != VFD_ERR) &&
        (vfd.ctrl.sys_st != VFD_ERR_LOCK))
    {
        reg_0x0001[0x0013] = 0x00;
        reg_0x0001[0x0014] = 0x00;
    }
    else
    {                 
        U32_SET_BIT(reg_0x0001[0x0013] ,0, vfd.diag.dc_11);          
        U32_SET_BIT(reg_0x0001[0x0013] ,1, vfd.diag.dc_12);          
        U32_SET_BIT(reg_0x0001[0x0013] ,2, vfd.diag.dc_13 || vfd.diag.dc_14 || (vfd.diag.dc_52 & vfd.bit.lock_stop) );          
        U32_SET_BIT(reg_0x0001[0x0013] ,3, vfd.diag.dc_15);          
        U32_SET_BIT(reg_0x0001[0x0013] ,4, vfd.diag.dc_16 || vfd.diag.dc_17);           
        U32_SET_BIT(reg_0x0001[0x0013] ,5, vfd.diag.dc_18 || vfd.diag.dc_19);     
        U32_SET_BIT(reg_0x0001[0x0013] ,6, vfd.diag.dc_25 || vfd.diag.dc_26 || vfd.diag.dc_27);           
        U32_SET_BIT(reg_0x0001[0x0013] ,7, vfd.diag.dc_50);          
        U32_SET_BIT(reg_0x0001[0x0013] ,8, vfd.diag.dc_44 || vfd.diag.dc_45 || vfd.diag.dc_46);          
        U32_SET_BIT(reg_0x0001[0x0013] ,9, vfd.diag.dc_32 );  
        U32_SET_BIT(reg_0x0001[0x0013] ,10,vfd.diag.dc_33);         
        U32_SET_BIT(reg_0x0001[0x0013] ,11,vfd.diag.dc_22 || vfd.diag.dc_23 || vfd.diag.dc_24);  
        U32_SET_BIT(reg_0x0001[0x0013] ,12,vfd.diag.dc_31);         
        U32_SET_BIT(reg_0x0001[0x0013] ,13,vfd.diag.dc_28);      
        U32_SET_BIT(reg_0x0001[0x0013] ,14,vfd.diag.dc_29);  

        U32_SET_BIT(reg_0x0001[0x0013] ,15,vfd.bit.lock_stop); 
        
        U32_SET_BIT(reg_0x0001[0x0014] ,0, vfd.diag.dc_20);          
        U32_SET_BIT(reg_0x0001[0x0014] ,1, vfd.diag.dc_21);          
        U32_SET_BIT(reg_0x0001[0x0014] ,2, vfd.diag.dc_58);          
        U32_SET_BIT(reg_0x0001[0x0014] ,3, vfd.diag.dc_40);          
        U32_SET_BIT(reg_0x0001[0x0014] ,4, vfd.diag.dc_36 || vfd.diag.dc_37 || vfd.diag.dc_38 || vfd.diag.dc_39);           
        U32_SET_BIT(reg_0x0001[0x0014] ,5, vfd.diag.dc_67);     
        U32_SET_BIT(reg_0x0001[0x0014] ,6, (vfd.diag.dc_52 || vfd.diag.dc_53 || vfd.diag.dc_54 || vfd.diag.dc_55 ||\
                                            vfd.diag.dc_56 || vfd.diag.dc_08 || vfd.diag.dc_09 || vfd.diag.dc_10 ) && vfd.bit.lock_stop
                                            );           
        U32_SET_BIT(reg_0x0001[0x0014] ,7, vfd.diag.dc_08 && vfd.bit.lock_stop);          
        U32_SET_BIT(reg_0x0001[0x0014] ,8, vfd.diag.dc_10 && vfd.bit.lock_stop);          
        U32_SET_BIT(reg_0x0001[0x0014] ,9, vfd.diag.dc_09 && vfd.bit.lock_stop);  
        U32_SET_BIT(reg_0x0001[0x0014] ,10,vfd.diag.dc_30);       
    }
    
    reg_0x0001[0x0015] = 0x03; // BSM
    reg_0x0001[0x0016] = (VFD_SOFT_MAIN << 8) | VFD_SOFT_SUB; //0x0100 ==  V1.00
    
    reg_0x0001[0x0017] = 0x0001; // 0x0001 == V01
    
    reg_0x0001[0x0018] = vfd.ad.vsr; 
    reg_0x0001[0x0019] = vfd.ad.vsr2; 
    
    reg_0x0001[0x001A] =  (vfd.ad.temp_pfc > -400) ? ((vfd.ad.temp_pfc/10)+40) : 0;
    reg_0x0001[0x001B] =  (TEMP_PFC_L1 > -400) ? ((TEMP_PFC_L1/10)+40) : 0;
    reg_0x0001[0x001C] =  (vfd.ad.temp_IGBT_L1 > -400) ? ((vfd.ad.temp_IGBT_L1/10)+40) : 0;
    
    reg_0x0001[0x001D] =  (vfd.ad.temp_CAP > -400) ? ((vfd.ad.temp_CAP/10)+40) : 0;
    reg_0x0001[0x001E] =  (TEMP_DCDC_TRANSF > -400) ? ((TEMP_DCDC_TRANSF/10)+40) : 0;
    reg_0x0001[0x001F] =  (vfd.ad.temp_Hb > -400) ? ((vfd.ad.temp_Hb/10)+40) : 0;
    
    reg_0x1000[0x0000] = (vfd.date.year << 8) | vfd.date.month;
    reg_0x1000[0x0001] = (vfd.date.day << 8) | vfd.date.hour;
    reg_0x1000[0x0002] = (vfd.date.min << 8) | vfd.date.sec;
    
    if( !com_can.flag_normal     && 
        !com_modbus.flag_normal  && 
        !com_ptu.flag_normal)
    {
        if(!vfd.ctrl.load_reducing)
        {
            if(VFD_SWITCH_AC380)
                Inv_SetFreqRef((float)nvs_datas.modbus.reg_3000[9]/100);
            else
                Inv_SetFreqRef((float)nvs_datas.modbus.reg_3000[0xD]/100);
        }
        
        if(!vfd.ctrl.erase_flash)
            vfd.manual.start_mask = 0;
    }
    
    first = 0;
    
    for(int i = 0; i< 32; i++)
        reg_0x3000[i] = nvs_datas.modbus.reg_3000[i];
    
    for(int i = 0; i< 32; i++)
        reg_0x4000[i] = nvs_datas.modbus.reg_4000[i];

    
    reg_0xF000[0xE] = nvs_datas.config.serial_com_id;
    
    if(!com_modbus.flag_normal)
        return -1;
    
    /////////////////////////////////////////////////////////////////////////////////////////////

    if(modbus_slave.cmd.bit.cmd_set_freq)
    {
        modbus_slave.cmd.bit.cmd_set_freq = 0;
        Inv_SetFreqRef((float)modbus_slave.set_freq/100);
    }
        
    
    if(     modbus_slave.year_month_flag 
            && modbus_slave.day_hour_flag 
            && modbus_slave.min_sec_flag)
    {
        modbus_slave.year_month_flag = 0;
        modbus_slave.day_hour_flag = 0;
        modbus_slave.min_sec_flag = 0;
        
        uint8_t year = (reg_0x2000[3] >> 8) & 0xFF;
        uint8_t mon  = (reg_0x2000[3] >> 0) & 0xFF;
        uint8_t day  = (reg_0x2000[4] >> 8) & 0xFF;
        uint8_t hour = (reg_0x2000[4] >> 0) & 0xFF;
        uint8_t min  = (reg_0x2000[5] >> 8) & 0xFF;
        uint8_t sec  = (reg_0x2000[5] >> 0) & 0xFF;
        
        timedate_sync_rtc(year,mon,day,hour,min,sec,5); 
    }
    
    if( modbus_slave.reset_power_flag )
    {
        modbus_slave.reset_power_flag = 0;
        nvs_datas.accumulator.power_consumption = 0;
    }
    
    
    if( modbus_slave.reset_lock_flag )
    {
        modbus_slave.reset_lock_flag = 0;
        diag_lockstop_reset();
    }
}                    

int my_modbus_unpack(const uint8_t *buff,uint16_t size)
{
    uint16_t *preg_base = NULL;
    uint16_t reg_addr = 0;
    uint16_t reg_nums = 0;
    uint16_t reg_data = 0;
    uint8_t  reg_bytes = 0;
    uint16_t crc = 0;
    uint8_t offset = 0;
    uint8_t  unpack_ok = 0;
    
    if(size < 1024)
        rt_memcpy(rx_buf,buff,size);
    
    if((SLAVE_ADDR == rx_buf[0]) || (DEFAULT_SERIAL_COM_ID == rx_buf[0]))
    {
        modbus_slave.recv_addr = rx_buf[0];
        //unpack
        reg_addr = mb_invert_u16(*(uint16_t *)&rx_buf[2]);
        reg_nums = mb_invert_u16(*(uint16_t *)&rx_buf[4]);
        offset   = reg_addr & 0xFF;
        preg_base = mdbus_regaddr_maptoarray(reg_addr);
        
        switch(rx_buf[1])
        {
            case MY_MODBUS_FUNC_0x03:
            case MY_MODBUS_FUNC_0x04:
            //ack
            if(preg_base != RT_NULL)
            {
                
                size = mb_slv_pack_respond(tx_buf,
                                rx_buf[1],
                                NULL,
                                reg_nums,
                                &preg_base[offset]
                                );
                
                com_cnter_send(&com_modbus);
                com_cnter_recv(&com_modbus);
                if(!modbus_slave.cmd.bit.cmd_silent)
                    rs485_send_frame(tx_buf,size);
            }
            else
            {
                if(!modbus_slave.cmd.bit.cmd_silent)
                    mb_slv_pack_respond_err(tx_buf,rx_buf[1],0x02,reg_addr);
            }
            break;
            
            case MY_MODBUS_FUNC_0x06:
            
            if(preg_base != RT_NULL)
            {
                preg_base[offset] = reg_data;
                
                if(preg_base == &reg_0x3000[0])
                {
                    if(mdbus_regaddr_maptoreg(reg_addr) != RT_NULL)
                    {
                        char str[8] = {0};
                        
                        rt_sprintf(str,"%04X",reg_addr);
                        
                        nvsdata_write_one_mbreg(str,reg_data);
                    }
                }
                else if(preg_base == &reg_0x2000[0])
                {
                    if(offset == 0)
                    {
                        modbus_slave.cmd.bit.cmd_set_freq = 1;
                        modbus_slave.set_freq = reg_data;
                    }
                    else if(offset == 1)
                    {
                        
                    }
                    else if((offset == 2)&& (reg_data == 0xA5A5))
                    {
                        modbus_slave.reset_lock_flag = 1;
                    }
                    else if(offset == 3)
                    {
                        modbus_slave.year_month_flag = 1;
                    }
                    else if(offset == 4)
                    {
                        modbus_slave.day_hour_flag = 1;
                    }
                    else if(offset == 5)
                    {
                        modbus_slave.min_sec_flag = 1;
                    }
                    else if(offset == 6)
                    {
                        modbus_slave.reset_power_flag = 1;
                    }
                }
                else if(preg_base == &reg_0xF000)
                {
                    if(offset == 0xE)
                    {
                        nvs_datas.config.serial_com_id = reg_data;
                        
                        if(nvs_datas.config.serial_com_id != 0) 
                        {
                            vfd.serial_addr = nvs_datas.config.serial_com_id;
                            vfd.nvs_write_config_flag = 1;
                            WriteSetInfo();
                        }
                    }
                }                
                
                unpack_ok = 1;
            }   
            else
            {
                if(!modbus_slave.cmd.bit.cmd_silent)
                    mb_slv_pack_respond_err(tx_buf,rx_buf[1],0x02,reg_addr);
            }
            
            //ack
            if(unpack_ok)
            {
                size = mb_slv_pack_respond(tx_buf,
                                MY_MODBUS_FUNC_0x06,
                                reg_addr,
                                1,
                                &reg_data
                                );
   
                com_cnter_send(&com_modbus);
                com_cnter_recv(&com_modbus);
                if(!modbus_slave.cmd.bit.cmd_silent)
                    rs485_send_frame(tx_buf,size);
            }
            break;
            
            case MY_MODBUS_FUNC_0x10:
            //unpack
            reg_bytes = rx_buf[6];
            
            if(size != (reg_nums*2 + 9))
                break;
            
            if(preg_base != RT_NULL)
            {
                uint8_t i = 0;
                offset = reg_addr & 0xFF;
                
                while(i < reg_nums)
                {
                    reg_data = mb_invert_u16(*(uint16_t *)&rx_buf[7+i*2]);
                    if(offset < 32)
                    {
                        unpack_ok = 1;
                        
                        if(preg_base == &reg_0x2000)
                        {
                            if(offset == 0)
                            {
                                modbus_slave.cmd.bit.cmd_set_freq = 1;
                                modbus_slave.set_freq = reg_data;
                            }
                            else if(offset == 1)
                            {
                                
                            }
                            else if((offset == 2)&& (reg_data == 0xA5A5))
                            {
                                modbus_slave.reset_lock_flag = 1;
                            }
                            else if(offset == 3)
                            {
                                modbus_slave.year_month_flag = 1;
                            }
                            else if(offset == 4)
                            {
                                modbus_slave.day_hour_flag = 1;
                            }
                            else if(offset == 5)
                            {
                                modbus_slave.min_sec_flag = 1;
                            }
                            else if(offset == 6)
                            {
                                modbus_slave.reset_power_flag = 1;
                            }
                        }
                        else if(preg_base == &reg_0x3000[0])
                        {
                            if(mdbus_regaddr_maptoreg(reg_addr) != RT_NULL)
                            {
                                char str[8] = {0};
                                
                                rt_sprintf(str,"%04X",reg_addr);
                                
                                nvsdata_write_one_mbreg(str,reg_data);
                            }
                        }
                        else if(preg_base == &reg_0xF000)
                        {
                            if(offset == 0xE)
                            {
                                nvs_datas.config.serial_com_id = reg_data;
                                
                                if( (vfd.addr == 0) 
                                    && (nvs_datas.config.serial_com_id != 0) 
                                )
                                {
                                    vfd.serial_addr = nvs_datas.config.serial_com_id;
                                    vfd.nvs_write_config_flag = 1;
                                    WriteSetInfo();
                                }
                            }
                        }   
                        
                        preg_base[offset] = reg_data;
                        
                    }
                    
                    else
                        break;
                    
                    offset++;
                    i++;
                }
                
                unpack_ok = 1;
            }
            else
            {
                if(!modbus_slave.cmd.bit.cmd_silent)
                    mb_slv_pack_respond_err(tx_buf,rx_buf[1],0x02,reg_addr);
            }
            
            //ack
            if(unpack_ok)
            {
                size = mb_slv_pack_respond(tx_buf,
                                MY_MODBUS_FUNC_0x10,
                                reg_addr,
                                reg_nums,
                                &reg_data
                                );
                
                com_cnter_recv(&com_modbus);
                com_cnter_send(&com_modbus);
                if(!modbus_slave.cmd.bit.cmd_silent)
                    rs485_send_frame(tx_buf,size);
            }
            break;
            
            default:
                if(!modbus_slave.cmd.bit.cmd_silent)
                    mb_slv_pack_respond_err(tx_buf,rx_buf[1],0x01,reg_addr);
                break;
            
        }
        
    }
    
    return 0;
}