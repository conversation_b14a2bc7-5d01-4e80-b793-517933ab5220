#MicroXplorer Configuration settings - do not modify
ADC1.Channel-0\#ChannelRegularConversion=ADC_CHANNEL_6
ADC1.Channel-1\#ChannelRegularConversion=ADC_CHANNEL_7
ADC1.Channel-2\#ChannelRegularConversion=ADC_CHANNEL_8
ADC1.ContinuousConvMode=ENABLE
ADC1.DMAContinuousRequests=ENABLE
ADC1.IPParameters=Rank-0\#ChannelRegularConversion,Channel-0\#ChannelRegularConversion,SamplingTime-0\#ChannelRegularConversion,OffsetNumber-0\#ChannelRegularConversion,NbrOfConversionFlag,Rank-1\#ChannelRegularConversion,Channel-1\#ChannelRegularConversion,SamplingTime-1\#ChannelRegularConversion,OffsetNumber-1\#ChannelRegularConversion,Rank-2\#ChannelRegularConversion,Channel-2\#ChannelRegularConversion,SamplingTime-2\#ChannelRegularConversion,OffsetNumber-2\#ChannelRegularConversion,NbrOfConversion,ContinuousConvMode,DMAContinuousRequests,master
ADC1.NbrOfConversion=3
ADC1.NbrOfConversionFlag=1
ADC1.OffsetNumber-0\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC1.OffsetNumber-1\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC1.OffsetNumber-2\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC1.Rank-0\#ChannelRegularConversion=1
ADC1.Rank-1\#ChannelRegularConversion=2
ADC1.Rank-2\#ChannelRegularConversion=3
ADC1.SamplingTime-0\#ChannelRegularConversion=ADC_SAMPLETIME_2CYCLES_5
ADC1.SamplingTime-1\#ChannelRegularConversion=ADC_SAMPLETIME_2CYCLES_5
ADC1.SamplingTime-2\#ChannelRegularConversion=ADC_SAMPLETIME_2CYCLES_5
ADC1.master=1
ADC2.Channel-0\#ChannelRegularConversion=ADC_CHANNEL_3
ADC2.ContinuousConvMode=ENABLE
ADC2.EnableInjectedConversion=ENABLE
ADC2.IPParameters=NbrOfConversionFlag,Channel-0\#ChannelRegularConversion,NbrOfConversion,ContinuousConvMode,EnableInjectedConversion,InjectedRank-9\#ChannelInjectedConversion,InjectedChannel-9\#ChannelInjectedConversion,Rank1_Channel,InjectedSamplingTime-9\#ChannelInjectedConversion,InjectedOffsetNumber-9\#ChannelInjectedConversion,InjectedRank-10\#ChannelInjectedConversion,InjectedChannel-10\#ChannelInjectedConversion,Rank2_Channel,InjectedSamplingTime-10\#ChannelInjectedConversion,InjectedOffsetNumber-10\#ChannelInjectedConversion,InjNumberOfConversion
ADC2.InjNumberOfConversion=2
ADC2.InjectedChannel-10\#ChannelInjectedConversion=ADC_CHANNEL_4
ADC2.InjectedChannel-9\#ChannelInjectedConversion=ADC_CHANNEL_3
ADC2.InjectedOffsetNumber-10\#ChannelInjectedConversion=ADC_OFFSET_NONE
ADC2.InjectedOffsetNumber-9\#ChannelInjectedConversion=ADC_OFFSET_NONE
ADC2.InjectedRank-10\#ChannelInjectedConversion=2
ADC2.InjectedRank-9\#ChannelInjectedConversion=1
ADC2.InjectedSamplingTime-10\#ChannelInjectedConversion=ADC_SAMPLETIME_2CYCLES_5
ADC2.InjectedSamplingTime-9\#ChannelInjectedConversion=ADC_SAMPLETIME_2CYCLES_5
ADC2.NbrOfConversion=1
ADC2.NbrOfConversionFlag=1
ADC2.Rank1_Channel=ADC_CHANNEL_3
ADC2.Rank2_Channel=ADC_CHANNEL_4
ADC3.Channel-1\#ChannelRegularConversion=ADC_CHANNEL_12
ADC3.Channel-2\#ChannelRegularConversion=ADC_CHANNEL_1
ADC3.ContinuousConvMode=ENABLE
ADC3.IPParameters=Rank-1\#ChannelRegularConversion,Channel-1\#ChannelRegularConversion,SamplingTime-1\#ChannelRegularConversion,OffsetNumber-1\#ChannelRegularConversion,NbrOfConversionFlag,master,Rank-2\#ChannelRegularConversion,Channel-2\#ChannelRegularConversion,SamplingTime-2\#ChannelRegularConversion,OffsetNumber-2\#ChannelRegularConversion,NbrOfConversion,ContinuousConvMode
ADC3.NbrOfConversion=2
ADC3.NbrOfConversionFlag=1
ADC3.OffsetNumber-1\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC3.OffsetNumber-2\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC3.Rank-1\#ChannelRegularConversion=1
ADC3.Rank-2\#ChannelRegularConversion=2
ADC3.SamplingTime-1\#ChannelRegularConversion=ADC_SAMPLETIME_2CYCLES_5
ADC3.SamplingTime-2\#ChannelRegularConversion=ADC_SAMPLETIME_2CYCLES_5
ADC3.master=1
ADC4.Channel-0\#ChannelRegularConversion=ADC_CHANNEL_8
ADC4.Channel-1\#ChannelRegularConversion=ADC_CHANNEL_9
ADC4.Channel-2\#ChannelRegularConversion=ADC_CHANNEL_11
ADC4.ContinuousConvMode=ENABLE
ADC4.DMAContinuousRequests=ENABLE
ADC4.EnableInjectedConversion=ENABLE
ADC4.IPParameters=Rank-0\#ChannelRegularConversion,Channel-0\#ChannelRegularConversion,SamplingTime-0\#ChannelRegularConversion,OffsetNumber-0\#ChannelRegularConversion,NbrOfConversionFlag,Rank-1\#ChannelRegularConversion,Channel-1\#ChannelRegularConversion,SamplingTime-1\#ChannelRegularConversion,OffsetNumber-1\#ChannelRegularConversion,Rank-2\#ChannelRegularConversion,Channel-2\#ChannelRegularConversion,SamplingTime-2\#ChannelRegularConversion,OffsetNumber-2\#ChannelRegularConversion,NbrOfConversion,ContinuousConvMode,DMAContinuousRequests,EnableInjectedConversion,InjectedRank-6\#ChannelInjectedConversion,InjectedChannel-6\#ChannelInjectedConversion,Rank1_Channel,InjectedSamplingTime-6\#ChannelInjectedConversion,InjectedOffsetNumber-6\#ChannelInjectedConversion,InjectedRank-7\#ChannelInjectedConversion,InjectedChannel-7\#ChannelInjectedConversion,Rank2_Channel,InjectedSamplingTime-7\#ChannelInjectedConversion,InjectedOffsetNumber-7\#ChannelInjectedConversion,InjectedRank-8\#ChannelInjectedConversion,InjectedChannel-8\#ChannelInjectedConversion,Rank3_Channel,InjectedSamplingTime-8\#ChannelInjectedConversion,InjectedOffsetNumber-8\#ChannelInjectedConversion,InjNumberOfConversion
ADC4.InjNumberOfConversion=3
ADC4.InjectedChannel-6\#ChannelInjectedConversion=ADC_CHANNEL_12
ADC4.InjectedChannel-7\#ChannelInjectedConversion=ADC_CHANNEL_13
ADC4.InjectedChannel-8\#ChannelInjectedConversion=ADC_CHANNEL_7
ADC4.InjectedOffsetNumber-6\#ChannelInjectedConversion=ADC_OFFSET_NONE
ADC4.InjectedOffsetNumber-7\#ChannelInjectedConversion=ADC_OFFSET_NONE
ADC4.InjectedOffsetNumber-8\#ChannelInjectedConversion=ADC_OFFSET_NONE
ADC4.InjectedRank-6\#ChannelInjectedConversion=1
ADC4.InjectedRank-7\#ChannelInjectedConversion=2
ADC4.InjectedRank-8\#ChannelInjectedConversion=3
ADC4.InjectedSamplingTime-6\#ChannelInjectedConversion=ADC_SAMPLETIME_2CYCLES_5
ADC4.InjectedSamplingTime-7\#ChannelInjectedConversion=ADC_SAMPLETIME_2CYCLES_5
ADC4.InjectedSamplingTime-8\#ChannelInjectedConversion=ADC_SAMPLETIME_2CYCLES_5
ADC4.NbrOfConversion=3
ADC4.NbrOfConversionFlag=1
ADC4.OffsetNumber-0\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC4.OffsetNumber-1\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC4.OffsetNumber-2\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC4.Rank-0\#ChannelRegularConversion=1
ADC4.Rank-1\#ChannelRegularConversion=2
ADC4.Rank-2\#ChannelRegularConversion=3
ADC4.Rank1_Channel=ADC_CHANNEL_12
ADC4.Rank2_Channel=ADC_CHANNEL_13
ADC4.Rank3_Channel=ADC_CHANNEL_7
ADC4.SamplingTime-0\#ChannelRegularConversion=ADC_SAMPLETIME_2CYCLES_5
ADC4.SamplingTime-1\#ChannelRegularConversion=ADC_SAMPLETIME_2CYCLES_5
ADC4.SamplingTime-2\#ChannelRegularConversion=ADC_SAMPLETIME_2CYCLES_5
ADC5.Channel-0\#ChannelRegularConversion=ADC_CHANNEL_1
ADC5.Channel-1\#ChannelRegularConversion=ADC_CHANNEL_10
ADC5.ContinuousConvMode=ENABLE
ADC5.DMAContinuousRequests=ENABLE
ADC5.IPParameters=Rank-0\#ChannelRegularConversion,Channel-0\#ChannelRegularConversion,SamplingTime-0\#ChannelRegularConversion,OffsetNumber-0\#ChannelRegularConversion,NbrOfConversionFlag,Rank-1\#ChannelRegularConversion,Channel-1\#ChannelRegularConversion,SamplingTime-1\#ChannelRegularConversion,OffsetNumber-1\#ChannelRegularConversion,NbrOfConversion,ContinuousConvMode,DMAContinuousRequests
ADC5.NbrOfConversion=2
ADC5.NbrOfConversionFlag=1
ADC5.OffsetNumber-0\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC5.OffsetNumber-1\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC5.Rank-0\#ChannelRegularConversion=1
ADC5.Rank-1\#ChannelRegularConversion=2
ADC5.SamplingTime-0\#ChannelRegularConversion=ADC_SAMPLETIME_2CYCLES_5
ADC5.SamplingTime-1\#ChannelRegularConversion=ADC_SAMPLETIME_2CYCLES_5
DAC1.DAC_Channel-DAC_OUT2=DAC_CHANNEL_2
DAC1.IPParameters=DAC_Channel-DAC_OUT2
Dma.ADC1.2.Direction=DMA_PERIPH_TO_MEMORY
Dma.ADC1.2.EventEnable=DISABLE
Dma.ADC1.2.Instance=DMA1_Channel3
Dma.ADC1.2.MemDataAlignment=DMA_MDATAALIGN_HALFWORD
Dma.ADC1.2.MemInc=DMA_MINC_ENABLE
Dma.ADC1.2.Mode=DMA_CIRCULAR
Dma.ADC1.2.PeriphDataAlignment=DMA_PDATAALIGN_HALFWORD
Dma.ADC1.2.PeriphInc=DMA_PINC_DISABLE
Dma.ADC1.2.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.ADC1.2.Priority=DMA_PRIORITY_LOW
Dma.ADC1.2.RequestNumber=1
Dma.ADC1.2.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.ADC1.2.SignalID=NONE
Dma.ADC1.2.SyncEnable=DISABLE
Dma.ADC1.2.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.ADC1.2.SyncRequestNumber=1
Dma.ADC1.2.SyncSignalID=NONE
Dma.ADC2.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.ADC2.0.EventEnable=DISABLE
Dma.ADC2.0.Instance=DMA1_Channel1
Dma.ADC2.0.MemDataAlignment=DMA_MDATAALIGN_HALFWORD
Dma.ADC2.0.MemInc=DMA_MINC_ENABLE
Dma.ADC2.0.Mode=DMA_CIRCULAR
Dma.ADC2.0.PeriphDataAlignment=DMA_PDATAALIGN_HALFWORD
Dma.ADC2.0.PeriphInc=DMA_PINC_DISABLE
Dma.ADC2.0.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.ADC2.0.Priority=DMA_PRIORITY_LOW
Dma.ADC2.0.RequestNumber=1
Dma.ADC2.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.ADC2.0.SignalID=NONE
Dma.ADC2.0.SyncEnable=DISABLE
Dma.ADC2.0.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.ADC2.0.SyncRequestNumber=1
Dma.ADC2.0.SyncSignalID=NONE
Dma.ADC3.6.Direction=DMA_PERIPH_TO_MEMORY
Dma.ADC3.6.EventEnable=DISABLE
Dma.ADC3.6.Instance=DMA1_Channel7
Dma.ADC3.6.MemDataAlignment=DMA_MDATAALIGN_HALFWORD
Dma.ADC3.6.MemInc=DMA_MINC_ENABLE
Dma.ADC3.6.Mode=DMA_CIRCULAR
Dma.ADC3.6.PeriphDataAlignment=DMA_PDATAALIGN_HALFWORD
Dma.ADC3.6.PeriphInc=DMA_PINC_DISABLE
Dma.ADC3.6.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.ADC3.6.Priority=DMA_PRIORITY_LOW
Dma.ADC3.6.RequestNumber=1
Dma.ADC3.6.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.ADC3.6.SignalID=NONE
Dma.ADC3.6.SyncEnable=DISABLE
Dma.ADC3.6.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.ADC3.6.SyncRequestNumber=1
Dma.ADC3.6.SyncSignalID=NONE
Dma.ADC4.3.Direction=DMA_PERIPH_TO_MEMORY
Dma.ADC4.3.EventEnable=DISABLE
Dma.ADC4.3.Instance=DMA1_Channel4
Dma.ADC4.3.MemDataAlignment=DMA_MDATAALIGN_HALFWORD
Dma.ADC4.3.MemInc=DMA_MINC_ENABLE
Dma.ADC4.3.Mode=DMA_CIRCULAR
Dma.ADC4.3.PeriphDataAlignment=DMA_PDATAALIGN_HALFWORD
Dma.ADC4.3.PeriphInc=DMA_PINC_DISABLE
Dma.ADC4.3.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.ADC4.3.Priority=DMA_PRIORITY_LOW
Dma.ADC4.3.RequestNumber=1
Dma.ADC4.3.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.ADC4.3.SignalID=NONE
Dma.ADC4.3.SyncEnable=DISABLE
Dma.ADC4.3.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.ADC4.3.SyncRequestNumber=1
Dma.ADC4.3.SyncSignalID=NONE
Dma.ADC5.1.Direction=DMA_PERIPH_TO_MEMORY
Dma.ADC5.1.EventEnable=DISABLE
Dma.ADC5.1.Instance=DMA1_Channel2
Dma.ADC5.1.MemDataAlignment=DMA_MDATAALIGN_HALFWORD
Dma.ADC5.1.MemInc=DMA_MINC_ENABLE
Dma.ADC5.1.Mode=DMA_CIRCULAR
Dma.ADC5.1.PeriphDataAlignment=DMA_PDATAALIGN_HALFWORD
Dma.ADC5.1.PeriphInc=DMA_PINC_DISABLE
Dma.ADC5.1.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.ADC5.1.Priority=DMA_PRIORITY_LOW
Dma.ADC5.1.RequestNumber=1
Dma.ADC5.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.ADC5.1.SignalID=NONE
Dma.ADC5.1.SyncEnable=DISABLE
Dma.ADC5.1.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.ADC5.1.SyncRequestNumber=1
Dma.ADC5.1.SyncSignalID=NONE
Dma.Request0=ADC2
Dma.Request1=ADC5
Dma.Request2=ADC1
Dma.Request3=ADC4
Dma.Request4=UART5_TX
Dma.Request5=UART5_RX
Dma.Request6=ADC3
Dma.RequestsNb=7
Dma.UART5_RX.5.Direction=DMA_PERIPH_TO_MEMORY
Dma.UART5_RX.5.EventEnable=DISABLE
Dma.UART5_RX.5.Instance=DMA1_Channel6
Dma.UART5_RX.5.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.UART5_RX.5.MemInc=DMA_MINC_ENABLE
Dma.UART5_RX.5.Mode=DMA_CIRCULAR
Dma.UART5_RX.5.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.UART5_RX.5.PeriphInc=DMA_PINC_DISABLE
Dma.UART5_RX.5.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.UART5_RX.5.Priority=DMA_PRIORITY_LOW
Dma.UART5_RX.5.RequestNumber=1
Dma.UART5_RX.5.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.UART5_RX.5.SignalID=NONE
Dma.UART5_RX.5.SyncEnable=DISABLE
Dma.UART5_RX.5.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.UART5_RX.5.SyncRequestNumber=1
Dma.UART5_RX.5.SyncSignalID=NONE
Dma.UART5_TX.4.Direction=DMA_MEMORY_TO_PERIPH
Dma.UART5_TX.4.EventEnable=DISABLE
Dma.UART5_TX.4.Instance=DMA1_Channel5
Dma.UART5_TX.4.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.UART5_TX.4.MemInc=DMA_MINC_ENABLE
Dma.UART5_TX.4.Mode=DMA_NORMAL
Dma.UART5_TX.4.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.UART5_TX.4.PeriphInc=DMA_PINC_DISABLE
Dma.UART5_TX.4.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.UART5_TX.4.Priority=DMA_PRIORITY_LOW
Dma.UART5_TX.4.RequestNumber=1
Dma.UART5_TX.4.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.UART5_TX.4.SignalID=NONE
Dma.UART5_TX.4.SyncEnable=DISABLE
Dma.UART5_TX.4.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.UART5_TX.4.SyncRequestNumber=1
Dma.UART5_TX.4.SyncSignalID=NONE
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.Family=STM32G4
Mcu.IP0=ADC1
Mcu.IP1=ADC2
Mcu.IP10=RCC
Mcu.IP11=RTC
Mcu.IP12=SPI2
Mcu.IP13=SYS
Mcu.IP14=TIM1
Mcu.IP15=TIM2
Mcu.IP16=TIM5
Mcu.IP17=TIM8
Mcu.IP18=UART5
Mcu.IP19=USART1
Mcu.IP2=ADC3
Mcu.IP20=USART2
Mcu.IP3=ADC4
Mcu.IP4=ADC5
Mcu.IP5=DAC1
Mcu.IP6=DMA
Mcu.IP7=FDCAN1
Mcu.IP8=IWDG
Mcu.IP9=NVIC
Mcu.IPNb=21
Mcu.Name=STM32G474Q(B-C-E)Tx
Mcu.Package=LQFP128
Mcu.Pin0=PE2
Mcu.Pin1=PE3
Mcu.Pin10=PF9
Mcu.Pin11=PF10
Mcu.Pin12=PF0-OSC_IN
Mcu.Pin13=PF1-OSC_OUT
Mcu.Pin14=PC0
Mcu.Pin15=PC1
Mcu.Pin16=PC2
Mcu.Pin17=PF2
Mcu.Pin18=PA0
Mcu.Pin19=PA1
Mcu.Pin2=PE4
Mcu.Pin20=PA2
Mcu.Pin21=PA3
Mcu.Pin22=PA4
Mcu.Pin23=PA5
Mcu.Pin24=PA6
Mcu.Pin25=PA7
Mcu.Pin26=PC5
Mcu.Pin27=PB0
Mcu.Pin28=PB1
Mcu.Pin29=PB2
Mcu.Pin3=PE5
Mcu.Pin30=PF11
Mcu.Pin31=PF12
Mcu.Pin32=PF13
Mcu.Pin33=PF14
Mcu.Pin34=PF15
Mcu.Pin35=PE7
Mcu.Pin36=PE8
Mcu.Pin37=PE9
Mcu.Pin38=PE10
Mcu.Pin39=PE11
Mcu.Pin4=PE6
Mcu.Pin40=PE12
Mcu.Pin41=PE13
Mcu.Pin42=PE14
Mcu.Pin43=PE15
Mcu.Pin44=PB10
Mcu.Pin45=PB11
Mcu.Pin46=PB12
Mcu.Pin47=PB13
Mcu.Pin48=PB14
Mcu.Pin49=PB15
Mcu.Pin5=PF3
Mcu.Pin50=PD8
Mcu.Pin51=PD9
Mcu.Pin52=PD10
Mcu.Pin53=PD11
Mcu.Pin54=PD12
Mcu.Pin55=PD13
Mcu.Pin56=PD14
Mcu.Pin57=PC6
Mcu.Pin58=PC7
Mcu.Pin59=PG0
Mcu.Pin6=PF4
Mcu.Pin60=PG1
Mcu.Pin61=PG2
Mcu.Pin62=PG3
Mcu.Pin63=PG4
Mcu.Pin64=PC8
Mcu.Pin65=PA8
Mcu.Pin66=PA9
Mcu.Pin67=PA10
Mcu.Pin68=PA11
Mcu.Pin69=PA12
Mcu.Pin7=PF5
Mcu.Pin70=PA13
Mcu.Pin71=PA14
Mcu.Pin72=PC12
Mcu.Pin73=PG5
Mcu.Pin74=PG6
Mcu.Pin75=PG7
Mcu.Pin76=PD1
Mcu.Pin77=PD2
Mcu.Pin78=PD3
Mcu.Pin79=PB9
Mcu.Pin8=PF7
Mcu.Pin80=PE0
Mcu.Pin81=PE1
Mcu.Pin82=VP_IWDG_VS_IWDG
Mcu.Pin83=VP_RTC_VS_RTC_Activate
Mcu.Pin84=VP_RTC_VS_RTC_Calendar
Mcu.Pin85=VP_SYS_VS_Systick
Mcu.Pin86=VP_SYS_VS_DBSignals
Mcu.Pin87=VP_TIM2_VS_no_output1
Mcu.Pin9=PF8
Mcu.PinsNb=88
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32G474QETx
MxCube.Version=6.2.1
MxDb.Version=DB.6.0.21
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.DMA1_Channel1_IRQn=true\:0\:0\:false\:false\:true\:false\:true
NVIC.DMA1_Channel2_IRQn=true\:0\:0\:false\:false\:true\:false\:true
NVIC.DMA1_Channel3_IRQn=true\:0\:0\:false\:false\:true\:false\:true
NVIC.DMA1_Channel4_IRQn=true\:0\:0\:false\:false\:true\:false\:true
NVIC.DMA1_Channel5_IRQn=true\:0\:0\:false\:false\:true\:false\:true
NVIC.DMA1_Channel6_IRQn=true\:0\:0\:false\:false\:true\:false\:true
NVIC.DMA1_Channel7_IRQn=true\:0\:0\:false\:false\:true\:false\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.EXTI15_10_IRQn=true\:0\:0\:false\:false\:true\:true\:true
NVIC.EXTI9_5_IRQn=true\:0\:0\:false\:false\:true\:true\:true
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.SysTick_IRQn=true\:0\:0\:false\:false\:true\:false\:true
NVIC.TIM2_IRQn=true\:0\:0\:false\:false\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false
PA0.Locked=true
PA0.Signal=S_TIM5_CH1
PA1.Locked=true
PA1.Signal=GPIO_Output
PA10.Locked=true
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA11.Locked=true
PA11.Mode=FDCAN_Activate
PA11.Signal=FDCAN1_RX
PA12.Locked=true
PA12.Mode=FDCAN_Activate
PA12.Signal=FDCAN1_TX
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA2.Locked=true
PA2.Mode=Asynchronous
PA2.Signal=USART2_TX
PA3.Locked=true
PA3.Mode=Asynchronous
PA3.Signal=USART2_RX
PA4.Locked=true
PA4.Signal=COMP_DAC11_group
PA5.Locked=true
PA5.Signal=COMP_DAC12_group
PA6.Locked=true
PA6.Mode=IN3-Single-Ended
PA6.Signal=ADC2_IN3
PA7.Mode=IN4-Single-Ended
PA7.Signal=ADC2_IN4
PA8.Locked=true
PA8.Mode=IN1-Single-Ended
PA8.Signal=ADC5_IN1
PA9.Locked=true
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB0.Locked=true
PB0.Mode=IN12-Single-Ended
PB0.Signal=ADC3_IN12
PB1.Locked=true
PB1.Mode=IN1-Single-Ended
PB1.Signal=ADC3_IN1
PB10.GPIOParameters=GPIO_ModeDefaultEXTI
PB10.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_RISING_FALLING
PB10.Locked=true
PB10.Signal=GPXTI10
PB11.GPIOParameters=GPIO_ModeDefaultEXTI
PB11.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_RISING_FALLING
PB11.Locked=true
PB11.Signal=GPXTI11
PB12.Locked=true
PB12.Signal=GPIO_Output
PB13.Locked=true
PB13.Mode=Full_Duplex_Master
PB13.Signal=SPI2_SCK
PB14.Locked=true
PB14.Mode=Full_Duplex_Master
PB14.Signal=SPI2_MISO
PB15.Locked=true
PB15.Mode=Full_Duplex_Master
PB15.Signal=SPI2_MOSI
PB2.Locked=true
PB2.Mode=IN12-Single-Ended
PB2.Signal=ADC2_IN12
PB9.GPIOParameters=GPIO_ModeDefaultEXTI
PB9.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_RISING_FALLING
PB9.Locked=true
PB9.Signal=GPXTI9
PC0.Locked=true
PC0.Mode=IN6-Single-Ended
PC0.Signal=ADC1_IN6
PC1.Locked=true
PC1.Mode=IN7-Single-Ended
PC1.Signal=ADC1_IN7
PC12.Locked=true
PC12.Mode=Asynchronous
PC12.Signal=UART5_TX
PC2.Locked=true
PC2.Mode=IN8-Single-Ended
PC2.Signal=ADC1_IN8
PC5.Locked=true
PC5.Mode=IN11-Single-Ended
PC5.Signal=ADC2_IN11
PC6.Locked=true
PC6.Signal=S_TIM8_CH1
PC7.Locked=true
PC7.Signal=S_TIM8_CH2
PC8.Locked=true
PC8.Signal=S_TIM8_CH3
PD1.Locked=true
PD1.Mode=Activate-Break-Input-2
PD1.Signal=TIM8_BKIN2
PD10.Locked=true
PD10.Mode=IN7-Single-Ended
PD10.Signal=ADC4_IN7
PD11.Locked=true
PD11.Mode=IN8-Single-Ended
PD11.Signal=ADC4_IN8
PD12.Locked=true
PD12.Mode=IN9-Single-Ended
PD12.Signal=ADC4_IN9
PD13.Locked=true
PD13.Mode=IN10-Single-Ended
PD13.Signal=ADC5_IN10
PD14.Locked=true
PD14.Mode=IN11-Single-Ended
PD14.Signal=ADC4_IN11
PD2.Locked=true
PD2.Mode=Asynchronous
PD2.Signal=UART5_RX
PD3.GPIOParameters=PinState
PD3.Locked=true
PD3.PinState=GPIO_PIN_RESET
PD3.Signal=GPIO_Output
PD8.Locked=true
PD8.Mode=IN12-Single-Ended
PD8.Signal=ADC4_IN12
PD9.Locked=true
PD9.Mode=IN13-Single-Ended
PD9.Signal=ADC4_IN13
PE0.Locked=true
PE0.Signal=GPIO_Output
PE1.Locked=true
PE1.Signal=GPIO_Output
PE10.Locked=true
PE10.Mode=PWM Generation2 CH2 CH2N
PE10.Signal=TIM1_CH2N
PE11.Locked=true
PE11.Signal=S_TIM1_CH2
PE12.Locked=true
PE12.Mode=PWM Generation3 CH3 CH3N
PE12.Signal=TIM1_CH3N
PE13.Locked=true
PE13.Signal=S_TIM1_CH3
PE14.Locked=true
PE14.Mode=Activate-Break-Input-2
PE14.Signal=TIM1_BKIN2
PE15.Locked=true
PE15.Signal=GPIO_Output
PE2.Locked=true
PE2.Signal=GPIO_Output
PE3.Locked=true
PE3.Signal=GPIO_Output
PE4.Locked=true
PE4.Signal=GPIO_Output
PE5.Locked=true
PE5.Signal=GPIO_Output
PE6.Locked=true
PE6.Signal=GPIO_Output
PE7.Locked=true
PE7.Signal=GPIO_Output
PE8.Locked=true
PE8.Mode=PWM Generation1 CH1 CH1N
PE8.Signal=TIM1_CH1N
PE9.Locked=true
PE9.Signal=S_TIM1_CH1
PF0-OSC_IN.Mode=HSE-External-Oscillator
PF0-OSC_IN.Signal=RCC_OSC_IN
PF1-OSC_OUT.Mode=HSE-External-Oscillator
PF1-OSC_OUT.Signal=RCC_OSC_OUT
PF10.Locked=true
PF10.Signal=GPIO_Output
PF11.Locked=true
PF11.Signal=GPIO_Output
PF12.Locked=true
PF12.Signal=GPIO_Output
PF13.Locked=true
PF13.Signal=GPIO_Output
PF14.Locked=true
PF14.Signal=GPIO_Output
PF15.Locked=true
PF15.Signal=GPIO_Output
PF2.Locked=true
PF2.Signal=GPIO_Input
PF3.Locked=true
PF3.Signal=GPIO_Input
PF4.Locked=true
PF4.Signal=GPIO_Input
PF5.Locked=true
PF5.Signal=GPIO_Input
PF7.Locked=true
PF7.Signal=GPIO_Input
PF8.Locked=true
PF8.Signal=GPIO_Input
PF9.Locked=true
PF9.Signal=GPIO_Input
PG0.Locked=true
PG0.Signal=GPIO_Output
PG1.Locked=true
PG1.Signal=GPIO_Output
PG2.Locked=true
PG2.Signal=GPIO_Output
PG3.Locked=true
PG3.Signal=GPIO_Output
PG4.Locked=true
PG4.Signal=GPIO_Output
PG5.Locked=true
PG5.Signal=GPIO_Output
PG6.Locked=true
PG6.Signal=GPIO_Output
PG7.Locked=true
PG7.Signal=GPIO_Output
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=false
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32G474QETx
ProjectManager.FirmwarePackage=STM32Cube FW_G4 V1.4.0
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=CubeMX_Config.ioc
ProjectManager.ProjectName=CubeMX_Config
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5.27
ProjectManager.ToolChainLocation=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-MX_GPIO_Init-GPIO-false-HAL-true,2-MX_DMA_Init-DMA-false-HAL-true,3-SystemClock_Config-RCC-false-HAL-true,4-MX_IWDG_Init-IWDG-false-HAL-true,5-MX_FDCAN1_Init-FDCAN1-false-HAL-true,6-MX_SPI2_Init-SPI2-false-HAL-true,7-MX_UART5_Init-UART5-false-HAL-true,8-MX_USART1_UART_Init-USART1-false-HAL-true,9-MX_USART2_UART_Init-USART2-false-HAL-true,10-MX_ADC1_Init-ADC1-false-HAL-true,11-MX_ADC2_Init-ADC2-false-HAL-true,12-MX_ADC4_Init-ADC4-false-HAL-true,13-MX_ADC5_Init-ADC5-false-HAL-true,14-MX_DAC1_Init-DAC1-false-HAL-true,15-MX_TIM1_Init-TIM1-false-HAL-true,16-MX_TIM5_Init-TIM5-false-HAL-true,17-MX_TIM8_Init-TIM8-false-HAL-true,18-MX_RTC_Init-RTC-false-HAL-true,19-MX_TIM2_Init-TIM2-false-HAL-true,20-MX_ADC3_Init-ADC3-false-HAL-true
RCC.ADC12Freq_Value=170000000
RCC.ADC345Freq_Value=170000000
RCC.AHBFreq_Value=170000000
RCC.APB1Freq_Value=170000000
RCC.APB1TimFreq_Value=170000000
RCC.APB2Freq_Value=170000000
RCC.APB2TimFreq_Value=170000000
RCC.CRSFreq_Value=48000000
RCC.CortexFreq_Value=170000000
RCC.EXTERNAL_CLOCK_VALUE=12288000
RCC.FCLKCortexFreq_Value=170000000
RCC.FDCANFreq_Value=170000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=170000000
RCC.HRTIM1Freq_Value=170000000
RCC.HSE_VALUE=8000000
RCC.HSI48_VALUE=48000000
RCC.HSI_VALUE=16000000
RCC.I2C1Freq_Value=170000000
RCC.I2C2Freq_Value=170000000
RCC.I2C3Freq_Value=170000000
RCC.I2C4Freq_Value=170000000
RCC.I2SFreq_Value=170000000
RCC.IPParameters=ADC12Freq_Value,ADC345Freq_Value,AHBFreq_Value,APB1Freq_Value,APB1TimFreq_Value,APB2Freq_Value,APB2TimFreq_Value,CRSFreq_Value,CortexFreq_Value,EXTERNAL_CLOCK_VALUE,FCLKCortexFreq_Value,FDCANFreq_Value,FamilyName,HCLKFreq_Value,HRTIM1Freq_Value,HSE_VALUE,HSI48_VALUE,HSI_VALUE,I2C1Freq_Value,I2C2Freq_Value,I2C3Freq_Value,I2C4Freq_Value,I2SFreq_Value,LPTIM1Freq_Value,LPUART1Freq_Value,LSCOPinFreq_Value,LSE_VALUE,LSI_VALUE,MCO1PinFreq_Value,PLLM,PLLN,PLLPoutputFreq_Value,PLLQoutputFreq_Value,PLLRCLKFreq_Value,PLLSourceVirtual,PWRFreq_Value,QSPIFreq_Value,RNGFreq_Value,SAI1Freq_Value,SYSCLKFreq_VALUE,SYSCLKSource,UART4Freq_Value,UART5Freq_Value,USART1Freq_Value,USART2Freq_Value,USART3Freq_Value,USBFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value
RCC.LPTIM1Freq_Value=170000000
RCC.LPUART1Freq_Value=170000000
RCC.LSCOPinFreq_Value=32000
RCC.LSE_VALUE=32768
RCC.LSI_VALUE=32000
RCC.MCO1PinFreq_Value=16000000
RCC.PLLM=RCC_PLLM_DIV2
RCC.PLLN=85
RCC.PLLPoutputFreq_Value=170000000
RCC.PLLQoutputFreq_Value=170000000
RCC.PLLRCLKFreq_Value=170000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.PWRFreq_Value=170000000
RCC.QSPIFreq_Value=170000000
RCC.RNGFreq_Value=170000000
RCC.SAI1Freq_Value=170000000
RCC.SYSCLKFreq_VALUE=170000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.UART4Freq_Value=170000000
RCC.UART5Freq_Value=170000000
RCC.USART1Freq_Value=170000000
RCC.USART2Freq_Value=170000000
RCC.USART3Freq_Value=170000000
RCC.USBFreq_Value=170000000
RCC.VCOInputFreq_Value=4000000
RCC.VCOOutputFreq_Value=340000000
RTC.Format=RTC_FORMAT_BIN
RTC.IPParameters=Format
SH.COMP_DAC11_group.0=DAC1_OUT1,DAC_OUT1
SH.COMP_DAC11_group.ConfNb=1
SH.COMP_DAC12_group.0=DAC1_OUT2,DAC_OUT2
SH.COMP_DAC12_group.ConfNb=1
SH.GPXTI10.0=GPIO_EXTI10
SH.GPXTI10.ConfNb=1
SH.GPXTI11.0=GPIO_EXTI11
SH.GPXTI11.ConfNb=1
SH.GPXTI9.0=GPIO_EXTI9
SH.GPXTI9.ConfNb=1
SH.S_TIM1_CH1.0=TIM1_CH1,PWM Generation1 CH1 CH1N
SH.S_TIM1_CH1.ConfNb=1
SH.S_TIM1_CH2.0=TIM1_CH2,PWM Generation2 CH2 CH2N
SH.S_TIM1_CH2.ConfNb=1
SH.S_TIM1_CH3.0=TIM1_CH3,PWM Generation3 CH3 CH3N
SH.S_TIM1_CH3.ConfNb=1
SH.S_TIM5_CH1.0=TIM5_CH1,PWM Generation1 CH1
SH.S_TIM5_CH1.ConfNb=1
SH.S_TIM8_CH1.0=TIM8_CH1,PWM Generation1 CH1
SH.S_TIM8_CH1.ConfNb=1
SH.S_TIM8_CH2.0=TIM8_CH2,PWM Generation2 CH2
SH.S_TIM8_CH2.ConfNb=1
SH.S_TIM8_CH3.0=TIM8_CH3,PWM Generation3 CH3
SH.S_TIM8_CH3.ConfNb=1
SPI2.BaudRatePrescaler=SPI_BAUDRATEPRESCALER_4
SPI2.CalculateBaudRate=42.5 MBits/s
SPI2.DataSize=SPI_DATASIZE_8BIT
SPI2.Direction=SPI_DIRECTION_2LINES
SPI2.IPParameters=VirtualType,Mode,Direction,BaudRatePrescaler,CalculateBaudRate,DataSize
SPI2.Mode=SPI_MODE_MASTER
SPI2.VirtualType=VM_MASTER
TIM1.Channel-PWM\ Generation1\ CH1\ CH1N=TIM_CHANNEL_1
TIM1.Channel-PWM\ Generation2\ CH2\ CH2N=TIM_CHANNEL_2
TIM1.Channel-PWM\ Generation3\ CH3\ CH3N=TIM_CHANNEL_3
TIM1.IPParameters=Channel-PWM Generation3 CH3 CH3N,Channel-PWM Generation2 CH2 CH2N,Channel-PWM Generation1 CH1 CH1N
TIM2.Channel-Output\ Compare1\ No\ Output=TIM_CHANNEL_1
TIM2.IPParameters=Channel-Output Compare1 No Output,PeriodNoDither,Prescaler
TIM2.PeriodNoDither=100
TIM2.Prescaler=169
TIM5.Channel-PWM\ Generation1\ CH1=TIM_CHANNEL_1
TIM5.IPParameters=Channel-PWM Generation1 CH1,Prescaler,PeriodNoDither
TIM5.PeriodNoDither=262
TIM5.Prescaler=2
TIM8.Channel-PWM\ Generation1\ CH1=TIM_CHANNEL_1
TIM8.Channel-PWM\ Generation2\ CH2=TIM_CHANNEL_2
TIM8.Channel-PWM\ Generation3\ CH3=TIM_CHANNEL_3
TIM8.IPParameters=Channel-PWM Generation1 CH1,Channel-PWM Generation2 CH2,Channel-PWM Generation3 CH3
USART1.IPParameters=VirtualMode-Asynchronous
USART1.VirtualMode-Asynchronous=VM_ASYNC
USART2.IPParameters=VirtualMode-Asynchronous
USART2.VirtualMode-Asynchronous=VM_ASYNC
VP_IWDG_VS_IWDG.Mode=IWDG_Activate
VP_IWDG_VS_IWDG.Signal=IWDG_VS_IWDG
VP_RTC_VS_RTC_Activate.Mode=RTC_Enabled
VP_RTC_VS_RTC_Activate.Signal=RTC_VS_RTC_Activate
VP_RTC_VS_RTC_Calendar.Mode=RTC_Calendar
VP_RTC_VS_RTC_Calendar.Signal=RTC_VS_RTC_Calendar
VP_SYS_VS_DBSignals.Mode=DisableDeadBatterySignals
VP_SYS_VS_DBSignals.Signal=SYS_VS_DBSignals
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM2_VS_no_output1.Mode=Output Compare1 No Output
VP_TIM2_VS_no_output1.Signal=TIM2_VS_no_output1
board=custom
