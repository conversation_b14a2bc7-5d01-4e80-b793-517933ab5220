
/***************************************************************************

Copyright (C), 2022-2022, BSM Tech. Co., Ltd.

* @file           pfc_isr.c
* <AUTHOR> @version        V0.0.1
* @date           2022-09-06
* @brief          this is PFC isr

History:          // Revision Records

       <Author>             <time>         <version >               <desc>



***************************************************************************/
#include "pfc_isr.h"
#include "pfc_pwm.h"
#include "pfc_dqctrl.h"
#include "pfc_sdpll.h"

#define LED_COMM_PIN    GET_PIN(G, 1)

static uint32_t ctrlFreqCnt = 0;
static volatile uint32_t enter_tick;
static volatile uint32_t leave_tick;
static volatile uint32_t use_tick;
static void ad_sample_filter(void);
extern void sDPLL3P(void);
extern void pfc_pwmctrl(void);
extern ADC_HandleTypeDef hadc4;
extern DMA_HandleTypeDef hdma_adc4;
extern void adc_pfc_dma_isr(void);
extern void PfcDisPwm(void);
extern void tim8_break_isr(void);
extern void pfc_ac_collect(void);
volatile int pfc_irqing_flag = 0;
static void fast_cache_data(void);
static void pfc_ad_sample(void);
extern uint16_t pfc_adc_value[8];

#include "alg_pcmaster.h"
/**
  * @brief 
  * @param 
  * @retval 
  */
void DMA2_Channel2_IRQHandler(void)
{
    static uint8_t freq_cnt = 0;
    rt_ubase_t  level;

    level = rt_hw_interrupt_disable();
    
    pfc_irqing_flag = 1;
    
    /* USER CODE BEGIN DMA1_Channel1_IRQn 0 */
    HAL_DMA_IRQHandler(&hdma_adc4);

    enter_tick = TIM3->CNT;
    pfc.cycle_tick = (enter_tick > pfc.prev_tick) ? (enter_tick - pfc.prev_tick) : pfc.cycle_tick;
    pfc.prev_tick  = enter_tick;
    
    ad_sample_filter();
    
    adc_pfc_dma_isr(); /* pfc main int function */
    
    float abs_freq = fabs(DPLL3P.DPLLDDSRF.lfo);
    float abs_Va = (vfd.ad.ac_vin_u*0.1f);
    float abs_Vb = (vfd.ad.ac_vin_v*0.1f);
    float abs_Vc = (vfd.ad.ac_vin_w*0.1f);
    
    static uint8_t save_isr_freq_flag = 0;
    
    if(   ((int)abs_freq >= 40)
      &&  ((int)abs_freq <= 65)
      &&  ((abs_Va > 50) || (abs_Vb > 50) || (abs_Vc > 50))    
      )
    {
        freq_cnt = 0;
        vfd.bit.isr_freq_ready = 1;
    }
    else if( ((abs_freq <= 5) || (abs_freq >= 95))
              ||  ((abs_Va <= 50) && (abs_Vb <= 50) && (abs_Vc <= 50))
    )
    {
        if(freq_cnt < 10)
            freq_cnt++;
        else
        {
            vfd.bit.isr_freq_ready = 0;
            
            #ifndef VFD_TEST_DEBUG
            if((VFD_NORMAL == vfd.ctrl.sys_st) && PFC_IS_RUN)
                vfd.inverter->ops->stop();
            #endif
            
            vfd.pfc->ops->stop();
        }
    }
    
    if(vfd.bit.isr_freq_ready != save_isr_freq_flag)    vfd.freq_switch_cnt++;
    
    save_isr_freq_flag = vfd.bit.isr_freq_ready;
    
    static uint32_t freq_ready_cnt = 0;
    vfd.bit.freq_ready = single_filter(vfd.bit.freq_ready,vfd.bit.isr_freq_ready,&freq_ready_cnt,100);  
    
    pfc_ad_sample();
    fast_cache_data();
    
    { 
        void Fullbridge_DCDC_Pid(void);
        extern float vbus_folpf_coffee_dcdc;
        
        //vbus = F32_FoLowPassFilter(vbus,DPLL3P.Vbus.Fil*10,vbus_folpf_coffee_dcdc);
        vfd.boost->ops->control(BOOST_CMD_UPDATE_VOLT_FB ,(uint32_t)(DPLL3P.Vbus.Fil*10));
       
        Fullbridge_DCDC_Pid();
    }
    
    PC_M1_pcmasterdrvRecorder();
    //cal cpu usage
    leave_tick = TIM3->CNT;
    
    if(leave_tick > enter_tick)
        use_tick = (leave_tick - enter_tick);
    else
        use_tick = (leave_tick + 0xFFFF - enter_tick);
    
    pfc.cpu = use_tick*(float)Tim8_Freq/1000000.0f;
    pfc.use_tick = use_tick;
    
    if(pfc.cpu > 1)
       pfc.cpu_overload++;
        
    pfc_irqing_flag = 0;
    rt_hw_interrupt_enable(level);
    
    
    /* USER CODE END DMA1_Channel1_IRQn 0 */
}

static void pfc_ad_sample(void)
{
    static uint32_t tick = 0;
    static uint32_t freq = 0;
    static float ad_200us_div = 0;
    static float ad_200us_cnt = 0;
    
    tick++;
    
    if(freq != Tim8_Freq)
    {
        ad_200us_div = 200.0f * ((float)Tim8_Freq/1000000.0f);
        freq = Tim8_Freq;
        ad_200us_cnt = ctrlFreqCnt+ad_200us_div;
    }
    
    if(tick >= ad_200us_cnt)
    {
        acInput_adc_collect();
        
        if(tick != ad_200us_cnt)
            ad_200us_cnt = ad_200us_cnt + ad_200us_div;
        else
        {
            tick = 0;
            ad_200us_cnt = ad_200us_div;
        }
    }
}

/*=======================================================*/
/**
 * @brief   Calibrate AD offset.
 * @return  None.
 */
 CurExcursion_t gUinExcursion;
 CurExcursion_t gIinExcursion;
void pfc_3PCalibADOffset(void)
{
    if(!gIinExcursion.EnableFlag)
    {
        gIinExcursion.ZeroIu = IA_OFFSET_A;
        gIinExcursion.ZeroIv = IB_OFFSET_A;
        gIinExcursion.ZeroIw = IC_OFFSET_A;
    }
    
    if(vfd.pfc->ControlState != Pfc_Stop)
    {
        gIinExcursion.EnableCount = 0;
        return;
    }
   
	gIinExcursion.Iu  = ((float)pfc_adc_value[4] * USER_CURRENT_SF) ;
	gIinExcursion.Iv  = ((float)pfc_adc_value[5] * USER_CURRENT_SF) ;
    gIinExcursion.Iw  = ((float)pfc_adc_value[6] * USER_CURRENT_SF) ;
    
    gIinExcursion.EnableCount++;
    gIinExcursion.EnableCount = ( gIinExcursion.EnableCount > 200) ? 200 : gIinExcursion.EnableCount;
    if(gIinExcursion.EnableCount < 200)
    {
        gIinExcursion.TotalIu = 0;
        gIinExcursion.TotalIv = 0;
        gIinExcursion.TotalIw = 0;
        gIinExcursion.Count = 0;
        return;
    }
    
    gIinExcursion.TotalIu += gIinExcursion.Iu;
    gIinExcursion.TotalIv += gIinExcursion.Iv;
    gIinExcursion.TotalIw += gIinExcursion.Iw;
    gIinExcursion.Count++;
    
    if(gIinExcursion.Count >= 2000)
    {
        float m_ZeroIu,m_ZeroIv,m_ZeroIw;
        
        m_ZeroIu = gIinExcursion.TotalIu * 0.0005f;
        m_ZeroIv = gIinExcursion.TotalIv * 0.0005f;
        m_ZeroIw = gIinExcursion.TotalIw * 0.0005f;
        
        gIinExcursion.TotalIu = 0;
        gIinExcursion.TotalIv = 0;
        gIinExcursion.TotalIw = 0;
        gIinExcursion.Count = 0;
        
        if((fabs(m_ZeroIu-IA_OFFSET_A) <= 1.0f) &&
           (fabs(m_ZeroIv-IB_OFFSET_A) <= 1.0f) &&
           (fabs(m_ZeroIw-IC_OFFSET_A) <= 1.0f) )
        {
            gIinExcursion.ZeroIu = m_ZeroIu;
            gIinExcursion.ZeroIv = m_ZeroIv;
            gIinExcursion.ZeroIw = m_ZeroIw;
            gIinExcursion.ErrCnt = 0;
            gIinExcursion.EnableFlag = 1;
        }
        else if(gIinExcursion.ErrCnt++ >= 5)
        {
            gIinExcursion.ErrCnt = 0;
            gIinExcursion.EnableCount = 0;
        }
        
    }
    
}



/**
  * @brief 
  * @param 
  * @retval 
  */
void adc_pfc_dma_isr(void)
{
    ctrlFreqCnt ++;
    
    {
        //LED_COM_OUT(1);
        
        sDPLL3P();
        
        #ifdef VFD_TEST_DEBUG
        if( !vfd.ctrl.start_pfc     &&
            (vfd.manual.pfc_u_duty || vfd.manual.pfc_v_duty || vfd.manual.pfc_w_duty)
        )
        {
            PfcEnPwm();
            LL_TIM_OC_SetCompareCH1(TIM8, TIM8->ARR * vfd.manual.pfc_u_duty/100);
            LL_TIM_OC_SetCompareCH2(TIM8, TIM8->ARR * vfd.manual.pfc_v_duty/100);
            LL_TIM_OC_SetCompareCH3(TIM8, TIM8->ARR * vfd.manual.pfc_w_duty/100); 
        }
        else 
        #endif
        {
            pfc_qdctrl();
            pfc_pwmctrl();
        }
        
        
        pfc_3PCalibADOffset();   /* only use to get cali data */
        
        pfc.PFC_PreRuning = pfc.PFC_Runing;
      
        //LED_COM_OUT(0);
    }
    
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void TIM8_BRK_IRQHandler(void)
{
  /* USER CODE BEGIN TIM1_BRK_TIM15_IRQHandler 0 */
  if (LL_TIM_IsActiveFlag_BRK(TIM8))
  {
      LL_TIM_ClearFlag_BRK(TIM8);
      tim8_break_isr();
  }
  
  if (LL_TIM_IsActiveFlag_BRK2(TIM8))
  {
      LL_TIM_ClearFlag_BRK2(TIM8);
      tim8_break_isr();
  }
  /* USER CODE END TIM1_BRK_TIM15_IRQHandler 0 */
}

#include "app_ad.h"
extern uint16_t pfc_adc_value[8];
static void ad_sample_filter(void)
{
    uint16_t ia_max,ia_min;
    float vbus_cali = 0;
    pfc_adc_value[0] = (ADC4_ValTemp[1]+ADC4_ValTemp[4]+ADC4_ValTemp[7]+ADC4_ValTemp[10]+ADC4_ValTemp[13]) * 0.2f;
    pfc_adc_value[1] = (ADC4_ValTemp[2]+ADC4_ValTemp[5]+ADC4_ValTemp[8]+ADC4_ValTemp[11]+ADC4_ValTemp[14]) * 0.2f;
    pfc_adc_value[2] = (ADC4_ValTemp[3]+ADC4_ValTemp[6]+ADC4_ValTemp[9]+ADC4_ValTemp[12]+ADC4_ValTemp[15]) * 0.2f;
    
    uint16_t vbus_ad = ADC4_ValTemp[0];
    vbus_cali = (vbus_ad >= 100) ? 95.0f : (vbus_ad * 95.0f/100.0f);
    
    pfc_adc_value[3] = (vbus_ad + vbus_cali);
    
    pfc_adc_value[4] = (ADC5_ValTemp[0]+ADC5_ValTemp[3]+ADC5_ValTemp[6]+ADC5_ValTemp[9] +ADC5_ValTemp[12]) * 0.2f;
    pfc_adc_value[5] = (ADC5_ValTemp[1]+ADC5_ValTemp[4]+ADC5_ValTemp[7]+ADC5_ValTemp[10]+ADC5_ValTemp[13]) * 0.2f;
    pfc_adc_value[6] = (ADC5_ValTemp[2]+ADC5_ValTemp[5]+ADC5_ValTemp[8]+ADC5_ValTemp[11]+ADC5_ValTemp[14]) * 0.2f;
    
    static float vbus_array[200] = {0};
    static int vbus_array_cnt = 0;
    static float vbus_sum = 0;
    static char first = 1;
    uint16_t last_vbus = 0;
     
    if(vbus_array_cnt >= 200)
        vbus_array_cnt = 0;
    
    last_vbus = vbus_array[vbus_array_cnt];
    vbus_array[vbus_array_cnt++] = pfc_adc_value[3];
    
    vbus_sum += pfc_adc_value[3];
    vbus_sum -= last_vbus;
    
    pfc_adc_value[7] = vbus_sum / 200;

}

static void fast_cache_data(void)
{
    static uint32_t cnt = 0;
    cnt++;
    
    if(vfd.disp_ctrl == 0) return;
    
    #ifdef DEBUG_SERIAL_PLOT
    
    if(cnt % 2  == 0) 
    {
        if((vfd.debug_picnt < DEBUG_PLOT_POINTS)
        )
        {
            if(vfd.debug_data_index == 10)
            {      
                vfd.debug_data1[vfd.debug_picnt] = DPLL3P.VLac.In.a;
                vfd.debug_data2[vfd.debug_picnt] = DPLL3P.VLac.In.b;            
                vfd.debug_data3[vfd.debug_picnt] = DPLL3P.VLac.In.c;
//                vfd.debug_data4[vfd.debug_picnt] = DPLL3P.Vbus.In;
           
            }
            else if(vfd.debug_data_index == 11)
            {
//                if(pfc.PFC_Runing == 0)
//                {
//                    if(vfd.debug_picnt > 0)
//                        vfd.debug_picnt = DEBUG_PLOT_POINTS;
//                    return;
//                }
                
                vfd.debug_data1[vfd.debug_picnt] = DPLL3P.Iac.In.a;
                vfd.debug_data2[vfd.debug_picnt] = DPLL3P.IabIdq.d;
                vfd.debug_data3[vfd.debug_picnt] = DPLL3P.IabIdq.q;
         
            }
            else if(vfd.debug_data_index == 12)
            {
                if(pfc.PFC_Runing == 0)
                {
                    if(vfd.debug_picnt > 0)
                        vfd.debug_picnt = DEBUG_PLOT_POINTS;
                    
                    return;
                }
                
                vfd.debug_data1[vfd.debug_picnt] = DPLL3P.Iac.In.a;
                vfd.debug_data2[vfd.debug_picnt] = PFCVAR.PIId.Out;
                vfd.debug_data3[vfd.debug_picnt] = PFCVAR.L;
          
            }
            else if(vfd.debug_data_index == 13)
            {
//                if(pfc.PFC_Runing == 0)
//                {
//                    if(vfd.debug_picnt > 0)
//                        vfd.debug_picnt = DEBUG_PLOT_POINTS;
//                    return;
//                }
                
                vfd.debug_data1[vfd.debug_picnt] = DPLL3P.Iac.In.a;
                vfd.debug_data2[vfd.debug_picnt] = PFCVAR.Vout.d;            
                vfd.debug_data3[vfd.debug_picnt] = PFCVAR.Vout.q;
              
              
            }
            else if(vfd.debug_data_index == 14)
            {
                vfd.debug_data1[vfd.debug_picnt] = DPLL3P.DPLLDDSRF.dpdelpf;
                vfd.debug_data2[vfd.debug_picnt] = DPLL3P.Vbus.In;
                vfd.debug_data3[vfd.debug_picnt] = DPLL3P.DPLLDDSRF.qpdelpf;
  
               
            }
//            else if(vfd.debug_data_index == 15)
//            {
//                if(pfc.PFC_Runing == 0)
//                {
//                    if(vfd.debug_picnt > 0)
//                        vfd.debug_picnt = DEBUG_PLOT_POINTS;
//                    return;
//                }
//                vfd.debug_data1[vfd.debug_picnt] = PFCVAR.Vout.alpha;
//                vfd.debug_data2[vfd.debug_picnt] = DPLL3P.Vac.In.a;
//                vfd.debug_data3[vfd.debug_picnt] = DPLL3P.DPLLDDSRF.theta;
//                vfd.debug_data4[vfd.debug_picnt] = DPLL3P.Iac.In.a;
//            }
//            else if(vfd.debug_data_index == 16)
//            {
//                vfd.debug_data1[vfd.debug_picnt] = rt_pin_read(FS_DR_PIN)   ? 100 : 0;
//                vfd.debug_data2[vfd.debug_picnt] = rt_pin_read(FS_DR_B_PIN) ? 100 : 0;
//                vfd.debug_data3[vfd.debug_picnt] = PFCVAR.PIVbus.Ref;
//                vfd.debug_data4[vfd.debug_picnt] = DPLL3P.Vbus.In;
//         
//            }
//            else if(vfd.debug_data_index == 17)
//            {
//                vfd.debug_data1[vfd.debug_picnt] = PFCVAR.Vout.alpha;
//                vfd.debug_data2[vfd.debug_picnt] = -DPLL3P.Iac.In.a;
//                vfd.debug_data3[vfd.debug_picnt] = -DPLL3P.Iac.In.b;
//                vfd.debug_data4[vfd.debug_picnt] = -DPLL3P.Iac.In.c;
//             
//            }
//            else if(vfd.debug_data_index == 18)
//            {
//                vfd.debug_data1[vfd.debug_picnt] = DPLL3P.IabIdq.d;
//                vfd.debug_data2[vfd.debug_picnt] = DPLL3P.IabIdq.q;
//                vfd.debug_data3[vfd.debug_picnt] = PFCVAR.PIId.Out;
//                vfd.debug_data4[vfd.debug_picnt] = PFCVAR.PIIq.Out;
//        
//            }
//            else if(vfd.debug_data_index == 19)
//            {
//                vfd.debug_data1[vfd.debug_picnt] = pfc_adc_value[0];
//                vfd.debug_data2[vfd.debug_picnt] = pfc_adc_value[1];
//                vfd.debug_data3[vfd.debug_picnt] = pfc_adc_value[2];
//                vfd.debug_data4[vfd.debug_picnt] = pfc_adc_value[3];
//            }
//            else if(vfd.debug_data_index == 20)
//            {
//                vfd.debug_data1[vfd.debug_picnt] = PFCVAR.SVPWM.CntPhA;
//                vfd.debug_data2[vfd.debug_picnt] = PFCVAR.SVPWM.CntPhB;
//                vfd.debug_data3[vfd.debug_picnt] = PFCVAR.SVPWM.CntPhC;
//                vfd.debug_data4[vfd.debug_picnt] = PFCVAR.SVPWM.CntPhC;
//            }
            else
                return;
            
            vfd.debug_picnt++;  
        }
    }
    #endif
}
/*******************************************************************************
* Function Name  : void tim8_break_isr(void)
* Description    : over cur, close pwm
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void tim8_break_isr(void)
{
    static uint32_t tick = 0;
    /* over current .*/
    LL_TIM_DisableIT_BRK(TIM8);

    hardware_irq_pin.f_ipm_pfc_cnt++;
    hardware_irq_pin.f_tim8_breakin_flag = RT_TRUE;
    dio_table[dio_pin_find(F_IPM_PFC_PIN)].irq_delay = IRQ_DELAY_DEFAULT;
    if(rt_tick_get() - tick > 100)
    {
//        rt_kprintf("[Tick %d] tim8 breakin. pfc[%d][%d][%d] boost[%d][%d][%d]\r\n", rt_tick_get() / 2,
//                    vfd.ctrl.start_pfc,vfd.ctrl.kmon2,vfd.ctrl.fs_pfc,
//                    vfd.ctrl.start_boost,vfd.ctrl.kmon1,vfd.ctrl.fs_boost);      
    }
    
    #ifndef DEBUG_PFC_DISABLE_SOFT_PROTECT
    if(vfd.ctrl.start_pfc)
    {
        pfc_fault_stop();
    }
    else if(vfd.ctrl.start_boost)
    {
        DcdcPwmStop();
    }
    #endif
    
    tick = rt_tick_get();
}

