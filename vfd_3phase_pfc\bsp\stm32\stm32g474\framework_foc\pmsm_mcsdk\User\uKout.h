
/*
 * File      : uLed.h
 *  
 *  
 *
 *  
 *  
 *  
 *
 * Change Logs:
 * Date           Author       Notes
 * 2019-07-22     Group       
 */

#ifndef __UKOUT_H__
#define __UKOUT_H__

//#include <rtthread.h>
//#include <string.h>
#include "stm32g4xx_hal.h"

/* Private typedef -----------------------------------------------------------*/


/* Private define ------------------------------------------------------------*/


void KOut_init(void);
void Kin_On(void);
void Kin_Off(void);
void Kflt_On(void);
void Kflt_Off(void);

void KACin_On(void);
void KACin_Off(void);

void KDCin_On(void);
void KDCin_Off(void);

#endif


/******************* (C) COPYRIGHT 2022 Group *****END OF FILE****/

