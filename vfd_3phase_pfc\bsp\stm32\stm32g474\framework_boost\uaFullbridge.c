
/******************** (C) COPYRIGHT 2022 bus-lan ********************
* File Name          : uaSysCharge.c
* Author             :
* Version            : V1.0
* Date               :
* Description        :
********************************************************************************/
#include "uaFullbridge.h"
#include "uApp.h"
#define DBG_TAG "boost"
#define DBG_LVL DBG_LOG
#include <rtdbg.h>

static void PIDClear(void);
static int32_t uaSysPIDCtrl(void);

extern ADC_HandleTypeDef hadc4;
extern TIM_HandleTypeDef htim20;

static rt_err_t boost_dcdc_init(void);
static rt_err_t boost_dcdc_start(void);
static rt_err_t boost_dcdc_stop(void);
static rt_err_t boost_dcdc_control(int cmd, void *arg);
static rt_err_t boost_dcdc_machine_task(void);
static uint8_t disp_ctrl;

static const struct rt_boost_ops ops =
{
    .init   = boost_dcdc_init,
    .start  = boost_dcdc_start,
    .stop   = boost_dcdc_stop,
    .machine_task = boost_dcdc_machine_task,
    .control = boost_dcdc_control,
};

rt_boost_t    boost =
{
    .ops = &ops,

};

static rt_err_t boost_dcdc_init(void)
{
    static uint8_t first = 1;
    
    boost.Vbus_ref   = 6000;
    boost.Iin_ref    =  130;

    rt_memset((uint8_t *)&boost._OutVPidCB, 0, sizeof(boost._OutVPidCB));

    boost._OutVPidCB.CtrlOutMax = 500;
    boost._OutVPidCB.CtrlOutMin = -50;

    boost._OutVPidCB.ErrMin     = 0;
    boost._OutVPidCB.Kdiv       = 1000;

    boost._OutVPidCB.Kp = 150;
    boost._OutVPidCB.Ki = 10;
    boost._OutVPidCB.Kd = 0;

    boost.gcSoftStartStep = 5;   
    
    if(first)
    {
        first = 0;
         MX_TIM20_Boost_Init();
    }
    
    boost.init_flag = 1;

    return 0;
}
static rt_err_t boost_dcdc_start(void)
{
    if (!boost.init_flag)
        boost.ops->init();

    if (boost.state == BOOST_STOP)
        boost.state = BOOST_START;

    return 0;
}

static rt_err_t boost_dcdc_stop(void)
{
    if (!boost.init_flag)
        boost.ops->init();

    boost.state = BOOST_STOP;

    return 0;
}

static rt_err_t boost_dcdc_control(int cmd, void *arg)
{
    if (!boost.init_flag)
        boost.ops->init();

    switch (cmd)
    {
    case BOOST_CMD_SET_VOLTREF:
        boost.Vbus_ref = (uint32_t)arg;
        break;

    case BOOST_CMD_SET_CURREF:
        boost.Iin_ref = (uint32_t)arg;
        break;

    case BOOST_CMD_GET_STATE:
        *(uint8_t *)arg = boost.state;
        break;
    
    case BOOST_CMD_UPDATE_VOLT_FB:
        boost._OutVPidCB.FeedbackValue = (int32_t)arg;
        break;
    
    case BOOST_CMD_UPDATE_VIN:
        boost.Vin_ads = (uint32_t)arg;
        break;
    

    case BOOST_CMD_INIT_TIM8:
        MX_TIM20_Boost_Init();
        break;
    default:
        break;


    }

    return 0;
}

const char *(boost_state[]) = {
    "STOP      ",
    "START     ",
    "PRECHARGE ",
    "SOFT_STARTUP",
    "RUN ",
    "ERR  ",
};      

int vfd_get_dcdc_breakflag(void)
{
    uint8_t ipm_irq_flag =  (!DIO_READ_BIT(F_IPM_DC_PIN)   || (DIO_IRQ_DELAY(F_IPM_DC_PIN) > 0))                                    ||
                    (vfd.ctrl.start_boost && (!DIO_READ_BIT(OVP_P_BUS_PIN)|| (DIO_IRQ_DELAY(OVP_P_BUS_PIN) > 0)))    ||
                    LL_TIM_IsActiveFlag_BRK(TIM20) || LL_TIM_IsActiveFlag_BRK2(TIM20);

    return ipm_irq_flag;
}

static rt_err_t boost_dcdc_machine_task(void)
{
    static uint8_t last_st = 0xff;
    static uint32_t cycle_Tick_1ms = 0;
    static uint16_t lastVbus = 0;

    if (!boost.init_flag)
        boost.ops->init();

    if (abs(lastVbus - boost._OutVPidCB.FeedbackValue) >= 500)
    {
        lastVbus = boost._OutVPidCB.FeedbackValue;
    }

    
    cycle_Tick_1ms++;

    if(vfd.ad.dc_vinbus < 770)
        boost.Vbus_ref = 5600;
    else
        boost.Vbus_ref = 6000;
    
    #ifdef VFD_TEST_DEBUG
    if(!vfd.ctrl.start_boost && (vfd.manual.dcdc_duty  != 0))
    {
        DcdcPwmSetCompare(vfd.manual.dcdc_duty * 10);
        DcdcPwmStart();
        BOOST_FS_OUT(1);
        vfd_vbus_kmon2_set(KMON2_CHNN_BOOST,1);
        return 0;
    }
    #endif
    
    if (vfd_get_dcdc_breakflag())
    {
        if(boost.state != BOOST_ERR)
            vfd.diag_latch = 0x3;
        DcdcPwmStop();
        boost.state = BOOST_ERR;
    }
    
    
    if (last_st != boost.state)
    {
        LOG_I("state:%d BK%d %s ",boost.state,hardware_irq_pin.f_tim20_breakin_flag,boost_state[boost.state]);
        last_st = boost.state;
        boost.machine_cnt = 0;
    }
    
    
    switch (boost.state)
    {
    case BOOST_ERR:
        BOOST_FS_OUT(0);
        boost.machine_cnt++;
        boost._OutVPidCB.SetValue = 0;
        if(boost.machine_cnt >= BOOSTC_ERR_RESET_TICK)
        {
            LL_TIM_ClearFlag_BRK2(TIM20) ;
            LL_TIM_ClearFlag_BRK(TIM20)  ;
            boost.softStartErrFlag = 0;
            hardware_irq_pin.f_tim20_breakin_flag = 0;
            
            vfd.diag_latch = 0x0;
            boost.state = BOOST_STOP;
        }
        
        break;
    case BOOST_STOP:
        boost.softStartErrFlag = 0;
        BOOST_FS_OUT(0);
        boost._OutVPidCB.SetValue = 0;
        boost.machine_cnt++;
        if(boost.machine_cnt >= 100)
        {
            BOOST_KMON_OUT(0);
            vfd_vbus_kmon2_set(KMON2_CHNN_BOOST,0);
        }
        break;
    case BOOST_START:
        boost._OutVPidCB.SetValue = 0;
    
        #ifndef VFD_TEST_DEBUG
        if(boost.Vin_ads >= BOOST_VIN_PRECHARGE)
        #endif
        {
            if((rt_tick_get() > RT_TICK_PER_SECOND*5))
            {
                boost.state = BOOST_PRECHARGE;
                vfd_vbus_kmon2_set(KMON2_CHNN_BOOST,1);
            }
        }
        break;
    case BOOST_PRECHARGE:

        if (boost.machine_cnt >= 1500)
        {
            boost._OutVPidCB.SetValue = boost._OutVPidCB.FeedbackValue;
            boost.state = BOOST_SOFT_STARTUP;
        }
        else if (boost.machine_cnt >= 1000)
        {
            BOOST_KMON_OUT(1);
            boost.machine_cnt++;
        }
        else if ( (boost.machine_cnt < 1000)
                #ifndef VFD_TEST_DEBUG
                &&(boost.Vin_ads >= BOOST_VIN_MIN) 
                #endif
                 
        )
        {
            boost.machine_cnt++;
        }

        break;
    case BOOST_SOFT_STARTUP:
        
        #ifndef VFD_TEST_DEBUG
        if((boost.modulation_out >= 100) && 
            (boost._OutVPidCB.FeedbackValue < 500)
        
        )
        {
            boost.softStartErrFlag = 1;
            boost.state = BOOST_ERR;
        }
        else if(boost.machine_cnt >= 2000)
        {
            boost.softStartErrFlag = 1;
            boost.state = BOOST_ERR;
        }
        else if((boost.machine_cnt >= 1000) && (boost._OutVPidCB.FeedbackValue < 1000))
        {
            boost.softStartErrFlag = 1;
            boost.state = BOOST_ERR;
        }
        #else
        if(boost.machine_cnt >= 1000)
        {
            boost.state = BOOST_RUN;
        }
        #endif
        else if(boost._OutVPidCB.FeedbackValue < boost.Vbus_ref)
        {
            BOOST_FS_OUT(1);
            boost.machine_cnt++;
        }
        else if(boost._OutVPidCB.FeedbackValue >= boost.Vbus_ref)
        {
            boost.state = BOOST_RUN;
        }
        
        break;
    case BOOST_RUN:

        boost.machine_cnt++;
        
        break;
    default:
        boost.state = BOOST_STOP;
        break;

    }

    //if (cycle_Tick_1ms % 2 == 0)
    mUapDcdcPwmExecute(boost.state);
    
//    if (disp_ctrl)
//    {
//        toolbox_dma_printf_ascii("b:%d,%d,%d\r\n", boost._OutVPidCB.FeedbackValue,boost.modulation_out,boost.state);
//    }
    
    return 0;
}


extern uint8_t gcTestPwmFlag;

extern uint16_t pwm_max;

int32_t mUapDcdcPwmExecute(uint8_t state)
{
    static uint8_t lastSt = 0;

    if (gcTestPwmFlag != RT_NULL)    return 0;


    if (state != lastSt)
    {
        lastSt = state;
        switch (state)
        {
        case BOOST_START:
            boost.modulation = 0;
            return 0;
        case BOOST_PRECHARGE:
            break;
        case BOOST_SOFT_STARTUP:
            DcdcPwmSetCompare(1);
            DcdcPwmStart();
            
            break;
        case BOOST_RUN:

            break;
        case BOOST_STOP:
        case BOOST_ERR:
        default:
            DcdcPwmStop();
            boost.modulation = 0;

            return 0;
        }
    }

    if ((state == BOOST_START)             ||
            (state == BOOST_PRECHARGE)     ||
            (state == BOOST_STOP)          ||
        (state == BOOST_ERR)
       )
    {
        DcdcPwmStop();
        PIDClear();
        return 0;
    }
    
    
    if(boost._OutVPidCB.SetValue >= boost.Vbus_ref)
    {
        boost._OutVPidCB.SetValue = boost.Vbus_ref;
    }
    else
    {
        boost._OutVPidCB.SetValue += boost.gcSoftStartStep;
    }

    return (0);
}

uint16_t boost_pid_div = 20; /* base period : 67 */
void Fullbridge_DCDC_Pid(void)
{ 
    static uint32_t cnt = 0;
    
    cnt++;
    
    if( boost.start_pwm             &&
       (cnt % boost_pid_div == 0)   &&
       ((BOOST_SOFT_STARTUP == vfd.boost->state) || (BOOST_RUN ==  vfd.boost->state))
    )
   {
       boost.modulation = uaSysPIDCtrl();
    
        //限调节量
        if(boost.modulation > 500)
            boost.modulation = 500;
        else if(boost.modulation < -500)
            boost.modulation = -500;
    
        #ifdef VFD_TEST_DEBUG
        if(vfd.manual.dcdc_duty == 0)   
        #endif
        DcdcPwmSetCompare(boost.modulation);
    }
    else
    {
        
    }
       
}


static void PIDClear(void)
{
    boost._OutVPidCB.calCounts = 0;
    boost._OutVPidCB.Err[0]    = 0;
    boost._OutVPidCB.Err[1]    = 0;
    boost._OutVPidCB.Err[2]    = 0;
    boost._OutVPidCB.CtrlKi = 0;
    boost._OutVPidCB.CtrlKp = 0;
    boost._OutVPidCB.CtrlKd = 0;
    boost._OutVPidCB.CtrlOut = 0;
    boost.modulation = 0;
        boost.modulation_out = 0;
}


/*******************************************************************************
* Function Name  : static int32_t uaSysPIDCtrl(void)
* Description    :
*
* Input          : - none.
* Output         : None
* Return         : None
*******************************************************************************/
static int32_t uaSysPIDCtrl(void)
{
    int32_t _ctrlOut = 0;

    uaPID_Ctrl(&boost._OutVPidCB);

    _ctrlOut = boost._OutVPidCB.CtrlOut ;

    return _ctrlOut;
}

void cmd_boost(int argc, char **argv)
{
    if(!rt_strcmp(argv[1],"disp"))
    {
        disp_ctrl = 1 - disp_ctrl;
        if(disp_ctrl == 0)
            toolbox_resume_rtkprintf();
    } 
}



MSH_CMD_EXPORT_ALIAS(cmd_boost,boost, boost set);
/******************* (C) COPYRIGHT 2021 *****END OF FILE****/



