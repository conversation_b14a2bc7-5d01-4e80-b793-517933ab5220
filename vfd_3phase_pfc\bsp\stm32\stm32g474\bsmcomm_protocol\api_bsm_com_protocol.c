/******************** (C) COPYRIGHT 2022     ***********************************
* File Name          : api_bsm_com_protocol.c
* Author             : dfy
* Version            : V1.0
* Date               : 2022.07.07
* Description        : BSM协议API
********************************************************************************/
#include "api_bsm_com_protocol.h"
#include "uapp.h"
#include "crc.h"


//打包结构体变量
static BsmFramePackage BsmPackType={0};


/**
  * @brief 校验函数
  * @param pData（数据）  Size（数据大小）
  * @retval 
  */
unsigned char CheckSum(unsigned char *pData,unsigned short Size)
{
	if (NULL != pData && 0 != Size)
	{
		int i,sum=0;
		for (i=0;i<Size;i++)
			sum+=pData[i];//将每个数相加
		if(sum>0xff)
		{
			sum=~sum;
					  
			sum+=1;            
		}
		return sum&0xff;
	}
	else{}	
}


/**
  * @brief 检查帧开始标志
  * @param pData （数据）
  * @retval 
  */
int api_bsm_check_frame(uint8_t *pData)
{
	if(NULL == pData)
	{
		return -10;
	}
	
	//----------------------------------------------------------------------------
	// 1.检查帧开始标志位 |- BSM -|
	//----------------------------------------------------------------------------
    if(pData[0] == 'B' && pData[1] == 'S' && pData[2] == 'M')
        return 0;
    else
        return -1;
}


/**
  * @brief 检查数据流的设备地址
  * @param 设备地址 数据流
  * @retval 
  */
int api_bsm_check_dev_addr(uint8_t DevAddr,const uint8_t *pData)
{
	if(NULL == pData)
	{
		return -10;
	}
	
	//----------------------------------------------------------------------------
	// 1.检查设备地址
	//----------------------------------------------------------------------------
    if(DevAddr == pData[3] || (!vfd.modbus_id_lock && (DEFAULT_SERIAL_COM_ID == pData[3])))
        return 0;
    else
        return -1;
}

/**
  * @brief 检查数据流的设备地址 和 CRC校验
  * @param 设备地址 数据流
  * @retval -1 设备地址错误  -2 CRC校验失败
  */
int api_bsm_check_addr_crc(uint8_t Dev_Addr,const uint8_t *pRxBuff,uint16_t Size)
{
	int ret = 0; //返回值
	
	if(NULL == pRxBuff || 0 == Size)
		return -10;
	
	//----------------------------------------------------------------------------
	// 1. 检查设备地址 == 当前设备地址？
	//----------------------------------------------------------------------------
	ret = api_bsm_check_dev_addr(Dev_Addr, pRxBuff);
	
	if(-1 == ret)
	{
		// - 设备地址不是当前设备地址
		//rt_kprintf("BSM DEV ADDR ERROR\n");
		return -1;
	}
	BsmFramePackage *pBsmFP = (BsmFramePackage *)pRxBuff;
    uint16_t crc_size = pBsmFP->Data_Lenth + 9 + 2;
    
    if(crc_size >= 2048)
        return -2;
    
	//----------------------------------------------------------------------------
	// 2. 校验处理
	//----------------------------------------------------------------------------
#if (1 == USE_CRC16)
	
	uint16_t crc = CRC16((uint8_t *)pRxBuff, (crc_size - 2));
	uint8_t crc_h = crc >> 8;
    uint8_t crc_l = crc;
	if(crc_l != pRxBuff[crc_size - 2]  && crc_h != pRxBuff[crc_size - 1])
#else
	uint8_t crc = CheckSum(pRxBuff, Size -1);
	if(crc != pRxBuff[Size - 1])
#endif	
	{
		rt_kprintf("BSM CRC ERROR %d ->",Size);//校验错误
        for(int i = 0; i < 16;i++)
        {
            rt_kprintf(" %02X,",pRxBuff[i]);
        }
        rt_kprintf("\r\n");
		return -2;
	}
	return 0;
}

/**
  * @brief 检查数据流的设备地址 和 CRC校验
  * @param 设备地址 数据流
  * @retval -1 设备地址错误  -2 CRC校验失败
  */
int api_bsm_check_crc(const uint8_t *pRxBuff,uint16_t Size)
{
	int ret = 0; //返回值
	
	if(NULL == pRxBuff || 0 == Size)
		return -10;
	
	//----------------------------------------------------------------------------
	// 2. 校验处理
	//----------------------------------------------------------------------------
#if (1 == USE_CRC16)
	
	uint16_t crc = CRC16((uint8_t *)pRxBuff, (Size - 2));
	uint8_t crc_h = crc >> 8;
    uint8_t crc_l = crc;
	if(crc_l != pRxBuff[Size - 2]  && crc_h != pRxBuff[Size - 1])
#else
	uint8_t crc = CheckSum(pRxBuff, Size -1);
	if(crc != pRxBuff[Size - 1])
#endif	
	{
		rt_kprintf("BSM CRC ERROR\n");//校验错误
		return -2;
	}
	return 0;
}

/**
  * @brief 封装数据流
  * @param 设备地址 /功能帧 /数据长度 /数据偏移量 /数据内容 /发送数据缓冲区
  * @retval 数据长度
  */
uint16_t api_bsm_com_package(uint8_t dev_addr,
							 uint8_t func_frame,
							 uint16_t data_lenth,
							 uint16_t data_offset,
							 void *pSendData,
							 uint8_t * sendBuff)
{
	if(data_lenth)
	{
		if (NULL == pSendData || NULL == sendBuff)
		{
			return 0;
		}
	}
	
	
	uint16_t sendSize = 0;
	uint8_t *pData = (uint8_t*)pSendData;
	
	rt_memset(sendBuff,0,data_lenth+10);     //发送数据缓存区清0
	
	//----------------------------------------------------------------------------
	// 1.协议帧头数据
	//----------------------------------------------------------------------------
	BsmPackType.StartOfFrame1 = 'B';          	//帧开始第1个标志
	BsmPackType.StartOfFrame2 = 'S';          	//帧开始第2个标志
	BsmPackType.StartOfFrame3 = 'M';          	//帧开始第3个标志
	
	BsmPackType.Dev_Addr 	  = dev_addr;       //设备地址
	BsmPackType.Frame_Fun 	  = func_frame;		//帧功能号
	BsmPackType.Data_Lenth 	  = data_lenth;		//数据长度
	BsmPackType.Data_offset   = data_offset;  	//数据偏移量
	
	//----------------------------------------------------------------------------
	// 2.拷贝帧头数据
	//----------------------------------------------------------------------------
	rt_memcpy(sendBuff, &BsmPackType, 9);
	BsmPackType.pData = (uint8_t*)pSendData;
	
	//----------------------------------------------------------------------------
	// 3.拷贝需要发送用户数据
	//----------------------------------------------------------------------------
	for(uint16_t i = 0; i<data_lenth; i++)
	{
		sendBuff[i+9] = pData[i];
	}
	
	//----------------------------------------------------------------------------
	// 4.计算校验码
	//----------------------------------------------------------------------------
#if (1 == USE_CRC16)
	
	uint16_t crc = CRC16(sendBuff, (data_lenth + 9));
	uint8_t crc_h = crc >> 8;
    uint8_t crc_l = crc;
	
	sendBuff[data_lenth+9] = crc_l;
	sendBuff[data_lenth+10] = crc_h;
	BsmPackType.Crc = crc;
#else 
	BsmPackType.CRC = CheckSum(sendBuff, data_lenth+9);
	sendBuff[data_lenth+9] = BsmPackType.CRC;
#endif	
	
	//----------------------------------------------------------------------------
	// 5.该帧数据长度
	//----------------------------------------------------------------------------
#if (1 == USE_CRC16)
	sendSize = data_lenth+11;
#else 
	sendSize = data_lenth+10;
#endif
	
	return sendSize;
}
