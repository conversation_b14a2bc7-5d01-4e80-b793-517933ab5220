/******************** (C) COPYRIGHT 2012 BSM ********************
* File Name          : task_display.c
* Author             : ���ڴ�ӡ��ʾ��Ϊ
* Version            : V1.0
* Date               :
* Description        :
********************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include <rtthread.h>
#include "user_ds.h"
#include "task_display.h"
#include "api_mvb.h"
#include "bsp_rs485.h"
#include "Bsp_ledhex.h"

__IO uint8_t _show_cmd_ = 0;
void Sterm_display(void);
void printf_data(u8 cmd);
void printf_head(u8 clr);
void printf_line(u8 len);


extern struct rt_semaphore sem_systask_10ms;
extern TYPE_LP_PRT_CFG    MVB_LPort_CFG[LPort_NUM_Max];
extern void gotoxy(u8 x, u8 y);

/*******************************************************************************
* Function Name  : void Bsp_display_task(void* paramemter)
* Description    :
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void Bsp_display_task(void *paramemter)
{
    static  u32  TimingDelay     = 0;


    //-------------------------------------------------------------------------
    // ���������� [ sem_systask_10ms ]�ź����Ŀ���  <10ms>
    //-------------------------------------------------------------------------
    for (;;)
    {
        rt_sem_take(&sem_systask_10ms, RT_WAITING_FOREVER);

        TimingDelay++;
        //-----------------------------------------------------------
        // ָʾ���߼�                          Ԥ�ƣ�10 * 10 = 100ms
        //-----------------------------------------------------------
        if (TimingDelay % 10 == 0)
        {
            System_Led_Logic();
        }

        if (TimingDelay % 2 == 0)
        {
            Sterm_display();
        }

    }
}


void Sterm_display(void)
{

    static u8 printtimes = 0;
    static u8 times = 0;
    u8 i;
    u8 data_slen;

    data_slen = GWDS.MVB_PortNum + 2 + 2 ; //+ 2;

    //��ʾһ��
    if (_show_cmd_ == 1)
    {
        printf_head(1);

        for (i = 0; i < data_slen; i++)
        {
            printf_data(i);
        }
        _show_cmd_ = 0;
    }

    //������ʾ
    if (_show_cmd_ == 2)
    {
        printf_head(1);

        times++;
    }

    //ֹͣ��ʾ
    if (_show_cmd_ == 5)
    {
        printf_head(0);
        times = 0;
        rt_kprintf("\r\nMVB GateWay System Information Stop Display\r\n");
    }

    //ͳ����������
    if (_show_cmd_ == 6)
    {
        GWDS.mvb.ErrCnt = 0;
        GWDS.mvb.WrnCnt = 0;
        GWDS.mvb.SinkBothCnt = 0;
        GWDS.mvb.SinkBCnt = 0;
        GWDS.mvb.SinkACnt = 0;

        GWDS.rx_rs485_sumcnt = 0;
        GWDS.rx_err_times = 0;
        GWDS.tx_rs485_sumcnt = 0;
        GWDS.rx_err_cnt = 0;
        GWDS.rx_cframe_cnt = 0;

        printf_head(1);
    }

    _show_cmd_ = 0;

    if (times)
    {
        times++;

        if (times % 5 == 0)
        {
            printf_data(printtimes);

            printtimes++;
            if (printtimes > data_slen) printtimes = 0;
        }

        if (times == 0) times = 1;

//      rt_kprintf(GOTO(41,1));
//      rt_kprintf("cpu use percent: %d.%d", PDS(CPU)->cpu_usage_major, PDS(CPU)->cpu_usage_minor);
    }
}


void printf_head(u8 clr)
{
    u16 i;

    rt_kprintf(CLR);

    rt_kprintf("\r\n");
    rt_kprintf(GOTO(2, 20));
    rt_kprintf("        Welcome To Use LJPT2 HVAC GW V%x -- S%d(Designed BSM   -- %s)               ", GW_VER, SUB_VER, __DATE__); //20140111

    rt_kprintf("\r\n");

    if (clr == 0)    return;

    rt_kprintf(GOTO(4, 1));
    printf_line(32);

    rt_kprintf("       |  Port_ID  |");
    for (i = 0; i < 32; i++) rt_kprintf("%02d|", i);
    rt_kprintf("\r\n");

    printf_line(32);

    for (i = 0; i < (GWDS.MVB_SrceNum); i++)
    {
        rt_kprintf("       | MVBSrce%03x|", MVB_LPort_CFG[i].prt_addr);
        rt_kprintf(" \r\n");
        printf_line(32);
    }

    for (i = (GWDS.MVB_SrceNum); i < GWDS.MVB_PortNum; i++)
    {
        rt_kprintf("       | MVBSink%03x|", MVB_LPort_CFG[i].prt_addr);
        rt_kprintf(" \r\n");
        printf_line(32);
    }

    rt_kprintf("       | RS485F1 TX|");
    rt_kprintf(" \r\n");
    printf_line(32);

    rt_kprintf("       | RS485F2 TX|");
    rt_kprintf(" \r\n");
    printf_line(32);

    rt_kprintf("       | RS485F1 RX|");
    rt_kprintf(" \r\n");
    printf_line(32);

    rt_kprintf("       | RS485F2 RX|");
    rt_kprintf(" \r\n");
    printf_line(32);

}

void printf_line(u8 len)
{
    uint16_t i;
    rt_kprintf("       +-----------+");
    for (i = 0; i < len; i++) rt_kprintf("--+");
    rt_kprintf("\r\n");
}

void printf_data(u8 cmd)    //20140817
{
#define X_START 21
#define Y_START 7

    uint16_t i;
    vu8 *ptru8;

    if (cmd < GWDS.MVB_SrceNum)
    {
        gotoxy((Y_START + cmd * 2), X_START);

        if (GWDS.MVBSend_Bigend == 1)
        {
            for (i = 0; i < (MVB_LPort_CFG[cmd].size / 2); i++)
            {
                rt_kprintf("%02x|", mvb_data.tp_srce[cmd][i * 2 + 1]);
                rt_kprintf("%02x|", mvb_data.tp_srce[cmd][i * 2]);
            }
        }
        else
        {
            for (i = 0; i < MVB_LPort_CFG[cmd].size; i++)
            {
                rt_kprintf("%02x|", mvb_data.tp_srce[cmd][i]);
            }
        }


    }
    else if (cmd < GWDS.MVB_PortNum)
    {
        gotoxy((Y_START + cmd * 2), X_START);

        for (i = 0; i < MVB_LPort_CFG[cmd].size; i++)
        {
            rt_kprintf("%02X|", mvb_data.tp_sink[cmd - GWDS.MVB_SrceNum][i]);
        }
        if (SinkSt[cmd - GWDS.MVB_SrceNum].Error == 1)
            rt_kprintf("ER|");
        else
            rt_kprintf("OK");
    }
    else if (cmd == GWDS.MVB_PortNum)
    {
        ptru8 = (u8 *)&RODS_D;

        gotoxy((Y_START + cmd * 2), X_START);

        for (i = 0; i < 32; i++)
        {
            rt_kprintf("%02X|", *ptru8++);
        }

        gotoxy((Y_START + cmd * 2 + 2), X_START);

        for (i = 32; i < RS485_TX_DFRAME_SIZE; i++)
        {
            rt_kprintf("%02X|", *ptru8++);
        }
    }
    else if (cmd == (GWDS.MVB_PortNum + 1))
    {
        ptru8 = (u8 *)&RIDS_D;

        gotoxy((Y_START + cmd * 2 + 2), X_START);
        for (i = 0; i < 32; i++)
        {
            rt_kprintf("%02X|", *ptru8++);
        }

        gotoxy((Y_START + cmd * 2 + 4), X_START);
        for (i = 32; i < RS485_RX_DFRAME_SIZE; i++)
        {
            rt_kprintf("%02X|", *ptru8++);
        }
    }

    else if (cmd == (GWDS.MVB_PortNum + 2))
    {
        gotoxy(36, 8);

        rt_kprintf("HEX = %1x; CarNum = %2d; MVBDevAddr = 0x%2x; PwrUpcnt = %6d; ", GWDS.hexdata, GWDS.carnum, GWDS.mvb.dev_address, GWDS.PwrUpCnt);
        rt_kprintf("Tx=%6d; ", GWDS.tx_rs485_sumcnt);
        rt_kprintf("Rx=%6d; ", GWDS.rx_rs485_sumcnt);
        if (GWDS.Rs485_Rx_Err)rt_kprintf(" Rs485 Offline **");
        else    rt_kprintf(" Rs485 Online");
        rt_kprintf("\r\n");
    }
    else if (cmd == (GWDS.MVB_PortNum + 3))
    {
        gotoxy(38, 8);
        rt_kprintf("MVBABCnt = %8d ; MVBACnt = %8d ; MVBBCnt = %8d ; MVBErrCnt = %5d ; MVBWrnCnt = %5d ;", GWDS.mvb.SinkBothCnt, GWDS.mvb.SinkACnt, GWDS.mvb.SinkBCnt, GWDS.mvb.ErrCnt, GWDS.mvb.WrnCnt);
        if (GWDS.Mvb_Rx_Err)rt_kprintf("  MVB Error **");
        else rt_kprintf(" -- MVB OK -- ");
    }
}

void show_one(void)
{
    _show_cmd_ = 1;
}

void show_start(void)
{
    _show_cmd_ = 2;
}

void show_stop(void)
{
    _show_cmd_ = 5;
}

void mvbclr(void)
{
    _show_cmd_ = 6;
}



#include "finsh.h"
//----------------------------------------------------------------
// �������̨����
//----------------------------------------------------------------
FINSH_FUNCTION_EXPORT(show_one, All Data Display One Time)
FINSH_FUNCTION_EXPORT(show_start, All Data Display Start)
FINSH_FUNCTION_EXPORT(show_stop, All Data Display Stop)
FINSH_FUNCTION_EXPORT(mvbclr, All Data CNT Clr)





/******************* (C) COPYRIGHT 2012 Group *****END OF FILE****/
