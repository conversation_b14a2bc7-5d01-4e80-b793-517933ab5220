/**
  ******************************************************************************
  * @file    UIIRQHandlerPrivate.h
  * <AUTHOR> Control SDK Team, ST Microelectronics
  * @brief   Proxy include file. To be removed before 5.0.0.
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; COPYRIGHT 2017 STMicroelectronics</center></h2>
  *
  * Licensed under MCD-ST Liberty SW License Agreement V2, (the "License");
  * You may not use this file except in compliance with the License.
  * You may obtain a copy of the License at:
  *
  *        http://www.st.com/software_license_agreement_liberty_v2
  *
  * Unless required by applicable law or agreed to in writing, software 
  * distributed under the License is distributed on an "AS IS" BASIS, 
  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  * See the License for the specific language governing permissions and
  * limitations under the License.
  *
  ******************************************************************************
  */

#include "ui_irq_handler.h"

/******************* (C) COPYRIGHT 2019 STMicroelectronics *****END OF FILE****/
