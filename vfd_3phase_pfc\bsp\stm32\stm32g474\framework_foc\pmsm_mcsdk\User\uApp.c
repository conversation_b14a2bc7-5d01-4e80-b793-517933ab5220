
/******************** (C) COPYRIGHT 2012 BSM ********************
* File Name          : UserApp.c
* Author             :
* Version            : V1.0
* Date               :
* Description        :
********************************************************************************/

//--------------------------------------------------------------------------
// �����? DAP_MACRO, �����������ݼ��������?
//--------------------------------------------------------------------------
#define  DAP_MACRO


#include "uApp.h"
#include "mc_api.h"
#include "mc_tuning.h"
#include "mc_config.h"
#include "state_machine.h"
#include "usctrl.h"
#include "record_log.h"
#include "intel_flash.h"
#include "mc_config.h"
#include "ptu_rs485.h"


#define DBG_TAG "App"
#define DBG_LVL DBG_LOG
#include <rtdbg.h>
void User_McCtrl(void);

_sys_parm_t sys_param = { 0 };

static uint16_t utick_cnt = 0;
extern run_log_ctr_t g_rlcs;
extern struct xfe_ctl _xfe_ctl_;
extern void rs485_startup_reponse(void);
extern MCT_Handle_t *pMCT[NBR_OF_MOTORS];
extern MCI_Handle_t *pMCI[NBR_OF_MOTORS];
extern void Api_Rs485_Fault(void);
extern void mb_timeout(void);
uint8_t MC_FORCE_SET_FLAG = 0;

extern _para_compensation_t para_compensation_save;
extern _board_ver_   hw_board_ver_save;
extern _file_log_mgr_t file_log_mgr;

extern _intel_flash_data_t  intel_flash_data_rd;
extern _intel_flash_statistic_data_t  intel_flash_statistic_data;

//uint8_t pvd_it_flag = 0;
uint16_t pvd_delay_cnt = 0;
uint8_t sys_inof_save = 0;
uint8_t sys_info_update = 0;


void HAL_PWR_PVDCallback(void)
{
    //pvd_it_flag = 1;
    pvd_delay_cnt = 2000;
    //file_access_disable();
    //rt_kprintf("HAL_PWR_PVDCallback\r\n");
}



/*******************************************************************************
* Function Name  : void UserApp_Tick(void)
* Description    : Ӧ�ò��߼�����ģ��
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void UserApp_Tick(void)
{
    utick_cnt++;
}

/*******************************************************************************
* Function Name  : void UserApp_Init(void)
* Description    : Ӧ�ò��ʼ��?
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void UserApp_Init(void)
{
    //SYSI.APP_Ver = ((UAPP_VERSION & 0xFF) << 24) + ((UAPP_SUBVERSION & 0xFF) << 16)
    //             + ((UAPP_REVISION & 0xFF) << 8) + (UAPP_SUBREVISION & 0xFF);
    Led_init();
    KOut_init();
    User_MotorControl("stop", NULL);
}



void run_log_record()
{
    static uint32_t run_log_update_cnt = 0; //4294967291

    if (rt_tick_get() <= 3 * RT_TICK_PER_SECOND)
    {
        return;
    }

    if (_xfe_ctl_.firmware_upgrade_flag)
    {
        return;
    }

    if (pvd_delay_cnt)
    {
        pvd_delay_cnt--;
        return;
    }

    if (g_rlcs.r_log_flag)
    {
        g_rlcs.r_log_flag = 0;
        g_rlcs.delay_cnt = 300;
    }

    if (g_rlcs.delay_cnt)
    {
        g_rlcs.delay_cnt--;
        return;
    }

    if (!bIN1_Enable)
    {
        return;
    }

    if (file_log_mgr.log_init_bis != 0x07)
    {
        return;
    }

    if (0 == ((++run_log_update_cnt) % 50)) //100
    {
        run_log_update();
        if (0 == run_log_update_cnt % 3000) //6000
        {
            g_rlcs.run_log_save = 1;
        }
    }
}


void fault_log_record(void)
{
    static uint32_t fault_log_update_cnt = 0; //4294967291

    if (rt_tick_get() <= 3 * RT_TICK_PER_SECOND)
    {
        return;
    }

    if (_xfe_ctl_.firmware_upgrade_flag)
    {
        return;
    }

    if (pvd_delay_cnt)
    {
        pvd_delay_cnt--;
        return;
    }

    if (!bIN1_Enable)
    {
        return;
    }

    if (file_log_mgr.log_init_bis != 0x07)
    {
        return;
    }

    if (0 == (fault_log_update_cnt++ % 10))//20
    {
        fault_log_update();
        //rt_kprintf("fault_log_update, fault_log_update_cnt: %d \r\n",fault_log_update_cnt );
    }
}


void sys_param_update(void)
{
    static uint32_t tick_dc = 0;
    static uint32_t tick_ac = 0;
    static uint8_t  dc_pwr_on_flag = 0;
    static uint8_t  ac_pwr_on_flag = 0;
    static uint32_t  tick_mc = 0;
    static uint32_t  pwr_on_tick = 0;
    static uint32_t  info_wr_tick = 0;
    static uint8_t first_rd = 0;
    uint16_t crc16_val = 0;

    if (rt_tick_get() <= 3 * RT_TICK_PER_SECOND)
    {
        return;
    }

    if (_xfe_ctl_.firmware_upgrade_flag)
    {
        return;
    }

    if (pvd_delay_cnt)
    {
        pvd_delay_cnt--;
        return;
    }

    if (file_log_mgr.log_init_bis != 0x07)
    {
        return;
    }

    if (0 == first_rd)
    {
        first_rd = 1;
        sys_info_update = 1;

        Flash_Rd_Data((uint8_t *)&intel_flash_data_rd, sizeof(intel_flash_data_rd));
        crc16_val = getCRC16((uint8_t *)&intel_flash_data_rd, sizeof(intel_flash_data_rd) - 8);
        if (intel_flash_data_rd.crc16_val == crc16_val)
        {
            if (0xb5 == intel_flash_data_rd.compensation_is_set)
            {
                //rt_kprintf("0xb5 == intel_flash_data_rd.compensation_is_set\r\n");
                rt_memcpy((uint8_t *)&para_compensation_save, (uint8_t *)&intel_flash_data_rd.DC110V_COMPEN, sizeof(_para_compensation_t));
            }
            else
            {
                rt_memset((uint8_t *)&para_compensation_save, 0, sizeof(para_compensation_save));
            }

            if (0xc5 == intel_flash_data_rd.board_ver_is_set)
            {
                //rt_kprintf("0xc5 == intel_flash_data_rd.board_ver_is_set\r\n");
                rt_memcpy((uint8_t *)&hw_board_ver_save, (uint8_t *)&intel_flash_data_rd.DCDC_BOARD_VER, sizeof(_board_ver_));
            }
            else
            {
                rt_memset((uint8_t *)&hw_board_ver_save, 0, sizeof(hw_board_ver_save));
            }
        }
        else
        {
            rt_memset((uint8_t *)&para_compensation_save, 0, sizeof(para_compensation_save));
            rt_memset((uint8_t *)&hw_board_ver_save, 0, sizeof(hw_board_ver_save));
        }

        sys_info_rd(&sys_param);
        crc16_val =  getCRC16((uint8_t *)&sys_param, sizeof(sys_param) - 2);

        if (crc16_val == sys_param.crc16_val)
        {
            //rt_kprintf("crc16_val == sys_param.crc16_val\r\n");
            intel_flash_statistic_data.flash_not_empty = 0xd5;
            rt_memcpy((uint8_t *)(&intel_flash_statistic_data.power_on_time), &sys_param, sizeof(sys_param) - 2);
            crc16_val =  getCRC16((uint8_t *)&intel_flash_statistic_data, sizeof(intel_flash_statistic_data) - 8);
            intel_flash_statistic_data.crc16_val = crc16_val;
            Flash_Save_Statistic_Data((uint8_t *)&intel_flash_statistic_data, sizeof(intel_flash_statistic_data));
        }
        else
        {
            Flash_Rd_Statistic_Data((uint8_t *)&intel_flash_statistic_data, sizeof(intel_flash_statistic_data));
            crc16_val = getCRC16((uint8_t *)&intel_flash_statistic_data, sizeof(intel_flash_statistic_data) - 8);
            if (crc16_val == intel_flash_statistic_data.crc16_val)
            {
                if (0xd5 == intel_flash_statistic_data.flash_not_empty)
                {
                    //rt_kprintf("0xd5 == intel_flash_statistic_data.flash_not_empty \r\n");

                    rt_memcpy((uint8_t *)&sys_param, (uint8_t *)&intel_flash_statistic_data.power_on_time, sizeof(sys_param) - 2);
                    crc16_val = getCRC16((uint8_t *)&sys_param, sizeof(sys_param) - 2);
                }
                else
                {
                    rt_memset(&sys_param, 0, sizeof(sys_param));
                }
            }
            else
            {
                // rt_kprintf("crc16_val != sys_param.crc16_val, 0xd5 != intel_flash_statistic_data.flash_not_empty \r\n");
                rt_memset(&sys_param, 0, sizeof(sys_param));
            }
        }

        sys_param.mcu_pwr_on_times++;
    }

    if (0 == (++pwr_on_tick % 3000)) //6000
    {
        sys_param.power_on_time += 1;
    }


    if (Mc_Run ==  PMSMST.ControlState)
    {
        if (0 == (++tick_mc % 3000)) //6000
        {
            sys_param.motor_work_time += 1;
        }
    }
    else
    {
        tick_mc = 0;
    }


    //if (_CS.isAcVReady)
    if (PMSMST.ACvinVoltage  > 1000)
    {
        if (0 == ac_pwr_on_flag)
        {
            ac_pwr_on_flag = 1;
            PMSMST.ac380_act_times++;
            sys_param.AC380_pwr_on_times++;
            sys_info_update = 1;
        }
    }
    else  if (PMSMST.ACvinVoltage < 300)
    {
        ac_pwr_on_flag = 0;
    }


    //if (_CS.isVinbusReady)
    if (PMSMST.VinbusVoltage > 500)
    {
        if (0 == dc_pwr_on_flag)
        {
            dc_pwr_on_flag = 1;
            PMSMST.dc110_act_times++;
            sys_param.DC110_pwr_on_times++;
            sys_info_update = 1;
        }
    }
    else  if (PMSMST.VinbusVoltage < 200)
    {
        dc_pwr_on_flag = 0;
    }

    if ((SYS_EMERG == PMSMST.SysState) && (Mc_Run ==  PMSMST.ControlState))
    {
        if (0 == (++tick_dc % 50)) //100
        {
            double power_1s = (double)(PMSMST.VinbusVoltage *  PMSMST.IinCurrent) / 1000 / 1000 / 3600;
            sys_param.DC110_pwr_consumption +=  power_1s ;
            PMSMST.dc110_consumpation +=  power_1s ;
        }
    }

    if ((SYS_NORMAL == PMSMST.SysState) && (Mc_Run ==  PMSMST.ControlState))
    {
        if (0 == (++tick_ac % 50)) //100
        {
            double power_1s = (double)(PMSMST.OutVoltage *  PMSMST.OutIpamp) / 1000 / 1000 / 3600;
            sys_param.AC380_pwr_consumption  +=     power_1s ;
            PMSMST.ac380_consumpation  +=   power_1s ;
        }
    }
    else
    {
        tick_ac = 0;
    }

    if (!bIN1_Enable)
    {
        return;
    }

    if (0 == (++info_wr_tick % 3000))// || (sys_info_update)))//6000
    {
        if (sys_info_update)
        {
            sys_info_update = 0;
        }
        crc16_val = getCRC16((uint8_t *)&sys_param, sizeof(sys_param) - 2);
        sys_param.crc16_val = crc16_val;
        sys_inof_save = 1;
        //rt_event_send(log_event, SYS_LOG_RECORD_EVEN);
    }
}




/*******************************************************************************
* Function Name  : void UserApp_Task(void)
* Description    : Ӧ�ò��߼�����ģ��
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void UserApp_Task(void)
{
    static uint16_t utick_cnt_pre = 0;
    static uint32_t tick = 0;

    tick++;
    //-------------------------------------------------------------------------
    // 10ms����,��ʱ20s
    //-------------------------------------------------------------------------
    if (utick_cnt_pre != utick_cnt)
    {
        //if(utick_cnt%10 == 0) //10ms
        {
            Led_Logic();
            User_McCtrl();
            Api_Rs485_Fault();
            mb_timeout();
        }
        utick_cnt_pre = utick_cnt;
    }

}

uint32_t gsIaTick;
uint32_t gsIaPeriod;
int16_t  gsIaLast;

int SampleIabcEffectiveValue(const char *name, int16_t val)
{
    DS_Iabc *pI;

    if (!rt_strcmp("Ia", name))
        pI = &MotorPhaseIa;
    else if (!rt_strcmp("Ib", name))
        pI = &MotorPhaseIb;
    else if (!rt_strcmp("Ic", name))
        pI = &MotorPhaseIc;
    else
        return -1;


    if ((pI->gsLast > 0) && (val <= 0))
    {
        pI->gsMaxSampleValue = pI->gsTempMaxSampleValue;
        pI->gsEffectiveValue = pI->gsMaxSampleValue * 707 / 3400;
        pI->gsTempMaxSampleValue = 0;
        pI->gsPeriod = rt_tick_get() - pI->gsTick;
        pI->gsTick = rt_tick_get();
    }

    if ((pI->gsLast == 0) && (val == 0))
    {
        if (pI->gsClearTick++ >= 2)
        {
            pI->gsClearTick = 0;
            pI->gsMaxSampleValue = 0;
            pI->gsEffectiveValue = 0;
            pI->gsTempMaxSampleValue = 0;
            pI->gsPeriod = 0;
            pI->gsTick = 0;
        }
    }
    else
        pI->gsClearTick = 0;

    if (pI->gsTempMaxSampleValue < val)
    {
        pI->gsTempMaxSampleValue = val;
    }

    pI->gsLast = val;

    return 0;
}

uint8_t m_pole_pair_num = 10;
uint8_t m_motor_vendor = 0;
/*******************************************************************************
* Function Name  : void User_McCtrl(void)
* Description    : �������?
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void User_McCtrl(void)
{
    uint8_t tmp_buf[8] = { 0 };
    ab_t    Iab_value;
    qd_t    Iqd_value;////////////////////////////////////
    static uint16_t pwron_cnt = 0;
    static uint16_t mcerrdelay_cnt = 0;
    static uint16_t Ramptime = 0;
    static int16_t  ctrlspeed = 0;
    static uint16_t update_cnt = 0;
    static uint8_t mc_start_cmd = 0;  //////////////////////////////////////////////////////
    static uint8_t mc_stop_cmd = 1;
    static uint16_t mc_start_cnt = 0;
    static uint16_t mc_stop_cnt = 0;
    static uint8_t pole_pair_number = 0;

#ifdef USE_MCSDK
    //-------------------------------------------------------------------------
    // startup mc driver delay 1.5s after poweron
    //-------------------------------------------------------------------------
    if (pwron_cnt < 150) //
    {
        pwron_cnt++;
        return;
    }

    if ((pole_pair_number != 5) && (pole_pair_number != 8))
    {
        Flash_Rd_Data(tmp_buf, 8);

        if (1 == tmp_buf[0])
        {
            PMSMFLT.FLT_DCDC_OVER_BosstIin = 0;
            m_motor_vendor = 1;
            pole_pair_number = 5;
            VirtualSpeedSensorM1._Super.bElToMecRatio = 5;
            STO_PLL_M1._Super.bElToMecRatio = 5;
            m_pole_pair_num = 5;
            rt_kprintf(" motor pole pair num is 5 ***********************************\r\n");
        }
        else if (2 == tmp_buf[0])
        {
            PMSMFLT.FLT_DCDC_OVER_BosstIin = 0;
            m_motor_vendor = 2;
            pole_pair_number = 8;
            VirtualSpeedSensorM1._Super.bElToMecRatio = 8;
            STO_PLL_M1._Super.bElToMecRatio = 8;
            m_pole_pair_num = 8;
            rt_kprintf(" motor pole pair num is 8 ***********************************\r\n");
        }
        else
        {
            PMSMFLT.FLT_DCDC_OVER_BosstIin = 1;
            rt_kprintf(" motor pole pair num is unkonw***********************************\r\n");
            rt_thread_delay(RT_TICK_PER_SECOND * 2);

            PMSMFLT.FLT_DCDC_OVER_BosstIin = 0;
            m_motor_vendor = 1;
            pole_pair_number = 5;
            VirtualSpeedSensorM1._Super.bElToMecRatio = 5;
            STO_PLL_M1._Super.bElToMecRatio = 5;
            m_pole_pair_num = 5;
            rt_kprintf(" motor pole pair num default is 5 ***********************************\r\n");

            return;
        }
    }

    if (PMSMST.CMD_MC_Stop == 1) //////////////////////////////////////////////////////
    {
        mc_start_cnt = 0;
        if (mc_stop_cnt < 20) //200ms
        {
            mc_stop_cnt++;
        }
        else
        {
            mc_start_cmd = 0;
            mc_stop_cmd = 1;
        }
    }
    else
    {
        mc_stop_cnt = 0;
        if (mc_start_cnt < 100) //1s
        {
            mc_start_cnt++;
        }
        else
        {
            mc_start_cmd = 1;
            mc_stop_cmd = 0;
        }
    }


    PMSMST.InputVoltage = VBS_GetAvBusVoltage_V(pMCT[M1]->pBusVoltageSensor);
    PMSMST.Temperature  = NTC_GetAvTemp_C(pMCT[M1]->pTemperatureSensor);
    PMSMST.MotorPower   = MPM_GetAvrgElMotorPowerW(pMCT[M1]->pMPM);
    PMSMST.MotorSpeed   = (MCI_GetAvrgMecSpeedUnit(pMCI[M1]) * _RPM) / SPEED_UNIT;
    PMSMST.OutIpamp     = MC_GetPhaseCurrentAmplitudeMotor1() / 5;
    PMSMST.OutVoltage   = MC_GetPhaseVoltageAmplitudeMotor1() * 111 / 10000;

    PMSMST.Ia = PMSMST.OutIpamp;
    PMSMST.Ib = PMSMST.OutIpamp;
    PMSMST.Ic = PMSMST.OutIpamp;

    if (!MC_FORCE_SET_FLAG)
    {
        PMSMST.ControlNewSpeed = get_ctrl_speed(mc_stop_cmd);
    }

    PMSMST.OccurredFault = MC_GetOccurredFaultsMotor1();
    PMSMST.NowFault = MC_GetCurrentFaultsMotor1();
    PMSMST.MCState  = MC_GetSTMStateMotor1();
    Iab_value = MC_GetIabMotor1();

    if ((Iab_value.a == 0) && (Iab_value.b == 0))
    {
        MotorPhaseIa.gsEffectiveValue = 0;
        MotorPhaseIb.gsEffectiveValue = 0;
        MotorPhaseIc.gsEffectiveValue = 0;
    }

    Iqd_value = MC_GetIqdMotor1();////////////////////////////////////////////////////////
    PMSMST.Id = Iqd_value.d;
    PMSMST.Iq = Iqd_value.q;

    Iqd_value = MC_GetIqdrefMotor1();
    PMSMST.Iqref = Iqd_value.q;
    PMSMST.Idref = Iqd_value.d;////////////////////////////////////////////////////////


    if ((PMSMST.MCState == FAULT_NOW) || (PMSMST.MCState == FAULT_OVER))
        PMSMST.ControlState = Mc_Err;

    switch (PMSMST.ControlState)
    {
    // ϵͳ����
    case Mc_Idle:

        //if (abs(PMSMST.ControlNewSpeed) > MINSPEED)////////////////////
        if ((abs(PMSMST.ControlNewSpeed) > MINSPEED) && (mc_start_cmd == 1))
        {
            if (PMSMST.ControlNewSpeed > 0) PMSMST.ControlSpeed = MINSPEED;
            else PMSMST.ControlSpeed = -MINSPEED;

            MC_ProgramSpeedRampMotor1((PMSMST.ControlSpeed / 6), 1000);

            MC_StartMotor1();
            PMSMST.MCworktime_cnt = 0;
            PMSMST.ControlState = Mc_Start;
        }

        break;

    case Mc_Start:

        if (mc_stop_cmd == 1) //////////////////////////////////////////////
        {
            PMSMST.ControlState = Mc_Stop;
        }
        else if (PMSMST.MCState == RUN)
        {
            PMSMST.MCstart_cnt++;

            PMSMST.ControlState = Mc_Run;
        }

        break;

    case Mc_Run:

        PMSMST.MCworktime_cnt++;

        if ((abs(PMSMST.ControlNewSpeed) < (MINSPEED * 8 / 10)) || (mc_stop_cmd == 1)) //|| (PMSMST.MotorSpeed < 0))
        {
            MC_StopMotor1();
            PMSMST.ControlSpeed = 0;
            ctrlspeed = 0;
            Ramptime = 0;
            PMSMST.ControlState = Mc_Stop;
        }
        else
        {
            if (abs(PMSMST.ControlNewSpeed) < MINSPEED)  //������?630;
            {
                ctrlspeed = (PMSMST.ControlNewSpeed > 0) ? MINSPEED : -MINSPEED;
            }
            else
            {
                ctrlspeed = PMSMST.ControlNewSpeed;
            }

            if (ctrlspeed != PMSMST.ControlSpeed)
            {
                if (abs(PMSMST.ControlSpeed - ctrlspeed) > 50) //50
                {
                    Ramptime = (abs(PMSMST.ControlSpeed - ctrlspeed)) * 8000 / MAXSPEED;
                    if (Ramptime < 1000) Ramptime = 1000;
                    //rt_kprintf("\r\n ctsp = %d; newsp = %d; rt = %d;",ctrlspeed,PMSMST.ControlSpeed,Ramptime);
                    PMSMST.ControlSpeed = ctrlspeed;
                    update_cnt = 0;
                    MC_ProgramSpeedRampMotor1((PMSMST.ControlSpeed / 6), Ramptime);
                    //rt_kprintf("\r\n run: SP = %d; CNP = %d; CP = %d",PMSMST.MotorSpeed,PMSMST.ControlNewSpeed,PMSMST.ControlSpeed);
                }
                else
                {
                    if (update_cnt < 10) //5S, 500
                    {
                        update_cnt++;
                    }
                    else
                    {
                        PMSMST.ControlSpeed = ctrlspeed;
                        update_cnt = 0;
                        MC_ProgramSpeedRampMotor1((PMSMST.ControlSpeed / 6), 1000);
                        //rt_kprintf("\r\n SP = %d; CNP = %d; CP = %d",PMSMST.MotorSpeed,PMSMST.ControlNewSpeed,PMSMST.ControlSpeed);
                    }
                }
            }
        }
        break;

    case Mc_Stop:
        if (PMSMST.MCState == IDLE)
        {
            PMSMST.ControlState = Mc_Idle;
        }
        else if (PMSMST.MCState != STOP)//////////////////////////////////////////////
        {
            MC_StopMotor1();
        }
        PMSMST.MCworktime_cnt = 0;

        break;

    case Mc_Err:

        PMSMST.MCworktime_cnt = 0;
        if (mcerrdelay_cnt > 200) //���д����ӳ�2��
        {
            if (MC_AcknowledgeFaultMotor1() == true)
            {
                PMSMST.ControlState = Mc_Idle;
                PMSMST.MCerr_cnt++;
                mcerrdelay_cnt = 0;
            }
        }
        else
        {
            mcerrdelay_cnt++;
        }

        break;

    default:

        break;

    }
#endif
}

/*
    case MC_PROTOCOL_REG_MIN_APP_SPEED:
      {
        bRetVal = STC_GetMinAppNegativeMecSpeed01Hz(pMCT->pSpeednTorqueCtrl) * 6;
      }
      break;

    case MC_PROTOCOL_REG_RAMP_FINAL_SPEED:
    {
      if (MCI_GetControlMode(pMCI) == STC_SPEED_MODE)
      {
        bRetVal = (int32_t)(MCI_GetLastRampFinalSpeed(pMCI) * 6);
      }
      else
      {
        bRetVal = (int32_t)(MCI_GetMecSpeedRef01Hz(pMCI) * 6);
      }
    }
    break;

   case MC_PROTOCOL_CMD_STOP_RAMP:
    {
      if (MCI_GetSTMState(pMCI) == RUN)
      {
        MCI_StopSpeedRamp(pMCI);
      }
    }
    break;
  case MC_PROTOCOL_CMD_START_STOP:
    {
      if (MCI_GetSTMState(pMCI) == IDLE)
      {
        MCI_StartMotor(pMCI);
      }
      else
      {
        MCI_StopMotor(pMCI);
      }
    }
    break;
  case MC_PROTOCOL_CMD_FAULT_ACK:
    {
      MCI_FaultAcknowledged(pMCI);
    }
    break;


bool UI_ExecSpeedRamp(UI_Handle_t *pHandle, int32_t wFinalMecSpeedRPM, uint16_t hDurationms)
{

  MCI_Handle_t * pMCI = pHandle->pMCI[pHandle->bSelectedDrive];

//  Call MCI Exec Ramp
  MCI_ExecSpeedRamp(pMCI,(int16_t)(wFinalMecSpeedRPM/6),hDurationms);
  return true;
}
*/

////////////////////////////////////////////////////////////////////
//////////////Finsh Msh CMD
////////////////////////////////////////////////////////////////////
#ifdef RT_USING_FINSH
#include "finsh.h"
void cmd_setrpm(int argc, char **argv)
{
    uint16_t _rpm = 0;
    if (argc >= 2)
    {
        _rpm = atoi(argv[1]);
        if (_rpm == 0)
        {
            PMSMST.ControlNewSpeed  = 0;
            MC_FORCE_SET_FLAG = 0;
            rt_kprintf(" Set 0 to Exit Force set motor rpm \r\n");
        }
        else
        {
            _rpm = (_rpm < MINSPEED) ? MINSPEED : _rpm;
            _rpm = (_rpm >= MAXSPEED) ? MAXSPEED : _rpm;
            PMSMST.ControlNewSpeed = _rpm;
            MC_FORCE_SET_FLAG = 1;
            rt_kprintf(" Force set motor rpm to :%d \r\n", PMSMST.ControlNewSpeed);
        }
    }
    else rt_kprintf(" \r\n eg: setrpm rpm  note:rpm[0:stop 1500:max]\r\n");
}

MSH_CMD_EXPORT_ALIAS(cmd_setrpm, setrpm, set motor rpm);


void cmd_list_mem_info(int argc, char **argv)
{
    rt_uint32_t  mem_total, mem_used, max_used;

    rt_memory_info(&mem_total, &mem_used, &max_used);
    rt_kprintf("total memory: %d\n", mem_total / 1024);
    rt_kprintf("used memory : %d\n", mem_used / 1024);
    rt_kprintf("maximum allocated memory: %d\n", max_used / 1024);

}

MSH_CMD_EXPORT_ALIAS(cmd_list_mem_info, list_mem_info, list mem info);


extern long list_thread(void);
void cmd_user_list_thread(int argc, char **argv)
{
    list_thread();
}

MSH_CMD_EXPORT_ALIAS(cmd_user_list_thread, user_list_thread, list thread info);


#endif

/******************* (C) COPYRIGHT 2021 Group *****END OF FILE****/
