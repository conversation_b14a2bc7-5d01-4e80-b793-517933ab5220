
/***************************************************************************

Copyright (C), 2022-2022, BSM Tech. Co., Ltd.

* @file           pfc_dqctrl.h
* <AUTHOR> @version        V0.0.1
* @date           2022-09-06
* @brief

History:          // Revision Records

       <Author>             <time>         <version >               <desc>



***************************************************************************/

#ifndef _PFC_DQCTRL_H_
#define _PFC_DQCTRL_H_

#include "stdint.h"


//*********** Structure Definition ********//
typedef struct
{
    float a;
    float b;
    float c;
    float alpha;
    float beta;
    float zero;
    float sinth;
    float costh;
    float d;
    float q;
    float z;
    float dfilter;
    float qfilter;
} tstCLARKEPARK;

typedef struct
{
    float alpha;
    float beta;
    float zero;
    float sinth;
    float costh;
    float d;
    float q;
    float z;
}
tstiPARK;

typedef struct
{
    float a;
    float b;
    float c;
    float alpha;
    float beta;
    float zero;
}
tstiCLARKE;

typedef struct
{
    float  Ref;
    float  Fbk;
    float  Out;
    float  Kp;
    float  Ki;
    float  Umax;
    float  Umin;
    float  up;
    float  ui;
    float  v1;
    float  i1;
}
tstPI;

typedef struct
{
    struct
    {
        float osg_k;
        float osg_x;
        float osg_y;
        float osg_b0;
        float osg_b2;
        float osg_a1;
        float osg_a2;
        float osg_qb0;
        float osg_qb1;
        float osg_qb2;
        float osgx;
        float osgy;
        float temp;
    }
    OSG;
    struct
    {
        float B1_lf;
        float B0_lf;
        float A1_lf;
    }
    LPF;
    struct
    {
        float Uin0;  // Ac Input
        float Uin1;  // Ac Input
        float Uin2;  // Ac Input
        float   d0;
        float   d1;
        float   d2;
        float   q0;
        float   q1;
        float   q2;
        float   Q0;
        float   Q1;
        float   D0;
        float   D1;
        float   ylf0;
        float   ylf1;
        float   fo; // output frequency of PLL
        float   fn; //nominal frequency
        float theta0;
        float theta1;
        float cost;
        float sint;
        float   delta_T;
    }
    VAR;
    struct
    {
        float ef2;
        float gamma;
        float x1;
        float x2;
        float x0;
        float wd;
    }
    FLL;
    struct
    {
        union
        {
            unsigned short all;
            struct
            {
                unsigned short Fin0  : 1;
                unsigned short Bin0  : 1;
                unsigned short Fin1  : 1;
                unsigned short Bin1  : 1;
                unsigned short Fflg  : 1;
                unsigned short Bflg  : 1;
                signed short sign  : 2;
            }
            bit;
        }
        Sta;
        unsigned short HFcnt;
        unsigned short LFcnt;
        unsigned short HBcnt;
        unsigned short LBcnt;

        enum
        {
            DeterVacP = 0,
            WaitVacN,
            DeterVacN,
            WaitVacP
        }
        Pro;
    }
    PD;
}
tstDPLL1P;

typedef struct
{
    float B1_lf;
    float B0_lf;
    float A1_lf;
}
SPLL3phDDSRFLPFCOEFF;

typedef struct
{

    float dpde;
    float dnde;
    float qpde;
    float qnde;

    float y[2];
    float x[2];
    float w[2];
    float z[2];
    float k1;
    float k2;
    float dpdelpf;
    float dndelpf;
    float qpdelpf;
    float qndelpf;

    float vqpre;
    float theta;
    float thetaoffset;
    float thetaoutput;
//    float thetaoffsetq;
//    float thetaoutputq;
    float thetaTA;//Transform coordinate axis
    float lfo;
    float fo;
    float fn;
    float fo_lpf;
    float delta_T;
    SPLL3phDDSRFLPFCOEFF lpf_coeff;
    char  freq_stableflag;
}
TstSPLL3phDDSRF;

typedef struct
{
    float alpha;
    float beta;
    float z;
    float dp;
    float qp;
    float dn;
    float qn;

    float d;
    float q;
    float d_lpf;
    float q_lpf;
}
TstABCDQ0PN;
typedef struct
{
    float cos_2theta;
    float sin_2theta;
    float cos_theta;
    float sin_theta;
    float cos_theta_offset;
    float sin_theta_offset;
    float cos_thetaaux;
    float sin_thetaaux;
//    float cos_theta_offsetq;
//    float sin_theta_offsetq;
}
TstAngleCal;
typedef struct
{
    float a;
    float b;
    float c;
}
Tst3PHASE;
typedef struct
{
    Tst3PHASE In;
    Tst3PHASE Offset;
    Tst3PHASE actPP;
    float PP;
    float PN;

    unsigned short cnt;
    unsigned short clearcnt;
}
Tst3PHASEALL;
typedef struct
{
    float Vdcref;
    float UserVdcref;
    float f;
    float L;
    float wL;
    float C;
    float Fs;
    float Ts;
    unsigned long Tpwm;
}
tstSysPar;

typedef struct
{
    tstSysPar SysPar;
    Tst3PHASEALL VLac;
    Tst3PHASEALL Vac;
    Tst3PHASEALL Iac;
    Tst3PHASEALL Iout;
    struct
    {
        float In;
        float Fil;
        float actPP;
        float PP;
        float PN;

        unsigned short cnt;
        unsigned short clearcnt;
    }
    Vbus;
    TstABCDQ0PN VabcVdq0PN;
    TstABCDQ0PN IabIdq;
    TstSPLL3phDDSRF DPLLDDSRF;
    TstAngleCal AngleCal;
    char FlagCalibEnd;
    
    float abs_Va;
    float abs_Vb;
    float abs_Vc;
}
tstDPLL3P;

typedef struct
{
    struct
    {
        float B2_notch;
        float B1_notch;
        float B0_notch;
        float A2_notch;
        float A1_notch;
        float temp1;
        float temp2;
        float temp3;
    }
    COE;
    struct
    {
        float Out1;
        float Out2;
        float In;
        float In1;
        float In2;
        float Out;
    }
    VAR;
}
tstNOTCHFil;

typedef struct
{
    float wrc;
    float krc;
    float kpc;
}
tstPRpara;

typedef struct
{
    float b0[2];   //!< b0
    float b1[2];   //!< b1
    float b2[2];   //!< b2
    float a1[2];   //!< a1
    float a2[2];   //!< a2
    unsigned short flg;
}
tstPRcoeff;

typedef struct
{
    float err0;   //!< b0
    float err1;   //!< b1
    float err2;   //!< b2
    float out0;   //!< a1
    float out1;   //!< a2
    float out2;   //!< x1
}
tstPRvar;

typedef struct
{
    float Irefold;
    float L;
    float Uout;
    float Vbus;
    float Upwm;

    tstCLARKEPARK IFB;
    tstCLARKEPARK Vout;
    struct
    {
        float txyz[6];
        float ton[3];

        float t1;
        float t2;
        float U1;
        float U2;
        float U3;
        float K;

        unsigned long CntPhA;
        unsigned long CntPhB;
        unsigned long CntPhC;

        unsigned short Sector;
        unsigned short CMP[3][7];
        unsigned short t12[2][7];
        unsigned short SN[7];

    }
    SVPWM;

    tstPI PIId;
    tstPI PIIq;
    tstPI PIVbus;

    tstPRcoeff PR6coeff;
    tstPRpara PR6para;
    tstPRvar PR6d;
    tstPRvar PR6q;

    tstPRcoeff PR12coeff;
    tstPRpara PR12para;
    tstPRvar PR12d;
    tstPRvar PR12q;
    
    tstPRcoeff PR18coeff;
    tstPRpara PR18para;
    tstPRvar PR18d;
    tstPRvar PR18q;
    
    float Voutd_coeff;
    float Voutq_coeff;
}
tstCONTROLVAR;

extern tstCONTROLVAR PFCVAR;
void InitDqctrl(void);


#define PICONST (float)3.1415926535897932384626433832795



//*********** Function Declarations *******//
float __fsat(float val, float max, float min);
void computeDF22_PRcontrollerCoeff(tstPRcoeff *v, float kp, float ki,
                                   float wo, float fs, float wrc);
void pfc_qdctrl(void);
void High_ErrCheck(void);
void High_ErrFastCheck(void);
#endif /* DQCONTROL_H_ */
