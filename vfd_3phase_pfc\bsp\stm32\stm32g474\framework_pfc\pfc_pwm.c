
/***************************************************************************

Copyright (C), 2022-2022, BSM Tech. Co., Ltd.

* @file           pfc_pwm.c
* <AUTHOR> @version        V0.0.1
* @date           2022-09-06
* @brief          this is PFC PWM

History:          // Revision Records

       <Author>             <time>         <version >               <desc>



***************************************************************************/


#include "pfc_pwm.h"
#include "pfc_sdpll.h"
#include "pfc_dqctrl.h"
#include "uapp.h"
#include "app_ad.h"
extern TIM_HandleTypeDef htim8;
extern ADC_HandleTypeDef hadc1;

/* Private defines -----------------------------------------------------------*/
#define TIMxCCER_MASK_CH123        ((uint16_t)  (LL_TIM_CHANNEL_CH1|LL_TIM_CHANNEL_CH1N|\
                                                 LL_TIM_CHANNEL_CH2|LL_TIM_CHANNEL_CH2N|\
                                                 LL_TIM_CHANNEL_CH3|LL_TIM_CHANNEL_CH3N))



void Pfc_PWMInit(void)
{
//    if(vfd.ctrl.tim8_mode != TIM8_MODE_PFC)
//        return;
    
    /* TIM8 Counter Clock stopped when the core is halted */
    LL_DBGMCU_APB2_GRP1_FreezePeriph(LL_DBGMCU_APB2_GRP1_TIM8_STOP);

    uint32_t Brk2Timeout = 1000;

    /* disable main TIM counter to ensure*/
    LL_TIM_DisableCounter(TIM8);

    /* Enables the TIMx Preload on CC1 Register */
    LL_TIM_OC_EnablePreload(TIM8, LL_TIM_CHANNEL_CH1);
    /* Enables the TIMx Preload on CC2 Register */
    LL_TIM_OC_EnablePreload(TIM8, LL_TIM_CHANNEL_CH2);
    /* Enables the TIMx Preload on CC3 Register */
    LL_TIM_OC_EnablePreload(TIM8, LL_TIM_CHANNEL_CH3);
    /* Enables the TIMx Preload on CC4 Register */
    LL_TIM_OC_EnablePreload(TIM8, LL_TIM_CHANNEL_CH4);
    /* Prepare timer for synchronization */
    LL_TIM_GenerateEvent_UPDATE(TIM8);

    while ((LL_TIM_IsActiveFlag_BRK2(TIM8) == 1u) && (Brk2Timeout != 0u))
    {
        LL_TIM_ClearFlag_BRK2(TIM8);
        Brk2Timeout--;
    }

}


void PfcEnPwm(void)
{
//    if(vfd.ctrl.tim8_mode != TIM8_MODE_PFC)
//        return;
    
    HAL_TIM_Base_Start(&htim8);
    LL_TIM_EnableIT_BRK(TIM8);
    /** @note 
    Bit 15 MOE: Main output enable
    This bit is cleared asynchronously by hardware as soon as one of the break inputs is active 
    (tim_brk or tim_brk2). It is set by software or automatically depending on the AOE bit. It is 
    acting only on the channels which are configured in output. 
    0:In response to a break 2 event. OC and OCN outputs are disabled
    In response to a break event or if MOE is written to 0: OC and OCN outputs are disabled or 
    forced to idle state depending on the OSSI bit.
    1:OC and OCN outputs are enabled if their respective enable bits are set (CCxE, CCxNE in 
    TIMx_CCER register).
    See OC/OCN enable description for more details (Section 28.6.11: TIMx capture/compare 
    enable register (TIMx_CCER)(x = 1, 8, 20)).
    */
    __HAL_TIM_MOE_ENABLE(&htim8);
    /* Enable PWM channel */
    LL_TIM_CC_EnableChannel(TIM8, TIMxCCER_MASK_CH123);
}

void PfcDisPwm(void)
{
//    if(vfd.ctrl.tim8_mode != TIM8_MODE_PFC)
//        return;
    
    LL_TIM_DisableIT_BRK(TIM8);
    HAL_TIM_Base_Stop(&htim8);
    /* It forces inactive level on TIMx CHy and CHyN */
    LL_TIM_CC_DisableChannel(TIM8, TIMxCCER_MASK_CH123);

    LL_TIM_OC_SetCompareCH1(TIM8, TIM8->ARR >> 1);
    LL_TIM_OC_SetCompareCH2(TIM8, TIM8->ARR >> 1);
    LL_TIM_OC_SetCompareCH3(TIM8, TIM8->ARR >> 1);
    
}

uint32_t pwm_cnt = 0;
extern uint16_t pfc_adc_value[8];
uint32_t dac1_chnn1_reg = 0;
uint32_t dac1_chnn2_reg = 0;
int COMP  = 0;
static int CompR = 0;
static int CompS = 0;
static int CompT = 0;
void pfc_pwmctrl(void)
{
    static uint32_t tick;
    tick++;
    PFCVAR.SVPWM.U1 =    PFCVAR.Vout.beta;
    PFCVAR.SVPWM.U2 = (1.732050807568877f * PFCVAR.Vout.alpha - PFCVAR.Vout.beta) * 0.5f;
    PFCVAR.SVPWM.U3 = (-1.732050807568877f * PFCVAR.Vout.alpha - PFCVAR.Vout.beta) * 0.5f;
    PFCVAR.SVPWM.Sector = PFCVAR.SVPWM.SN
                          [
                              (PFCVAR.SVPWM.U1 > 0)
                              + 2 * (PFCVAR.SVPWM.U2 > 0)
                              + 4 * (PFCVAR.SVPWM.U3 > 0)
                          ];

    PFCVAR.SVPWM.K = 1.732050807568877f * DPLL3P.SysPar.Tpwm * 2.0f / DPLL3P.Vbus.In;

    PFCVAR.SVPWM.txyz[0] = PFCVAR.SVPWM.U1 * PFCVAR.SVPWM.K;
    PFCVAR.SVPWM.txyz[1] = PFCVAR.SVPWM.U2 * PFCVAR.SVPWM.K;
    PFCVAR.SVPWM.txyz[2] = PFCVAR.SVPWM.U3 * PFCVAR.SVPWM.K;
    PFCVAR.SVPWM.txyz[3] = -PFCVAR.SVPWM.txyz[0];
    PFCVAR.SVPWM.txyz[4] = -PFCVAR.SVPWM.txyz[1];
    PFCVAR.SVPWM.txyz[5] = -PFCVAR.SVPWM.txyz[2];

    PFCVAR.SVPWM.t1 = PFCVAR.SVPWM.txyz[PFCVAR.SVPWM.t12[0][PFCVAR.SVPWM.Sector]];
    PFCVAR.SVPWM.t2 = PFCVAR.SVPWM.txyz[PFCVAR.SVPWM.t12[1][PFCVAR.SVPWM.Sector]];

    float t1 = PFCVAR.SVPWM.t1;
    float t2 = PFCVAR.SVPWM.t2;
    if ((t1 + t2) > (DPLL3P.SysPar.Tpwm * 2.0f))
    {
        PFCVAR.SVPWM.t1 = t1 * (DPLL3P.SysPar.Tpwm * 2.0f) / (t1 + t2);
        PFCVAR.SVPWM.t2 = t2 * (DPLL3P.SysPar.Tpwm * 2.0f) / (t1 + t2);
    }

    PFCVAR.SVPWM.ton[0] = ((DPLL3P.SysPar.Tpwm * 2.0f) - PFCVAR.SVPWM.t1 - PFCVAR.SVPWM.t2) * 0.25f;
    if (PFCVAR.SVPWM.ton[0] < 0)
        PFCVAR.SVPWM.ton[0] = 0;
    PFCVAR.SVPWM.ton[1] = PFCVAR.SVPWM.ton[0] + PFCVAR.SVPWM.t1 * 0.5f;
    PFCVAR.SVPWM.ton[2] = PFCVAR.SVPWM.ton[1] + PFCVAR.SVPWM.t2 * 0.5f;

    PFCVAR.SVPWM.CntPhA = PFCVAR.SVPWM.ton[PFCVAR.SVPWM.CMP[0][PFCVAR.SVPWM.Sector]] ; 
    PFCVAR.SVPWM.CntPhB = PFCVAR.SVPWM.ton[PFCVAR.SVPWM.CMP[1][PFCVAR.SVPWM.Sector]] ; 
    PFCVAR.SVPWM.CntPhC = PFCVAR.SVPWM.ton[PFCVAR.SVPWM.CMP[2][PFCVAR.SVPWM.Sector]] ; 
    
    if (pfc.PFC_Runing == 1)
    {
        #if 0 //for test pwm 
        PFCVAR.SVPWM.CntPhA = 0;
        PFCVAR.SVPWM.CntPhB = 0;
        PFCVAR.SVPWM.CntPhC = 0 ;
        #endif
        /** @note clear first pha */
        if(pfc.PFC_PreRuning == 0)
        {
            pwm_cnt = 0;
        }
        
        pwm_cnt++;
        
        #ifdef DEBUG_PFC_USE_LOG
        if(pfc.PFC_PreRuning == 0)
        {
            vfd.debug_maxpwm[0] = 0;
            vfd.debug_maxpwm[1] = 0;
            vfd.debug_maxpwm[2] = 0;
            vfd.debug_pwmcnt = 0;
            vfd.debug_picnt  = 0;
            
        }
        
        vfd.debug_maxpwm[0] = (vfd.debug_maxpwm[0] > PFCVAR.SVPWM.CntPhA) ? vfd.debug_maxpwm[0] : PFCVAR.SVPWM.CntPhA;
        vfd.debug_maxpwm[1] = (vfd.debug_maxpwm[1] > PFCVAR.SVPWM.CntPhB) ? vfd.debug_maxpwm[1] : PFCVAR.SVPWM.CntPhB;
        vfd.debug_maxpwm[2] = (vfd.debug_maxpwm[2] > PFCVAR.SVPWM.CntPhC) ? vfd.debug_maxpwm[2] : PFCVAR.SVPWM.CntPhC;
        vfd.debug_pwmcnt++;  
        
        #endif
    }
        
    {
        LL_TIM_OC_SetCompareCH1(TIM8, PFCVAR.SVPWM.CntPhA);
        LL_TIM_OC_SetCompareCH2(TIM8, PFCVAR.SVPWM.CntPhB);
        LL_TIM_OC_SetCompareCH3(TIM8, PFCVAR.SVPWM.CntPhC); 
    }
    
    extern DAC_HandleTypeDef hdac1;
    #include "uaInverter.h"
    uint16_t temp ;

    temp = DPLL3P.Iac.In.a * 0.1428f * 4096 ;
    
    HAL_DAC_SetValue(&hdac1,DAC_CHANNEL_1,DAC_ALIGN_12B_R,temp);
    
    temp = DPLL3P.Iac.In.a * 0.1428f * 4096 ;
    HAL_DAC_SetValue(&hdac1,DAC_CHANNEL_2,DAC_ALIGN_12B_R,temp);

}



////////////////////////////////////////////////////////////////////
//////////////Finsh Msh CMD
////////////////////////////////////////////////////////////////////
#ifdef RT_USING_FINSH
#include "finsh.h"


#endif